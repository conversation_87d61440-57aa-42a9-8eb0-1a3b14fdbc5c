#!/usr/bin/env bash

set -e

# Configuration
ROOT_DIR="$(pwd)"
OUTPUT_DIR="${ROOT_DIR}/build"

# Build binaries
# shellcheck disable=SC2043
for OS in linux; do
    for ARCH in amd64; do
        echo "Building binary for $OS/$ARCH..."
        BUILD_DIR="${OUTPUT_DIR}/credit-autocancel-service-${ARCH}-${OS}-${VERSION}"

        # Build go binary
        GOARCH=${ARCH} GOOS=${OS} BUILD_DIR="${BUILD_DIR}" VERSION="${VERSION}" make deps-build
    done
done

# archive artifacts
ARCHIVE_DIR="${ROOT_DIR}/archive"
mkdir -p "${ARCHIVE_DIR}"

pushd "${OUTPUT_DIR}"
for i in ./*; do
    RELEASE=$(basename "${i}")

    echo "Packing binary for ${RELEASE}..."
    tar -czf "${ARCHIVE_DIR}/${RELEASE}.tar.gz" "${RELEASE}"
done
popd
