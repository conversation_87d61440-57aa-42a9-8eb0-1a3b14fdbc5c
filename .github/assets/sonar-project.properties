sonar.exclusions=.github/**,ci/**,**/*_test.go,**/vendor/**,**/testdata/*,**/*.yaml,**/*.yml,**/*.sql,features/bootstrap/*,**/*_mock.go,integration_tests/**
sonar.test.inclusions="**/*_test.go"
sonar.test.exclusions="**/vendor/**"
sonar.projectKey=com.hellofresh:credit-autocancel-service
sonar.links.homepage=https://github.com/hellofresh/credit-autocancel-service
sonar.links.issue=https://github.com/hellofresh/credit-autocancel-service/issues
sonar.links.ci=https://github.com/hellofresh/credit-autocancel-service/actions
sonar.links.scm=https://github.com/hellofresh/credit-autocancel-service.git
sonar.scm.provider=git
sonar.sourceEncoding=UTF-8
