version = 1
# see https://kodiakhq.com/docs/config-reference

#[merge]
## support type: string or string[]
#automerge_label = "security vulnerability"
#
#[merge.automerge_dependencies]
## auto merge all PRs opened by "dependabot" that are "minor" or "patch" version upgrades. "major" version upgrades will be ignored.
#versions = ["minor", "patch"]
#usernames = ["dependabot"]

[approve]
# note: remove the "[bot]" suffix from GitHub Bot usernames.
# Instead of "dependabot[bot]" use "dependabot".
auto_approve_usernames = ["dependabot"]

# if using `update.always`, add dependabot to `update.ignore_usernames` to allow
# dependabot to update and close stale dependency upgrades.
[update]
ignored_usernames = ["dependabot"]
