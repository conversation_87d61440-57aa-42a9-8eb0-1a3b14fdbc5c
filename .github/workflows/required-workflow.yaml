# This workflow is centrally managed and generated from
# https://github.com/hellofresh/github-automation/blob/master/modules/repository/required-workflows/required-workflow-wrapper.yaml.tmpl
---
name: "PR: Company Required Workflows"

on:
  pull_request:
    types:
      - opened
      - reopened
      - synchronize
      - labeled
      - unlabeled

    branches:
      - master

permissions:
  contents: read
  pull-requests: write
  id-token: write
  
jobs:

  check-repository-ownership:
    name: Check repository ownership
    uses: hellofresh/workflow-validate-repository-ownership/.github/workflows/reusable.yml@master

  check-terraform-ownership:
    name: Check terraform ownership
    uses: hellofresh/workflow-tf-ownership-check/.github/workflows/reusable.yml@master

  check-tribe-squad-labels:
    name: Check tribe squad labels
    uses: hellofresh/workflow-validate-tribe-squad-labels/.github/workflows/reusable.yml@master

  orc-gating:
    name: ORC Gating
    uses: hellofresh/workflow-orc-gate/.github/workflows/reusable.yml@master

  peak-deployment-control-black-days:
    name: Peak Deployment Control Black Days
    uses: hellofresh/workflow-peak-deployment-control/.github/workflows/reusable.yml@master

  peak-deployment-control-gray-days:
    name: Peak Deployment Control Gray Days
    uses: hellofresh/workflow-peak-deployment-control-grey/.github/workflows/reusable.yml@master

  release-engineering-checks:
    name: Release Engineering Checks
    uses: hellofresh/workflow-release-engineering-checks/.github/workflows/reusable.yml@master

  required-workflows-status:
    name: Required workflows status
    if: always()
    runs-on: ubuntu-latest
    needs: [check-repository-ownership,check-terraform-ownership,check-tribe-squad-labels,orc-gating,peak-deployment-control-black-days,peak-deployment-control-gray-days,release-engineering-checks]
    steps:
      - if: |
          (needs.check-repository-ownership.result != 'success') ||
          (needs.check-terraform-ownership.result != 'success') ||
          (needs.check-tribe-squad-labels.result != 'success') ||
          (needs.orc-gating.result != 'success') ||
          (needs.peak-deployment-control-black-days.result != 'success') ||
          (needs.peak-deployment-control-gray-days.result != 'success') ||
          (needs.release-engineering-checks.result != 'success') 

        run: exit 1
