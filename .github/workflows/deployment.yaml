# This workflow is used to deploy the application within ftcp.
# Most notably, there aren't two separate workflows for staging and prod anymore, but this pipeline creating the flow.
name: Deploy Pipeline

on:
  release:
    types:
      - published

concurrency: deploy

jobs:
  deploy-stage:
    name: Deploy Stage
    uses: hellofresh/jetstream-ci-scripts/.github/workflows/reusable-deployment.yaml@master
    permissions:
      contents: write
      id-token: write
      deployments: write
    with:
      environment: "stage"
      version: "${{ github.event.release.tag_name }}"
      terraform_enabled: false

