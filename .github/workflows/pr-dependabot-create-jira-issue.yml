---
name: "PR - Dependabot - Create JIRA Issue"

on:
  workflow_dispatch:
  pull_request:
    branches:
      - master
    types:
      - opened

permissions: write-all

jobs:
  dependabot-create-jira-issue:
    name: 'Dependabot - Create Jira Issue'
    runs-on: [ self-hosted, default ]
    if: ${{ github.actor == 'dependabot[bot]' }} # run create ticket only if author is dependabot

    steps:
      - name: Import Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          secrets: |
            common/key-value/data/ci JIRA_BASE_URL | JIRA_BASE_URL ;
            common/key-value/data/ci JIRA_EMAIL | JIRA_EMAIL ;
            common/key-value/data/ci JIRA_TOKEN | JIRA_TOKEN ;
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN ;

      - name: Checkout source code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # fetch full history
          ref: master

      - name: Login to <PERSON><PERSON>
        uses: atlassian/gajira-login@master
        env:
          JIR<PERSON>_BASE_URL: ${{ env.JIRA_BASE_URL }}
          JIRA_USER_EMAIL: ${{ env.JIRA_EMAIL }}
          JIRA_API_TOKEN: ${{ env.JIRA_TOKEN }}

      - name: Create Jira Ticket
        id: create
        uses: atlassian/gajira-create@master
        with:
          project: RTESYS
          issuetype: Task
          summary: |
            [${{ github.event.repository.name }}] ${{ github.event.pull_request.title }}
          description: Dependabot PR ${{ github.event.pull_request.number }} ${{ github.event.pull_request.html_url }}

      - name: Transition issue
        uses: atlassian/gajira-transition@v3
        with:
          issue: ${{ steps.create.outputs.issue }}
          transition: 'Selected for Development'

      - name: Update PR name
        env:
          pr_title: ${{ github.event.pull_request.title }}
          pr_body: ${{ github.event.pull_request.body }}
          pr_url: ${{github.event.pull_request.url}}
          pr_number: ${{ github.event.pull_request.number }}
          pr_jira_ticket: ${{ steps.create.outputs.issue }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          title="[$pr_jira_ticket] $pr_title"
          gh pr edit "$pr_number" --title "$title" --body 'https://hellofresh.atlassian.net/browse/'"$pr_jira_ticket"'

          '"$pr_body"
