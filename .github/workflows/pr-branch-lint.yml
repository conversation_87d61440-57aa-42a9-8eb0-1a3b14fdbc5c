---
name: "PR: Lint"

concurrency:
  group: pr-lint-${{ github.head_ref }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - master

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;

      - name: Checkout source code
        uses: actions/checkout@v3
        with:
          # Full git history is needed to get a proper list of changed files within `super-linter`
          fetch-depth: 0

      - name: Setup Golang
        uses: actions/setup-go@v4
        with:
          go-version-file: "./go.mod"
          token: ${{ env.GITHUB_TOKEN }}

      - name: Setup dependencies
        run: make deps

      - name: Run linting
        uses: hellofresh/jetstream-ci-scripts/actions/super-linter@master
        with:
          base-branch: master
          token: ${{ env.GITHUB_TOKEN }}
          use-golangci-lint-v2: true

      - name: "Run Helm Check: credit-autocancel-service"
        uses: hellofresh/jetstream-ci-scripts/actions/helm3-check@master
        env:
          AHOY_DIR: ahoy
          CHART_NAME: credit-autocancel-service
          GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
