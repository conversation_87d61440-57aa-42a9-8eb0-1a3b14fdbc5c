---
name: "PR: Terraform"

concurrency:
  group: pr-tf-${{ github.head_ref }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - master

permissions:
  contents: read
  id-token: write
  pull-requests: write

jobs:
  changed-directories:
    name: Generate job matrix
    runs-on: [ self-hosted, default ]
    outputs:
      matrix-merged: ${{ steps.set-matrix-merged.outputs.matrix }}
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      matrix-cache: ${{ steps.set-matrix-cache.outputs.matrix }}
    steps:
      - id: set-matrix
        name: Generate matrix | Infrastructure
        uses: hellofresh/action-changed-files@v2
        with:
          # match any subdirectory in terraform/ directory
          # except for terraform/cache/ subdirectory
          # with a modules/ subdirectory
          # or with a subdirectory containing Terraform files
          pattern: ^(?P<directory>terraform/(?!cache).*?(?=/modules/|/[^/]+\.(hcl|terraform-version|tf)$))
      - id: set-matrix-cache
        name: Generate matrix | Cache
        uses: hellofresh/action-changed-files@v2
        with:
          # match terraform/cache directory
          # except for terraform/cache/modules/ subdirectory
          # with a subdirectory containing Terraform files
          pattern: ^(?P<directory>terraform/cache/(?!modules).*?(?=/[^/]+\.(hcl|terraform-version|tf)$))
          # changes in terraform/cache/modules directory should be planned as default changes
          default-patterns: |
            terraform/cache/modules/**
      - id: set-matrix-merged
        name: Merge matrices
        run: |
          echo "matrix=$(echo '${{ steps.set-matrix.outputs.matrix }} ${{ steps.set-matrix-cache.outputs.matrix }}' | jq -s '[.[].include] | add | unique | sort | { include: . }' | tr '\n' ' ')" >> "$GITHUB_OUTPUT"

  terraform:
    name: Terraform Plan
    # skip when matrix is empty
    if: fromJson(needs.changed-directories.outputs.matrix-merged).include[0]
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    needs:
      - changed-directories
    strategy:
      fail-fast: false
      max-parallel: 5
      matrix: ${{ fromJson(needs.changed-directories.outputs.matrix-merged) }}
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          export-token: true
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;

      - name: Checkout source code
        uses: actions/checkout@v3

      - uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: eu-west-1
          role-to-assume: arn:aws:iam::489198589229:role/github-actions-runner
          role-duration-seconds: 3600
          role-skip-session-tagging: true

      - name: Terraform Plan
        if: matrix.reason != 'removed'
        uses: hellofresh/jetstream-ci-scripts/actions/terraform-tfenv@master
        with:
          working-directory: ${{ matrix.directory }}
          apply: "false"
          comment-type: "true"
          github-token: ${{ env.GITHUB_TOKEN }}
