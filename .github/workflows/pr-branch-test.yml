---
name: "PR: Test and Release"

concurrency:
  group: pr-test-${{ github.head_ref }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - master

jobs:
  test:
    name: Test
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;
            common/data/defaults SONAR_TOKEN ;
          secrets: |
            common/key-value/data/ci slack_url | SLACK_URL ;

      - name: Checkout source code
        uses: actions/checkout@v3
        with:
          # SonarQube requires commits history for the analysis
          fetch-depth: 0

      - name: Setup Golang
        uses: actions/setup-go@v4
        with:
          go-version-file: "./go.mod"
          token: ${{ env.GITHUB_TOKEN }}

      - name: Setup dependencies
        run: make deps-build

      - name: Run Unit tests
        run: make test-unit

      - name: Run Benthos Integration tests
        run: make test-benthos-integration
        env:
          SKIP_BUILD: true

      - name: Sonar<PERSON>ube Scan
        uses: hellofresh/jetstream-ci-scripts/actions/sonar-scanner@master
        env:
          SONAR_TOKEN: ${{ env.SONAR_TOKEN }}
          SONAR_HOST_URL: "https://sonarqube.tools-k8s.hellofresh.io"
        with:
          args: >
            -Dsonar.go.coverage.reportPaths=./coverage.txt,./coverage_integration.txt
            -Dproject.settings=./.github/assets/sonar-project.properties
            -Dsonar.scm.revision=${{ github.event.pull_request.head.sha }}

  release:
    name: Release
    needs: test
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;

      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Setup Golang
        uses: actions/setup-go@v4
        with:
          go-version-file: "./go.mod"
          token: ${{ env.GITHUB_TOKEN }}

      - name: Generate PR version
        id: version
        run: echo "new_version=PR-${{ github.event.pull_request.number }}" >> "$GITHUB_OUTPUT"

      - name: Build artifacts
        uses: ./.github/actions/build
        with:
          version: ${{ steps.version.outputs.new_version }}

      - name: "Build and push Docker Image: credit-autocancel-service-api"
        uses: docker/build-push-action@v3
        with:
          context: .
          file: ./api.Dockerfile
          push: true
          build-args: |
            VERSION=${{ steps.version.outputs.new_version }}
            GITHUB_TOKEN=${{ env.GITHUB_TOKEN }}
          tags: |
            489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-api:${{ steps.version.outputs.new_version }}

      - name: "Build and push Docker Image: credit-autocancel-service-job"
        uses: docker/build-push-action@v3
        with:
          context: .
          file: ./job.Dockerfile
          push: true
          build-args: |
            VERSION=${{ steps.version.outputs.new_version }}
            GITHUB_TOKEN=${{ env.GITHUB_TOKEN }}
          tags: |
            489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-job:${{ steps.version.outputs.new_version }}

      - name: "Build and push Docker Image: credit-autocancel-service-benthos"
        uses: docker/build-push-action@v3
        with:
          context: .
          file: ./benthos.Dockerfile
          push: true
          build-args: |
            VERSION=${{ steps.version.outputs.new_version }}
            GITHUB_TOKEN=${{ env.GITHUB_TOKEN }}
          tags: |
            489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-benthos:${{ steps.version.outputs.new_version }}
