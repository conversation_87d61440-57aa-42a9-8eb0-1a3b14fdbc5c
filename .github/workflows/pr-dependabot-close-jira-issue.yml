---
name: "PR - Dependabot - Close JIRA Issue"

on:
  pull_request:
    types:
      - closed
    branches:
      - master

permissions: write-all

jobs:
  dependabot-close-jira-issue:
    name: 'Dependabot - Close Jira Issue'
    runs-on: [ self-hosted, heavy ]
    if: ${{ github.event.pull_request.user.login == 'dependabot[bot]' &&  github.event.pull_request.merged == true }}

    steps:
      - name: Import Secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          secrets: |
            common/key-value/data/ci JIRA_BASE_URL | JIRA_BASE_URL;
            common/key-value/data/ci JIRA_EMAIL | JIRA_EMAIL;
            common/key-value/data/ci JIRA_TOKEN | JIRA_TOKEN;
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN | GITHUB_TOKEN;

      - name: Checkout source code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # fetch full history
          ref: master

      - name: Login to <PERSON><PERSON>
        uses: atlassian/gajira-login@master
        env:
          JIRA_BASE_URL: ${{ env.JIRA_BASE_URL }}
          JIRA_USER_EMAIL: ${{ env.JIRA_EMAIL }}
          JIRA_API_TOKEN: ${{ env.JIRA_TOKEN }}

      - name: Find Issue Key
        id: find_issue
        uses: atlassian/gajira-find-issue-key@master
        with:
          string: ${{ github.event.pull_request.title }}

      - name: Close issue
        uses: atlassian/gajira-transition@master
        with:
          issue: ${{ steps.find_issue.outputs.issue }}
          transition: "Done"
