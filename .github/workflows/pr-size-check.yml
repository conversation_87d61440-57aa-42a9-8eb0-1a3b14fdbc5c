name: PR Size Check

permissions:
  contents: read
  pull-requests: write

on:
  pull_request:
    types: [opened, synchronize, reopened]

jobs:
  check-pr-size:
    runs-on: ubuntu-latest
    name: Check PR Size
    
    steps:
      - name: Check PR Size
        uses: actions/github-script@v7
        with:
          script: |
            const { data: pr } = await github.rest.pulls.get({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: context.issue.number
            });
            
            const additions = pr.additions;
            const deletions = pr.deletions;
            const totalChanges = additions + deletions;
            const changedFiles = pr.changed_files;
            
            console.log(`PR Statistics:
            - Files changed: ${changedFiles}
            - Lines added: ${additions}
            - Lines deleted: ${deletions}
            - Total changes: ${totalChanges}`);
            
            // Find existing comment from this bot
            const { data: comments } = await github.rest.issues.listComments({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number
            });
            
            const botComment = comments.find(comment => 
              (comment.user.login === 'github-actions[bot]' || comment.user.type === 'Bot') && 
              comment.body.includes('PR Size Check')
            );
            
            console.log(`Found ${comments.length} comments on this PR`);
            if (botComment) {
              console.log(`Found existing bot comment with ID: ${botComment.id}`);
            } else {
              console.log('No existing bot comment found');
            }
            
            // Check if PR exceeds size limit
            const maxChanges = 400;
            let message;
            
            if (totalChanges > maxChanges) {
              message = `❌ **PR Size Check Failed**
              
              This PR is too large with **${totalChanges} total changes** (${additions} additions + ${deletions} deletions).
              
              **Maximum allowed changes: ${maxChanges}**
              
              Please consider:
              - Breaking this PR into smaller, more focused changes
              - Removing unnecessary changes (formatting, imports, etc.)
              - Moving large refactoring to separate PRs
              
              Smaller PRs are:
              - Easier to review
              - Less likely to introduce bugs
              - Faster to merge
              - Easier to revert if needed`;
              
              // Fail the check
              core.setFailed(`PR has ${totalChanges} changes, which exceeds the limit of ${maxChanges} changes.`);
            } else {
              message = `✅ **PR Size Check Passed**
              
              This PR has **${totalChanges} total changes** (${additions} additions + ${deletions} deletions).
              
              **Maximum allowed changes: ${maxChanges}**
              
              Thank you for keeping your PR size manageable! 🎉`;
              
              console.log(`✅ PR size check passed: ${totalChanges}/${maxChanges} changes`);
            }
            
            // Delete existing comment if it exists
            if (botComment) {
              console.log(`Attempting to delete comment with ID: ${botComment.id}`);
              const deleteResponse = await github.rest.issues.deleteComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                comment_id: botComment.id
              });
              console.log(`Delete response status: ${deleteResponse.status}`);
              console.log('Successfully deleted existing comment');
            }
            
            // Always create a new comment
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: context.issue.number,
              body: message
            }); 
  
