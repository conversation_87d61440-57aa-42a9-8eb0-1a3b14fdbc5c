---
name: "Deployment: promote to live"

concurrency: deploy

on:
  workflow_dispatch:

jobs:
  promote:
    name: Promote release to live
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    permissions:
      contents: read
      id-token: write
      actions: read
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;

      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Get deployed staging version
        id: staging
        uses: hellofresh/jetstream-ci-scripts/actions/get-deployed-release@master
        with:
          workflow-name: "Deployment: staging"

      - name: "Trigger Deployment: live"
        uses: benc-uk/workflow-dispatch@v1
        with:
          token: ${{ env.GITHUB_TOKEN }}
          workflow: 'Deployment: live'
          ref: master
          inputs: '{ "version": "${{ steps.staging.outputs.version }}" }'
