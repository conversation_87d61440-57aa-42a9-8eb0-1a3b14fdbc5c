---
name: "Deployment: staging"

concurrency: deploy

on:
  release:
    types:
      - published

jobs:
  deployment:
    name: <PERSON><PERSON> Deploy
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    permissions:
      contents: read
      id-token: write
    strategy:
      matrix:
        chart:
          - name: "credit-autocancel-service"
            path: "ahoy/credit-autocancel-service"
            deployment-tag: "tag"

    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;
          secrets: |
            common/key-value/data/ci slack_url | SLACK_URL ;
            staging/key-value/data/ci GRAFANA_TOKEN | GRAFANA_TOKEN ;

      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Setup k8s
        uses: hellofresh/jetstream-ci-scripts/actions/build-kubeconfig@master
        with:
          environment: "staging"

      - name: Deploy
        uses: hellofresh/jetstream-ci-scripts/actions/helm3-deploy@master
        with:
          release-name: ${{ matrix.chart.name }}
          chart-name: ${{ matrix.chart.name }}
          version: ${{ github.event.release.tag_name }}
          values-path: './${{ matrix.chart.path }}/values-staging.yaml'
          namespace: 'conversions'
          set-string: '${{ matrix.chart.deployment-tag }}="${{ github.event.release.tag_name }}"'

      - name: Ensure k8s rollout
        uses: hellofresh/jetstream-ci-scripts/actions/ensure-k8s-rollout@master
        with:
          deployment-namespace: 'conversions'
          deployment-selector: 'release=${{ matrix.chart.name }}'

      - name: Slack notification about job failure
        if: ${{ failure() }}
        uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
        with:
          slack-url: ${{ env.SLACK_URL }}
          icon-emoji: ":kube:"
          color: "danger"
          pretext: "Deployment failed! :x:"
          text: "Chart: ${{ matrix.chart.name }}; Version: ${{ github.event.release.tag_name }}; Job: ${{ github.job }}"

      - name: Slack notification about job success
        if: ${{ success() }}
        uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
        with:
          slack-url: ${{ env.SLACK_URL }}
          icon-emoji: ":kube:"
          color: "good"
          pretext: "Deployment finished! :approve:"
          text: "Version: ${{ github.event.release.tag_name }}; <https://github.com/${{ github.repository }}/actions/workflows/promote-live.yml|Promote to live>"

      - name: Push Grafana Annotation
        if: ${{ success() }}
        shell: bash
        env:
          GRAFANA_TOKEN: ${{ env.GRAFANA_TOKEN }}
          RELEASE_NAME: ${{ github.event.release.tag_name }}
        continue-on-error: true
        run: |
          timestampMs=$(date +%s%N | cut -b1-13)

          curl --fail -X "POST" "https://stghellofresh.grafana.net/api/annotations" \
          -H 'Content-Type: application/json; charset=utf-8' \
          -H 'Authorization: Bearer '"$GRAFANA_TOKEN" \
          -d '{
            "text": "Deployment '"$RELEASE_NAME"'",
            "tags": [
              "deployments",
              "credit-autocancel-service-api-k8s",
              "credit-autocancel-service-job-k8s",
              "credit-autocancel-service-benthos-k8s"
            ],
            "time": '"$timestampMs"',
            "timeEnd": '"$timestampMs"'
          }'
