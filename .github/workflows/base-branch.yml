---
name: "Base Branch"

concurrency: release

on:
  push:
    branches:
      - master

jobs:
  test:
    name: Test
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;
            common/data/defaults SONAR_TOKEN ;
          secrets: |
            common/key-value/data/ci slack_url | SLACK_URL ;

      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Setup Golang
        uses: actions/setup-go@v4
        with:
          go-version-file: "./go.mod"
          token: ${{ env.GITHUB_TOKEN }}

      - name: Setup dependencies
        run: make deps-build

      - name: Run Unit tests
        run: make test-unit

      - name: Run Benthos Integration tests
        run: make test-benthos-integration
        env:
          SKIP_BUILD: true

      - name: SonarQube Scan
        uses: hellofresh/jetstream-ci-scripts/actions/sonar-scanner@master
        env:
          SONAR_TOKEN: ${{ env.SONAR_TOKEN }}
          SONAR_HOST_URL: "https://sonarqube.tools-k8s.hellofresh.io"
        with:
          args: >
            -Dsonar.go.coverage.reportPaths=./coverage_unit.txt,./coverage_integration.txt
            -Dproject.settings=./.github/assets/sonar-project.properties
            -Dsonar.scm.revision=${{ github.event.pull_request.head.sha }}

      - name: Slack notification about job failure
        if: ${{ failure() }}
        uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
        with:
          slack-url: ${{ env.SLACK_URL }}
          icon-emoji: ":fire:"
          color: "danger"
          pretext: "Base branch build failed! :x:"
          text: "Job: ${{ github.job }}"

  release:
    name: Release
    needs: test
    runs-on: [ self-hosted, default ]
    timeout-minutes: 15
    permissions:
      contents: read
      id-token: write
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;
            common/data/defaults artifactory_username | ARTIFACTORY_USERNAME ;
            common/data/defaults artifactory_password | ARTIFACTORY_PASSWORD ;
          secrets: |
            common/key-value/data/ci slack_url | SLACK_URL ;

      - name: Checkout source code
        uses: actions/checkout@v3

      - name: Setup Golang
        uses: actions/setup-go@v4
        with:
          go-version-file: "./go.mod"
          token: ${{ env.GITHUB_TOKEN }}

      - name: Generate release version
        id: version
        uses: hellofresh/jetstream-ci-scripts/actions/cal-ver@master

      - name: Build artifacts
        uses: ./.github/actions/build
        with:
          version: ${{ steps.version.outputs.new_version }}

      - name: "Build and push Docker Image: credit-autocancel-service-api"
        uses: docker/build-push-action@v3
        with:
          context: .
          file: ./api.Dockerfile
          push: true
          build-args: |
            VERSION=${{ steps.version.outputs.new_version }}
            GITHUB_TOKEN=${{ env.GITHUB_TOKEN }}
          tags: |
            489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-api:latest
            489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-api:${{ steps.version.outputs.new_version }}

      - name: "Build and push Docker Image: credit-autocancel-service-job"
        uses: docker/build-push-action@v3
        with:
          context: .
          file: ./job.Dockerfile
          push: true
          build-args: |
            VERSION=${{ steps.version.outputs.new_version }}
            GITHUB_TOKEN=${{ env.GITHUB_TOKEN }}
          tags: |
            489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-job:latest
            489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-job:${{ steps.version.outputs.new_version }}

      - name: "Build and push Docker Image: credit-autocancel-service-benthos"
        uses: docker/build-push-action@v3
        with:
          context: .
          file: ./benthos.Dockerfile
          push: true
          build-args: |
            VERSION=${{ steps.version.outputs.new_version }}
            GITHUB_TOKEN=${{ env.GITHUB_TOKEN }}
          tags: |
            489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-benthos:latest
            489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-benthos:${{ steps.version.outputs.new_version }}

      - name: "Build and push chart: credit-autocancel-service"
        uses: hellofresh/jetstream-ci-scripts/actions/helm3-build-and-push@master
        env:
          ARTIFACTORY_USERNAME: ${{ env.ARTIFACTORY_USERNAME }}
          ARTIFACTORY_PASSWORD: ${{ env.ARTIFACTORY_PASSWORD }}
          CHART_NAME: credit-autocancel-service
          CHART_PATH: ahoy/credit-autocancel-service
          GITHUB_TOKEN: ${{ env.GITHUB_TOKEN }}
          VERSION: ${{ steps.version.outputs.new_version }}

      - name: Build application manifests
        uses: hellofresh/jetstream-ci-scripts/actions/build-application-manifests@master
        with:
          version: ${{ steps.version.outputs.new_version }}

      - name: GitHub release
        uses: hellofresh/jetstream-ci-scripts/actions/github-release@master
        with:
          access_token: ${{ env.GITHUB_TOKEN }}
          tag: "${{ steps.version.outputs.new_version }}"
          assets: >
            ./app-manifests.tar.gz

      - name: Slack notification about job failure
        if: ${{ failure() }}
        uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
        with:
          slack-url: ${{ env.SLACK_URL }}
          icon-emoji: ":fire:"
          color: "danger"
          pretext: "Base branch build failed! :x:"
          text: "Job: ${{ github.job }}"

  changed-directories:
    name: Generate job matrix
    needs: release
    runs-on: [ self-hosted, default ]
    outputs:
      matrix-merged: ${{ steps.set-matrix-merged.outputs.matrix }}
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      matrix-cache: ${{ steps.set-matrix-cache.outputs.matrix }}
    steps:
      - id: set-matrix
        name: Generate matrix | Infrastructure
        uses: hellofresh/action-changed-files@v2
        with:
          # match any subdirectory in terraform/ directory
          # except for terraform/cache/ subdirectory
          # with a modules/ subdirectory
          # or with a subdirectory containing Terraform files
          pattern: ^(?P<directory>terraform/(?!cache).*?(?=/modules/|/[^/]+\.(hcl|terraform-version|tf)$))
      - id: set-matrix-cache
        name: Generate matrix | Cache
        uses: hellofresh/action-changed-files@v2
        with:
          # match terraform/cache directory
          # except for terraform/cache/modules/ subdirectory
          # with a subdirectory containing Terraform files
          pattern: ^(?P<directory>terraform/cache/(?!modules).*?(?=/[^/]+\.(hcl|terraform-version|tf)$))
          # changes in terraform/cache/modules directory should be planned as default changes
          default-patterns: |
            terraform/cache/modules/**
      - id: set-matrix-merged
        name: Merge matrices
        run: |
          echo "matrix=$(echo '${{ steps.set-matrix.outputs.matrix }} ${{ steps.set-matrix-cache.outputs.matrix }}' | jq -s '[.[].include] | add | unique | sort | { include: . }' | tr '\n' ' ')" >> "$GITHUB_OUTPUT"

  terraform:
    name: Terraform Apply
    if: fromJson(needs.changed-directories.outputs.matrix-merged).include[0]
    runs-on: [ self-hosted, default ]
    needs: [ changed-directories ]
    timeout-minutes: 15
    permissions:
      contents: read
      id-token: write
      pull-requests: write
    strategy:
      fail-fast: false
      max-parallel: 5
      matrix: ${{ fromJson(needs.changed-directories.outputs.matrix-merged) }}
    steps:
      - name: Import secrets
        id: vault-secrets
        uses: hellofresh/jetstream-ci-scripts/actions/vault@master
        with:
          export-token: true
          shared-secrets: |
            common/data/defaults GITHUB_TOKEN ;
          secrets: |
            common/key-value/data/ci slack_url | SLACK_URL ;

      - name: Checkout source code
        uses: actions/checkout@v3

      - uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-region: eu-west-1
          role-to-assume: arn:aws:iam::489198589229:role/github-actions-runner
          role-duration-seconds: 3600
          role-skip-session-tagging: true

      - name: Terraform Apply
        id: terraform-apply
        if: matrix.reason != 'removed'
        uses: hellofresh/jetstream-ci-scripts/actions/terraform-tfenv@master
        with:
          working-directory: ${{ matrix.directory }}
          apply: ${{ github.event_name == 'push' }}
          comment-type: "true"
          github-token: ${{ env.GITHUB_TOKEN }}

      - name: Slack notification about job success
        if: success() && !contains(steps.terraform-apply.outputs.stdout, '0 added, 0 changed, 0 destroyed')
        uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
        with:
          slack-url: ${{ env.SLACK_URL }}
          icon-emoji: ":terraform:"
          color: "good"
          pretext: "Terraform apply finished! :approve:"
          text: "Version: ${{ github.event.release.tag_name }}"

      - name: Slack notification about job failure
        if: ${{ failure() }}
        uses: hellofresh/jetstream-ci-scripts/actions/slack-notification@master
        with:
          slack-url: ${{ env.SLACK_URL }}
          icon-emoji: ":terraform:"
          color: "danger"
          pretext: "Terraform apply failed! :x:"
          text: "Version: ${{ github.event.release.tag_name }}"
