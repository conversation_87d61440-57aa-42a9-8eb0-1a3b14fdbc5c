---
# Jetstream configuration file version (Managed by <PERSON><PERSON><PERSON>, don't edit manually!)
jetstream_version: 2.20.0

# Name of your project
project_name: credit-autocancel-service

#-----------------------------------------------------------------------------------------------------------------------
# GitHub
#-----------------------------------------------------------------------------------------------------------------------

# GitHub User/Company name
github_user: hellofresh
# GitHub repository Name
github_repository_name: credit-autocancel-service
# GitHub base branch - master, main or any other valid branch name that contains production-ready code
github_base_branch: master
# GitHub releases flag
github_build_release: false
# Prefix release/tag version with 'v'
github_prefix_release: false
# Use SemVer or CalVer for releases tagging
github_sem_ver: false

# Configure stale action
github_stale_config:
  enabled: false
  action_args:

job_timeout_minutes: 15

use_argo_flow: false

#-----------------------------------------------------------------------------------------------------------------------
# Jetstream
#-----------------------------------------------------------------------------------------------------------------------
language_name: golang
language_version: "1.21"

build_oses: [ linux, darwin ]
build_architectures: [ 386, amd64 ]

slack_notifications: true

docker: true
docker_images:
  - name: "credit-autocancel-service-api"
    repository: "489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-api"
    path: "api.Dockerfile"
    tag: ""
    
  - name: "credit-autocancel-service-job"
    repository: "489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-job"
    path: "job.Dockerfile"
    tag: ""
    
  - name: "credit-autocancel-service-benthos"
    repository: "489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-benthos"
    path: "benthos.Dockerfile"
    tag: ""
    

kubernetes: true
kubernetes_staging_deployment: true
kubernetes_live_deployment: true
kubernetes_live_manual_judgment: true
kubernetes_tools_deployment: false
kubernetes_deployment_namespace: "conversions"
kubernetes_secrets_from_vault: true
kubernetes_charts:
  - name: "credit-autocancel-service"
    path: "ahoy/credit-autocancel-service"
    deployment_tag: "tag"
    multi_country: false
# List of environments to deploy to in FTCP
# Valid values are: "stage", "prod", "shared"
kubernetes_deployment_environments:
  - "stage"
helm_version: "v3"

terraform: true
terraform_version: "1.x"
terraform_live_on_promote: false

testing_unit: true
testing_integration: true
# Use Docker containers in integration tests.
docker_enabled: true
sonarqube: true
# A set of variables to export for each integration test in the form of dictionary
# Example: `AMQP_DSN: "amqp://guest:guest@localhost:5672"`
testing_variables:
  APP_HOST: "localhost"

documentation_raml: false
promote_release_manually: false
build_edge_release: false
