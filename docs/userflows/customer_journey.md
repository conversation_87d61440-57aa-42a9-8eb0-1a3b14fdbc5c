# Customer Journey
```mermaid
sequenceDiagram
    autonumber
    participant B as Benthos
    participant D as DB
    participant J as Job
    B->>D: Receive new order and benefit attachment events and save records
    B->>D: Find order event with a join of delivery and payments events
    D-->>D: Check a running subscription (subscription already enroled)
    Note right of D: no running subscription
    Note right of D: if running subscription is found, skip 4, 5 and 6, go to 7
    create participant V as Voucher Service
    B->>V: Check voucher code
    Note right of V: valid voucher found
    D-->>D: Verify the existence of campaign ID and determine the associated campaign type
    Note right of D: campaign ID exists
    B--xD: Skip all next steps when campaign type is CANCLE_ONCE
    Note left of D: and when a cancelled subscription is found
    B--xD: Skip all next steps when there is a successful benefit attachment event
    Note left of D: and sets a running subscription
    D-->>D: Lookup legacy customer ID
    Note right of D: legacy customer ID found
    create participant P as Price Service
    B->>P: Calculate price for next order
    Note right of P: grandTotal > 0
    Note right of P: when grandTotal = 0, skip 9, 11 and 12, only do 10
    D-->>D: Set subscription for auto-cancellation (cancellation date in the next 72hrs) [grandTotal > 0]
    D-->>D: Set a running subscripion [grandTotal = 0]
    create participant T as TCS
    B->>T: Send email message for cancellation in next 3 days (72hrs)
    create participant C as CPS
    J->>C: Trigger a subscription cancellation
```
