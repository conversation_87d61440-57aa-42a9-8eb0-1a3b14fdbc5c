# Customer Order Paid
```mermaid
graph TD
    subgraph Kafka Message inbound
    A[Customer, Order, Delivery and Payment] -->|public.customer.v1beta2, public.customer.order.v1, public.customer.order.delivery.v1, public.customer.order.payment.v1| B[(Record Kafka Events)]
    B -->|Extract order_uuid, country, customer_plan_id, subscription_id, customer_uuid | E[Benthos Parser]
    E --> |After successful parsing| F[Publish message to private topic credit-autocancel-service.benthos.trigger-order-paid]
    end

    subgraph Benthos Trigger Order Paid
    F --> |Find order event with a join of delivery, and payment event in DB| G{Check autocancel table with customer_plan_id and status: running whether subscription has been enrolled}
    G --> |Record found| H[Skip voucher code checks]
    
    G --> |Record not found| I{Call voucher service to get campaign ID}
    I --> |Voucher not found| J[Subscription not recorded]
    I --> |Voucher endpoint return Campaign ID| K{Check if campaign ID and country exists in campaign table}
    K --> |Campaign ID not found| J[Subsription not recorded]
    K --> |Campaign ID Exists in campaign table| M[Lookup legacy customer ID from DB]
    H --> M
    M --> N{Calls price service to calculate price for next order}
    N --> |GrandTotal = 0| O[Upserts the autocancel table with customer_plan_id, campaign_id, customer_uuid and voucher_code, status: running, and box_count]
    N --> |GrandTotal > 0| P[Upserts autocancel table with values : customer_plan_id, campaign_id, customer_uuid voucher_code, status: tobecanceled and cancellation_date  - set for the next 72hrs]
    end

    subgraph External Message Customers Exchange
    P --> Q[Publish TCS event auto-cancellation message]
    end
```
