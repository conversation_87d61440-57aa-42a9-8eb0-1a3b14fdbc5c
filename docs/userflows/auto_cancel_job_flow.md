# Credit Auto cancel Job
```mermaid
graph TD
    subgraph AutoCancel Service main entry Point
    A[Load Environment Configruation] -->|config variables: BOB_INTERNAL_URL, COUNTRY_CODES, database.Config, JOB_INTERVAL, TOGGLE_CPS_CHANGES, TOGGLE_CPS_CLIENT_COUNTRIES, CPS_BASE_URL| B[Database Connect via database.condig]
    B -->|Inititilze customerConvertedPublisher, cancellationPublisher, subscriptionRepo, canceller: Old way send request to BOB, cpsCanceller: new way to cancel through customer-plans-squad| C[Ticker intilize]
    C --> |Ticker is intilized with job internal of 5 minutes from the cfg and environment variables| D[cpsCancellerCountriesToggle]
    D --> |Sets toggle to true for every country available in CFG for CPS| E[Make Channel with OS.Signal] 
    E --> |Creates a go routine with two select cases for done and calling Execute Cancellations| G[Calls Job With Executive Cancellations]
    end

    subgraph JOB
    G --> H[executeCancellations]
    H --> |Params for execute Cancellations: <br/> context, subscriptionRepo, cancellationPublisher, cpsCancellerCountriesToggle, cpsCanceller|I[Calls Repository method AllSetForCancellation]
    I -->|AllSetForCancellation checks autocancel table for subscriptions where status = tobecanceled AND cancellation_date < now <br>/ All records are returned in the subscriptionList'|J{SubscriptionList}
    J -->|Do a loop over the subscripions returned in subscriptionlist| K{toggleCPSClientCountries converts sub.Country to UPPERCASE AND Sub has Valid Customer PlanID}
    K -->|Country exists and plan id exists <br/> logs Cancelling subscription through CPS| L[CPS.Cancellation]
    end

    subgraph external API CPS
    L --> M[Creates Post Request] --> N[CPS_BASEURL/plans/planID/cancel]
    N --> |get Response from CPS URL and Check status is 200 <br/> The response payload contains Cancellatedat and SubscriptionID|O[isCancelled function return true]
    end
    
    subgraph external API CPS
    L --> P[Calls canceller.Cancellation fucntion] --> |params: country, subscription_id, time| Q[Post Request to BOB_BASEURL/alice/webservice]
    Q--> |get Response from BOB and Check status is 200 <br/> The response payload contains result Cancelled|R[isCancelled function return true]
    end

    subgraph external exchange
    S[CancellationPublisher] --> |Publishes message to subscription_changes_exchange <br/> Type:subscription.cancellation|T[Publish]
    T --> |Payload for cancellation event: CancellationDate, CustomerID,Isb2bSale, RemainingVoucherAmount,SubscriptionID, VoucherCode | U[SetASCancelled]
    end
    
    subgraph internal DB
    U -->|Updates Autocancel Table and sets status=cancellation for the subscription ID and Country| v[(database)]
    end

```

1. Credit Auto Cancel Job is a go routine that is runs every 5 minutes
2. Go routine calls ExecuteCancellations with parameters: context, subscriptionRepo, cancellationPublisher, cpsCancellerCountriesToggle, cpsCanceller
3. ExecuteCancellations calls repository method AllSetForCancellation
4. AllSetForCancellation checks autocancel table for subscriptions where status = tobecanceled AND cancellation_date < now()
5. All records are returned in the subscriptionList
6. Range over the subscriptionList and do validations
    - check if toggleCPSClientCountries(return true if the country exists in CPS) AND SubscriptionList has valid Customer PLan ID
    - if check above is true log cancelling subscriptions through CPS
    - Call cpsCanceller.Cancellation with params: customerplanid.uuId
    - Cancellation Created a new Post Request to CPS_BASEURL/plans/planID/cancel
    - If Response is 200 from CPS and the response payload contains cancelledAt and subscription ID the function returns true.
7. If toggleCPSClientCountries returns false or does not have a valid Customer Plan ID
    - Call the canceller.Cancellation function with params: country, subscriptionID, time
    - Cancellation gets the baseurl for country from params and adds country to it.
    - Once baseurl is constructed it adds alicURL: alice/webservice to the end of URL
    - A post Request is made to the newly constructed URL and request body is build with subscriptionID, cancellationDate
    - If Response is 200 and the response body contains result string CANCELED
    - Cancellation call performed successfully
8. Publish to external exchange 'subscription_changes_exchange' with [CancellationEventPayload](https://github.com/hellofresh/credit-autocancel-service/blob/master/internal/subscription/events.go#L23-L31)
9. Calls Repository Method(SetAsCancelled) with parameters: subscriptionId, Country. SetAsCancelled sets the status of the customer in Autocancel table to cancelled

