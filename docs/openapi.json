{"openapi": "3.1.0", "info": {"title": "Credit Autocancel Service API", "version": "1.0.0", "description": "API for managing credit autocancel service campaigns"}, "servers": [{"url": "https://credit-autocancel-service.live-k8s.hellofresh.io", "description": "Production", "variables": {"basePath": {"default": "/", "description": "Base path for all API endpoints"}, "protocol": {"enum": ["https", "http"], "default": "https", "description": "The transfer protocol"}}}, {"url": "https://credit-autocancel-service.staging-k8s.hellofresh.io", "description": "Staging", "variables": {"basePath": {"default": "/", "description": "Base path for all API endpoints"}, "protocol": {"enum": ["https", "http"], "default": "https", "description": "The transfer protocol"}}}, {"url": "http://localhost:9090", "description": "Local", "variables": {"basePath": {"default": "/", "description": "Base path for all API endpoints"}, "port": {"default": "9090", "description": "Port number for local development"}, "protocol": {"enum": ["http"], "default": "http", "description": "The transfer protocol"}}}], "paths": {"/health": {"get": {"summary": "Service health check endpoint", "description": "Used for Kubernetes readiness probes to determine if the service is ready to accept traffic", "responses": {"200": {"description": "Service is healthy and ready to accept requests"}}}}, "/alive": {"get": {"summary": "Service liveness check endpoint", "description": "Used for Kubernetes liveness probes to determine if the service is alive", "responses": {"200": {"description": "Service is alive"}}}}, "/campaigns": {"get": {"summary": "Get all campaigns", "description": "Retrieve all campaigns with filtering by country", "parameters": [{"name": "country", "in": "query", "required": true, "schema": {"type": "string"}, "description": "HelloFresh country code"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Campaign"}}}}}, "204": {"description": "No Content - No campaigns found for the specified country"}, "400": {"description": "Bad Request - Missing or invalid country parameter"}, "500": {"description": "Internal Server Error"}}}, "post": {"summary": "Create new campaigns", "description": "Create one or more new campaigns for a specific country", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CampaignsPayload"}}}}, "responses": {"200": {"description": "OK - Campaigns created successfully"}, "400": {"description": "Bad Request - Invalid request payload or country code not enabled"}, "500": {"description": "Internal Server Error"}}}}, "/campaigns/{campaignId}": {"patch": {"summary": "Update campaign status and/or type", "description": "Update the active status and/or campaign type of a single campaign for a specific country. At least one field (active or campaign_type) must be provided.", "parameters": [{"name": "campaignId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}, "description": "Unique identifier for the campaign"}, {"name": "country", "in": "query", "required": true, "schema": {"type": "string", "minLength": 2, "maxLength": 2}, "description": "HelloFresh country code"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCampaignPayload"}}}}, "responses": {"200": {"description": "OK - Campaign updated successfully"}, "400": {"description": "Bad Request - Invalid request payload or country code not enabled"}, "404": {"description": "Not Found - Campaign not found for the specified campaign ID and country"}, "500": {"description": "Internal Server Error"}}}}}, "components": {"schemas": {"CampaignsPayload": {"type": "object", "required": ["campaign_ids", "active", "country"], "properties": {"campaign_ids": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of campaign IDs to create"}, "active": {"type": "boolean", "description": "Whether the campaigns are active or not"}, "country": {"type": "string", "minLength": 2, "maxLength": 2, "description": "HelloFresh country code"}, "campaign_type": {"type": "string", "enum": ["CANCEL_ANY", "CANCEL_ONCE"], "description": "Type of campaign: CANCEL_ANY (all subscriptions) or CANCEL_ONCE (first subscription only)", "default": "CANCEL_ANY"}}}, "Campaign": {"type": "object", "properties": {"campaign_id": {"type": "string", "format": "uuid", "description": "Unique identifier for the campaign"}, "active": {"type": "boolean", "description": "Whether the campaign is active or not"}, "country": {"type": "string", "description": "HelloFresh country code"}, "campaign_type": {"type": "string", "enum": ["CANCEL_ANY", "CANCEL_ONCE"], "description": "Type of campaign: CANCEL_ANY (all subscriptions) or CANCEL_ONCE (first subscription only)"}, "created_at": {"type": "string", "format": "date-time", "description": "Timestamp when the campaign was created"}}}, "UpdateCampaignPayload": {"type": "object", "properties": {"active": {"type": "boolean", "description": "Whether the campaign is active or not"}, "campaign_type": {"type": "string", "enum": ["CANCEL_ANY", "CANCEL_ONCE"], "description": "Type of campaign: CANCEL_ANY (all subscriptions) or CANCEL_ONCE (first subscription only)"}}, "anyOf": [{"required": ["active"]}, {"required": ["campaign_type"]}, {"required": ["active", "campaign_type"]}]}}}}