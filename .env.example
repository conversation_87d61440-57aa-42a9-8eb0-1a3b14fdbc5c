DB_HOST=postgres
DB_PORT=5432
DB_USER=credit-autocancel-service
DB_PASSWORD=freshforyou
DB_NAME=creditautocanceldb
POSTGRES_USER=credit-autocancel-service
POSTGRES_PASSWORD=freshforyou
POSTGRES_DB=creditautocanceldb
PORT=9090
APP_ENV=development
AMQP_DSN=amqp://credit-autocancel-service:credit-autocancel-service@rabbit:5672/
AUTH_URL=http://auth-service.staging-k8s.hellofresh.io
PRICE_SERVICE_URL=http://price-service.staging-k8s.hellofresh.io
VOUCHER_SERVICE_URL=http://voucher-service.staging-k8s.hellofresh.io
COUNTRY_CODES=AO,AT,AU,BE,CA,CG,CH,CK,DE,DK,ER,FR,GB,GN,LU,NL,NZ,SE,US
CPS_BASE_URL=http://customer-plans-service-api-k8s.consumer-core.svc.cluster.local/
BALANCE_SERVICE_URL=http://balance-service-api-k8s.payments.svc.cluster.local

# -------------------------------------------------
# SECRETS has to be replaced with values from vault
CLIENT_SECRET=secret
CLIENT_ID=id
