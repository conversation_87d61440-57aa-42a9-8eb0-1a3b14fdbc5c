ALTER TABLE events_customer_plan_v1
    DROP COLUMN business_unit;

CREATE INDEX IF NOT EXISTS idx_events_customer_plan_v1_plan_id
    ON events_customer_plan_v1 (customer_plan_id);

CREATE INDEX IF NOT EXISTS idx_events_customer_legacy_subscription_v1beta3_plan_id
    ON events_customer_legacy_subscription_v1beta3 (customer_plan_id);

DROP INDEX IF EXISTS idx_events_customer_plan_v1_business_unit_plan_id;
DROP INDEX IF EXISTS idx_events_customer_legacy_subscription_v1beta3_business_unit_p;
