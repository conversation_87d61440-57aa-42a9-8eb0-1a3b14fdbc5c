ALTER TABLE events_customer_order_v1 ADD order_uuid VARCHAR NOT NULL DEFAULT '00000000-0000-0000-0000-000000000000';

CREATE INDEX idx_events_customer_order_v1_lwr_bu_lwr_ord_uuid
    ON events_customer_order_v1 (lower(business_unit), lower(order_uuid))
;

DROP INDEX idx_events_customer_order_v1_2_lwr_bu_lwr_ord_uuid;
DROP INDEX idx_events_customer_order_v1_2_created_at;

DROP TABLE events_customer_order_v1_2;
