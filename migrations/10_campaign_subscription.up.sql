CREATE TABLE campaign_subscription
(
   campaign_subscription_uuid uuid default uuid_generate_v4() NOT NULL,
   created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
   campaign_id uuid NOT NULL,
   subscription_id integer NOT NULL
);

CREATE UNIQUE INDEX campaign_subscription_campaign_subscription_uuid_uindex
   ON campaign_subscription (campaign_subscription_uuid);

ALTER TABLE campaign_subscription
   ADD CONSTRAINT campaign_subscription_pk
      PRIMARY KEY (campaign_subscription_uuid);
