CREATE TABLE events_customer_v1beta2
(
    id                 uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),
    created_at         TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    business_unit      VARCHAR NOT NULL,
    customer_id        VARCHAR NOT NULL,
    customer_uuid      VARCHAR NOT NULL,

    metadata           JSONB
);

CREATE INDEX idx_events_customer_v1beta2_business_unit_customer_uuid
    ON events_customer_v1beta2 (business_unit, customer_uuid);

