CREATE TABLE events_customer_order_v1_2
(
    id            uuid PRIMARY KEY         DEFAULT gen_random_uuid(),
    created_at    TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    business_unit VARCHAR NOT NULL,
    order_uuid    VARCHAR NOT NULL,

    raw           BYTEA   NOT NULL,
    metadata      JSONB
)
;

CREATE INDEX idx_events_customer_order_v1_2_lwr_bu_lwr_ord_uuid
    ON events_customer_order_v1_2 (lower(business_unit), lower(order_uuid))
;

CREATE INDEX idx_events_customer_order_v1_2_created_at
    ON events_customer_order_v1_2 (created_at)
;

CREATE TABLE events_customer_order_payment_v1
(
    id            uuid PRIMARY KEY         DEFAULT gen_random_uuid(),
    created_at    TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    business_unit VARCHAR NOT NULL,
    order_uuid    VARCHAR NOT NULL,

    payment_state INTEGER NOT NULL,

    raw           BYTEA   NOT NULL,
    metadata      JSONB
)
;

CREATE INDEX idx_events_customer_order_payment_v1_lwr_bu_lwr_ord_uuid_pmt_st
    ON events_customer_order_payment_v1 (lower(business_unit), lower(order_uuid), payment_state)
;

CREATE INDEX idx_events_customer_order_payment_v1_created_at
    ON events_customer_order_payment_v1 (created_at)
;

CREATE TABLE events_customer_order_delivery_v1
(
    id            uuid PRIMARY KEY         DEFAULT gen_random_uuid(),
    created_at    TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    business_unit VARCHAR NOT NULL,
    order_uuid    VARCHAR NOT NULL,

    raw           BYTEA   NOT NULL,
    metadata      JSONB
)
;

CREATE INDEX idx_events_customer_order_delivery_v1_lwr_bu_lwr_ord_uuid
    ON events_customer_order_delivery_v1 (lower(business_unit), lower(order_uuid))
;

CREATE INDEX idx_events_customer_order_delivery_v_created_at
    ON events_customer_order_delivery_v1 (created_at)
;
