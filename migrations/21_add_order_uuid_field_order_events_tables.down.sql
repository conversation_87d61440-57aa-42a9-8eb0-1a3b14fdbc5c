ALTER TABLE events_customer_order_v1 DROP COLUMN order_uuid;

DROP INDEX idx_events_customer_order_v1_lwr_bu_lwr_ord_uuid;

CREATE TABLE events_customer_order_v1_2
(
    id            uuid PRIMARY KEY         DEFAULT gen_random_uuid(),
    created_at    TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    business_unit VARCHAR NOT NULL,
    order_uuid    VARCHAR NOT NULL,

    raw           BYTEA   NOT NULL,
    metadata      JSONB
)
;

CREATE INDEX idx_events_customer_order_v1_2_lwr_bu_lwr_ord_uuid
    ON events_customer_order_v1_2 (lower(business_unit), lower(order_uuid))
;

CREATE INDEX idx_events_customer_order_v1_2_created_at
    ON events_customer_order_v1_2 (created_at)
;
