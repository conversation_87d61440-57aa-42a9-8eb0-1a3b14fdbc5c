-- enable built-in uuid postgres extension;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- create a custom type to hold all possible status that a subscription can have regarding the autocancel feature.
CREATE TYPE autocancel_status_enum AS ENUM('running', 'cancelled');

-- main table holding all customer related information
CREATE TABLE autocancel (
  autocancel_id uuid DEFAULT uuid_generate_v4(),
  subscription_id int4 NOT NULL,
  customer_uuid uuid NULL,
  voucher VARCHAR(60) NOT NULL,
  status autocancel_status_enum NOT NULL DEFAULT 'running',
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE autocancel ADD PRIMARY KEY (autocancel_id);
ALTER TABLE autocancel ADD UNIQUE (subscription_id);

-- table holding information regarding campaigns, can be used to quickly activate/deactivate the autocancel from a
-- specific campaign_id.
CREATE TABLE campaign (
  campaign_id uuid NOT NULL,
  active bool DEFAULT TRUE,
  created_at timestamp DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp DEFAULT CURRENT_TIMESTAMP
);
ALTER TABLE campaign ADD PRIMARY KEY (campaign_id);

-- function to automatically update the "updated_at" field with the current time
CREATE FUNCTION changed() RETURNS trigger
AS '
BEGIN
   	NEW.updated_at := CURRENT_TIMESTAMP;
    RETURN NEW;
END
' LANGUAGE plpgsql;

-- trigger to call the `changed()` function every time an update operation happens on the "autocancel" table. This will
-- ensure the "updated_at" field is always correct.
CREATE TRIGGER autocancel_trigger BEFORE
UPDATE
  ON autocancel FOR EACH ROW EXECUTE PROCEDURE changed();

-- same as above but for the "campaign" table.
CREATE TRIGGER campaign_trigger BEFORE
UPDATE
  ON campaign FOR EACH ROW EXECUTE PROCEDURE changed();
