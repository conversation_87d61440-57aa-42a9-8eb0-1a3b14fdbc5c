CREATE TABLE events_customer_v1beta2_v2
(
    id                 uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),

    business_unit      VARCHAR NOT NULL,
    customer_id        VARCHAR NOT NULL,
    customer_uuid      VARCHAR NOT NULL,

    metadata           JSONB,

    created_at         TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at         TIMESTAMP WITH TIME ZONE DEFAULT now()
);

CREATE UNIQUE INDEX idx_events_customer_v1beta2_v2_business_unit_customer_uuid
    ON events_customer_v1beta2_v2 (business_unit, customer_uuid);
