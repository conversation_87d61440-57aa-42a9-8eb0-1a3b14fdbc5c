CREATE TABLE public.autocancel_history (
    autocancel_id uuid NOT NULL,
    revision int NOT NULL DEFAULT 1,
    state jsonb NOT NULL,
    created_at timestamptz NOT NULL DEFAULT NOW(),
    CONSTRAINT autocancel_history_un UNIQUE (revision,autocancel_id),
    CONSTRAINT autocancel_history_fk FOREIGN KEY (autocancel_id) REFERENCES public.autocancel(autocancel_id)
);

CREATE FUNCTION update_autocancel_history()
    RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
    INSERT INTO autocancel_history (
        autocancel_id,
        revision,
        created_at,
        state
    ) VALUES(
                OLD.autocancel_id,
                (SELECT COUNT(autocancel_id)+1 FROM autocancel_history WHERE autocancel_id = OLD.autocancel_id),
                now(),
                (SELECT row_to_json(row) FROM (SELECT old.*) row)
            );
    RETURN OLD;
END $$;

CREATE TRIGGER before_update_autocancel
    AFTER UPDATE ON autocancel
    FOR EACH ROW
    WHEN (OLD.* IS DISTINCT FROM NEW.*)
EXECUTE PROCEDURE update_autocancel_history();
