ALTER TABLE events_customer_plan_v1
    ADD COLUMN business_unit VARCHAR NOT NULL;

CREATE INDEX idx_events_customer_plan_v1_business_unit_plan_id
    ON events_customer_plan_v1 (business_unit, customer_plan_id);

CREATE INDEX idx_events_customer_legacy_subscription_v1beta3_business_unit_p
    ON events_customer_legacy_subscription_v1beta3 (business_unit, customer_plan_id);

DROP INDEX idx_events_customer_plan_v1_plan_id;
DROP INDEX idx_events_customer_legacy_subscription_v1beta3_plan_id;

