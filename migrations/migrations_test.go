package migrations

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAssetNames(t *testing.T) {
	t.<PERSON>llel()
	names := AssetNames()
	require.NotEmpty(t, names)

	// easier lookup
	foundMigrations := make(map[string]bool)
	for _, name := range names {
		foundMigrations[name] = true
	}

	// check if each migration exists as expected
	for name, tc := range map[string]struct {
		description string
		shouldExist bool
	}{
		"1_initial_database.up.sql":   {"initial up migration", true},
		"1_initial_database.down.sql": {"initial down migration", true},
		"nonexistent_file.sql":        {"non-existent file", false},
	} {
		t.Run(tc.description, func(t *testing.T) {
			t.Parallel()
			exists := foundMigrations[name]
			if tc.shouldExist {
				assert.True(t, exists,
					"could not find expected %s file: %s",
					tc.description, name)
				return
			}

			assert.False(t, exists,
				"found unexpected %s file: %s",
				tc.description, name)
		})
	}
}

func TestAsset(t *testing.T) {
	t.<PERSON>()

	// check if each migration file's contents match expectations
	for file, tc := range map[string]struct {
		description    string
		shouldExist    bool
		expectedPhrase string
	}{
		"1_initial_database.up.sql": {
			description:    "initial up migration",
			shouldExist:    true,
			expectedPhrase: "CREATE TABLE",
		},
		"1_initial_database.down.sql": {
			description:    "initial down migration",
			shouldExist:    true,
			expectedPhrase: "DROP TABLE",
		},
		"nonexistent_file.sql": {
			description:    "non-existent file",
			shouldExist:    false,
			expectedPhrase: "",
		},
	} {
		t.Run(tc.description, func(t *testing.T) {
			t.Parallel()
			content, err := Asset(file)

			if !tc.shouldExist {
				assert.Error(t, err,
					"expected error when reading non-existent file %s, but got none",
					file)
				return
			}

			require.NoError(t, err, "failed to read migration file %s", file)
			require.NotEmpty(t, content, "migration file %s has no content", file)

			if tc.expectedPhrase != "" {
				assert.Contains(t, string(content), tc.expectedPhrase,
					"migration file %s does not contain expected phrase: %s",
					file, tc.expectedPhrase,
				)
			}
		})
	}
}
