// Package migrations provides database migration functionality and embedded
// migration files.
package migrations

import (
	"embed"
	"io/fs"
)

//go:embed *.sql
var migrationsFS embed.FS

// AssetNames returns the names of the embedded migration files
func AssetNames() []string {
	entries, err := fs.ReadDir(migrationsFS, ".")
	if err != nil {
		return nil
	}

	var names []string
	for _, entry := range entries {
		if !entry.IsDir() {
			names = append(names, entry.Name())
		}
	}
	return names
}

// Asset returns the content of the specified file
func Asset(name string) ([]byte, error) {
	return migrationsFS.ReadFile(name)
}

// FS returns a filesystem containing the embedded migration files
func FS() fs.FS {
	return migrationsFS
}
