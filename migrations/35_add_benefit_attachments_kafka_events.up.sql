CREATE TABLE events_benefit_attachment_v2beta1
(
    id                 uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),
    created_at         TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    business_unit      VARCHAR NOT NULL,
    customer_plan_id   VARCHAR NOT NULL,
    voucher_code       VARCHAR,

    raw                BYTEA   NOT NULL,
    metadata           JSONB
);

CREATE INDEX idx_events_benefit_attachment_v2beta1_business_unit_plan_id
    ON events_benefit_attachment_v2beta1(lower(business_unit), customer_plan_id);
