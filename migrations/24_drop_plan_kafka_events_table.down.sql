CREATE TABLE events_customer_plan_v1
(
    id                          uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),
    created_at                  TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    customer_uuid               VARCHAR NOT NULL,
    customer_plan_id            VARCHAR NOT NULL,
    subscription_established_at TIMESTAMP WITH TIME ZONE NOT NULL,

    raw                         BYTEA   NOT NULL,
    metadata                    JSONB
);

CREATE INDEX idx_events_customer_plan_v1_plan_id
    ON events_customer_plan_v1 (customer_plan_id);

CREATE INDEX idx_events_customer_plan_v1_created_at
    ON events_customer_plan_v1 (created_at);
