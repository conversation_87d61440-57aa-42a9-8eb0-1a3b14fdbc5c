CREATE TABLE events_customer_legacy_subscription_v1beta3
(
    id                 uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),
    created_at         TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    business_unit      VARCHAR NOT NULL,
    subscription_id    VARCHAR NOT NULL,
    customer_id        VARCHAR NOT NULL,
    customer_plan_id   VARCHAR NOT NULL,
    voucher_code       VARCHAR,

    raw                BYTEA   NOT NULL,
    metadata           JSONB
);

CREATE INDEX idx_events_customer_legacy_subscription_v1beta3_business_unit_p
    ON events_customer_legacy_subscription_v1beta3 (business_unit, customer_plan_id);

CREATE INDEX idx_events_customer_legacy_subscription_v1beta3_created_at
    ON events_customer_legacy_subscription_v1beta3 (created_at);

