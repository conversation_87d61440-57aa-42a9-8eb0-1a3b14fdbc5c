ALTER TABLE autocancel
ADD subscription_id int4,
ADD customer_uuid uuid,
ADD voucher VARCHAR(60),
ADD country VARCHAR(2),
ADD customer_id int8,
ADD postcode VARCHAR,
ADD product_list varchar[],
ADD credit_event_received bool default false NULL,
ADD subscription_event_received bool default false NULL,
ADD order_event_received bool default false NULL,
ADD voucher_event_received bool default false NULL,
ADD remaining_voucher_amount float;

ALTER TABLE autocancel ADD UNIQUE (customer_id, country);
ALTER TABLE autocancel ADD UNIQUE (subscription_id, country);
ALTER TABLE autocancel DROP CONSTRAINT autocancel_customer_plan_id_key;
