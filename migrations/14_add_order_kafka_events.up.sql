CREATE TABLE events_customer_order_v1
(
    id                 uuid PRIMARY KEY         DEFAULT uuid_generate_v4(),
    created_at         TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    business_unit      VARCHAR NOT NULL,
    customer_plan_id   VARCHAR NOT NULL,
    voucher_code       VARCHAR,

    raw                BYTEA   NOT NULL,
    metadata           JSONB
);

CREATE INDEX idx_events_customer_order_v1_business_unit_plan_id_voucher
    ON events_customer_order_v1 (business_unit, customer_plan_id, voucher_code);

CREATE INDEX idx_events_customer_order_v1_created_at
    ON events_customer_order_v1 (created_at);

