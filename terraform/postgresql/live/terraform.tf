# Backend definition
terraform {
  backend "s3" {
    bucket = "hf-terraform-live"
    key = "infrastructure/credit-autocancel-service-pg-live.tfstate"
    region = "eu-west-1"
  }
}

# Providers
provider "aws" {
  region = "eu-west-1"
}

provider "vault" {
  namespace = "services/credit-autocancel-service"
}

data "vault_generic_secret" "vault_secrets" {
  path = format("live/key-value/db")
}

data "aws_iam_role" "rds-monitoring" {
  name = "rds-monitoring-role"
}
