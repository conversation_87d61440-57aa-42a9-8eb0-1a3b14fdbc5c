# Modules
module "credit_autocancel_service" {
  source                       = "github.com/hellofresh/tf-hf-rds?ref=1.10.0"
  allocated_storage            = "1000"
  engine                       = "postgres"
  major_engine_version         = "13"
  engine_version               = "13.20"
  environment                  = "staging"
  instance_class               = "db.m6g.xlarge"
  name                         = "credit-autocancel-service"
  database_name                = "creditautocancelservicedb"
  password                     = data.vault_generic_secret.vault_secrets.data["password"]
  port                         = "5432"
  username                     = "creditautocancelservice"
  create_db_parameter_group    = false
  db_subnet_group_name         = "default"
  monitoring_interval          = 10
  monitoring_role_arn          = data.aws_iam_role.rds-monitoring.arn
  performance_insights_enabled = true
  deletion_protection          = true
  apply_immediately            = true
  allow_major_version_upgrade  = true
  storage_type                 = "gp3"
  iops                         = "12000"
  tags = {
    Environment = "Staging"
    Group       = "credit-autocancel-service"
    Squad       = "rte-platform"
    Tribe       = "rte-expansion"
    CostGroup   = "Expansion"
  }
}




