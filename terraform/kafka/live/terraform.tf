terraform {
  required_providers {
    kafka = {
      source  = "Mongey/kafka"
      version = "0.5.2"
    }
  }

  backend "s3" {
    bucket = "hf-terraform-live"
    key    = "infrastructure/credit-autocancel-service-kafka.tfstate"
    region = "eu-west-1"
  }
}

provider "kafka" {
  bootstrap_servers = ["kafka-live-hellofresh-live.aivencloud.com:23408"]

  ca_cert = data.vault_generic_secret.vault_kafka_secrets.data["ca"]
  client_cert = data.vault_generic_secret.vault_kafka_secrets.data["cert"]
  client_key = data.vault_generic_secret.vault_kafka_secrets.data["key"]
}

provider "aws" {
  region = "eu-west-1"
}
