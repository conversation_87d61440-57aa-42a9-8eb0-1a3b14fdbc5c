module "benthos-trigger-order-paid-topic" {
  source = "github.com/hellofresh/kafka-automation?depth=1//terraform-modules/private-topic"

  name                = "benthos.trigger-order-paid"
  partitions          = 16
  replication_factor  = 3
  cleanup_policy      = "delete"
  min_insync_replicas = 2
  retention_ms        = 1209600000 # 14 days
}

module "benthos-trigger-benefit-attachment-topic" {
  source = "github.com/hellofresh/kafka-automation?depth=1//terraform-modules/private-topic"

  name                = "benthos.trigger-benefit-attachment"
  partitions          = 16
  replication_factor  = 3
  cleanup_policy      = "delete"
  min_insync_replicas = 2
  retention_ms        = 1209600000 # 14 days
}
