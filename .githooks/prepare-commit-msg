#!/bin/bash
COMMIT_MSG_FILE=$1
COMMIT_SOURCE=$2
SHA1=$3
/usr/bin/perl -i.bak -ne 'print unless(m/^. Please enter the commit message/..m/^#$/)' "$COMMIT_MSG_FILE"
case "$COMMIT_SOURCE,$SHA1" in
  ,|template,)
    /usr/bin/perl -i.bak -pe '
       print "\n" . `git diff --cached --name-status -r`
     if /^#/ && $first++ == 0' "$COMMIT_MSG_FILE" ;;
  *) ;;
esac
 # get current branch
 branchName=`git rev-parse --abbrev-ref HEAD`
 branchName_uc=$(echo $branchName | tr '[:lower:]' '[:upper:]')
 # search jira issue id in a pattern such a "feature/ABC-123-description"
 # jiraId=$(echo $branchName_uc | sed -E 's,([A-Z]+-[0-9]+)-.+,\1,g')
 jiraId=$(echo $branchName_uc | grep -o -E '[^/]*\/([a-zA-Z]+-[0-9]+)' | grep -o -E '\/.*' | grep -o -E '[^/]+' )
 # only prepare commit message if pattern matched and jiraId was found
 if [[ ! -z $jiraId ]]; then
  # only add the jira ticket if it is not already there.
  matches=$(head -n1 "$COMMIT_MSG_FILE" | grep -c "\[$jiraId\]")
  if [[ $matches = 0 ]]; then
   sed -i.bak -e "1s/^/[$jiraId] /" "$COMMIT_MSG_FILE"
  fi
 fi
