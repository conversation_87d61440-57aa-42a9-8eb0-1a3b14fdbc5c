package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/hellofresh/growth-go-kit/logger"
	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/hellofresh/growth-go-kit/os/process"
	"github.com/redpanda-data/benthos/v4/public/service"
	_ "github.com/redpanda-data/connect/v4/public/components/all"
	"go.uber.org/zap"

	"github.com/hellofresh/credit-autocancel-service/internal/amqp/publisher"
	"github.com/hellofresh/credit-autocancel-service/internal/app/command"
	"github.com/hellofresh/credit-autocancel-service/internal/campaign"
	"github.com/hellofresh/credit-autocancel-service/internal/campaignsubscription"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/auth"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/balanceservice"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/priceservice"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/voucherservice"
	"github.com/hellofresh/credit-autocancel-service/internal/customer"
	"github.com/hellofresh/credit-autocancel-service/internal/database"
	"github.com/hellofresh/credit-autocancel-service/internal/subscription"
)

// These variables contain build information about the Version and a Buildstamp (timestamp, githash, etc.)
// they are injected by the build tools.
var (
	AppName    = "credit-autocancel-service-benthos"
	Version    = "0.0.0"
	Buildstamp = "dev"
)

var lint = flag.Bool("lint", false, "Run lint")

func main() {
	ctx, done := process.Init(AppName, Version, Buildstamp)
	defer done()

	cfg, err := getConfig()
	if err != nil {
		panic(fmt.Errorf("failed to get config: %w", err))
	}

	log, err := logger.InitLogger(func() logger.InitOption {
		if strings.EqualFold(cfg.AppEnv, "development") {
			return logger.WithLevel(logger.LevelDebug)
		}

		return logger.WithLevel(logger.LevelInfo)
	}())
	if err != nil {
		panic(fmt.Errorf("failed to init logger: %w", err))
	}

	// init routes
	ready := httputil.NewReady(
		httputil.TextHandler(http.StatusServiceUnavailable, "application/json", `"NOT READY"`),
	)
	mux := httputil.NewBaseMux(
		ready.Handler(httputil.TextHandler(http.StatusOK, "application/json", `"READY"`)),
	)

	// init view
	err = observability.InitView(
		ctx,
		&cfg.View,
		mux,
		"",
	)
	if err != nil {
		log.Fatal("error initializing view", zap.Error(err))
	}

	// init tracing
	err = observability.InitTracing(
		ctx,
		&cfg.Trace,
		"",
	)
	if err != nil {
		log.Fatal("error initializing tracing", zap.Error(err))
	}

	// run linter
	if *lint {
		os.Args = []string{
			os.Args[0],
			"lint",
			"./cmd/credit-autocancel-service-benthos/*.yaml",
		}

		service.RunCLI(ctx)
		return
	}

	// set up database
	log.Info("connecting to the database…")
	db, err := database.Connect(ctx, cfg.Database)
	if err != nil {
		log.Fatal("cannot initialize connection with database", zap.Error(err))
	}
	err = db.Ping()
	if err != nil {
		log.Fatal("could not ping database", zap.Error(err))
	}
	defer db.Close()

	// set up recordSubscriptionCreated handler
	voucherClient := voucherservice.NewClient(cfg.VoucherServiceURL)
	campaignRepo := campaign.NewRepo(db.DB)
	campaignSubscription := campaignsubscription.NewRepo(db.DB)

	var customerConvertedPublisher *publisher.OutboxPublisher
	if cfg.AMQPDSN != "" {
		customerConvertedPublisher = publisher.NewOutboxEmitter(db.DB, "topic", 10*time.Second)
		if err != nil {
			log.Fatal("cannot initialize publisher", zap.Error(err))
		}
	} else {
		log.Warn("ampq dsn is empty, outbox publishing is disabled")
	}

	subscriptionRepo := subscription.NewRepo(db.DB, customerConvertedPublisher)
	customerRepo := customer.NewRepo(db.DB)

	// Initialize Dependencies
	authenticator := auth.NewClient(cfg.AuthURL, auth.Credentials{
		ClientSecret: cfg.AuthClientSecret,
		ClientID:     cfg.AuthClientID,
		GrantType:    "client_credentials",
	})

	// =============================================================================
	jsonPublisher := publisher.NewJSONStructPublisher(customerConvertedPublisher)

	priceCalculator := priceservice.NewClient(cfg.PriceServiceURL)
	balanceClient := balanceservice.NewClient(cfg.BalanceServiceURL)

	benthosHandler, err := command.NewBenthosHandler(&command.BenthosHandlerParams{
		VoucherClient:          voucherClient,
		CampaignRepository:     campaignRepo,
		SubscriptionRepository: subscriptionRepo,
		CustomerRepository:     customerRepo,
		CampaignSubscription:   campaignSubscription,
		Logger:                 log,
		Authenticator:          authenticator,
		PriceService:           priceCalculator,
		BalanceService:         balanceClient,
		JSONPublisher:          jsonPublisher,
	})
	if err != nil {
		log.Fatal("failed to create benthos handler", zap.Error(err))
	}

	initModules(benthosHandler)

	httpServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Port),
		Handler: observability.NewHTTPHandler(mux),
	}

	log.Info("starting HTTP server", zap.String("address", httpServer.Addr))

	if kafkaCaCert := os.Getenv("KAFKA_CA_CERT"); os.Getenv("KAFKA_CA_CERT_FILE") == "" && kafkaCaCert != "" {
		kafkaCaCertFile := prepareKafkaCaCert(kafkaCaCert, "kafka-ca-cert-")
		defer os.Remove(kafkaCaCertFile) // nolint:errcheck

		os.Setenv("KAFKA_CA_CERT_FILE", kafkaCaCertFile) // nolint:errcheck
	}

	// start server
	go httputil.StartServer(httpServer, log)
	ready.Ready()

	// prepare parameters for Benthos and run it!
	os.Args = []string{
		os.Args[0],
		"--resources", "./cmd/credit-autocancel-service-benthos/resources.yaml",
		"--set", fmt.Sprintf("http.address=:%d", cfg.Port+1),
		"--config", "./cmd/credit-autocancel-service-benthos/benthos.yaml",
		"streams", "./cmd/credit-autocancel-service-benthos/streams/*.yaml",
	}

	log.Info("starting Benthos…", zap.Strings("args", os.Args))
	service.RunCLI(ctx)

	<-ctx.Done()
	log.Info("killing app")

	// allow some time to shut down everything gracefully
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// try shutting down the HTTP server gracefully
	err = httpServer.Shutdown(ctx)
	if err != nil {
		log.Fatal("failed to shutdown server", zap.Error(err))
	}

	log.Info("shutdown reached")
}
