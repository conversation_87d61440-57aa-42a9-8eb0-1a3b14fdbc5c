package main

import (
	"fmt"

	"github.com/redpanda-data/benthos/v4/public/service"
)

var (
	errEmptyKafkaPartition = fmt.Errorf("message missing kafka_partition")
	errEmptyKafkaTopic     = fmt.Errorf("message missing kafka_topic")
	errInvalidMap          = fmt.Errorf("failed to convert message to map")
	errEmptyCustomerPlanID = fmt.Errorf("customer_plan_id is empty")
)

func deduplicateBatch(batch service.MessageBatch) (map[string]map[string]interface{}, error) {
	messages := make(map[string]map[string]interface{})
	var previousPartitionID string

	// deduplicate batch
	for _, msg := range batch {
		// make sure we receive a partition id
		partitionID, _ := msg.MetaGet("kafka_partition")
		if partitionID == "" {
			return nil, errEmptyKafkaPartition
		}
		// make sure that all messages in the batch have the same partition ID, if not it means the batching has gone wrong
		if previousPartitionID != "" && previousPartitionID != partitionID {
			return nil, fmt.Errorf("message has different kafka_partition %s than the previous message %s", partitionID, previousPartitionID)
		}
		previousPartitionID = partitionID

		msgStruct, err := msg.AsStructured()
		if err != nil {
			return nil, fmt.Errorf("failed to convert message to structured: %w", err)
		}
		msgMap, ok := msgStruct.(map[string]interface{})
		if !ok {
			return nil, errInvalidMap
		}

		customerPlanID, _ := msgMap["customer_plan_id"].(string)
		if customerPlanID == "" {
			return nil, errEmptyCustomerPlanID
		}

		messages[customerPlanID] = msgMap
	}

	return messages, nil
}

func topicFromBatch(batch service.MessageBatch) (string, error) {
	if len(batch) == 0 {
		return "", errEmptyKafkaTopic
	}
	topic, _ := batch[0].MetaGet("kafka_topic")
	if topic == "" {
		return "", errEmptyKafkaTopic
	}
	return topic, nil
}
