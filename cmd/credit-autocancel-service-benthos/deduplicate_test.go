package main

import (
	"testing"

	protoCustomerOrderDelivery "github.com/hellofresh/schema-registry-go/stream/customer/order/delivery/v1"
	protoCustomerOrder "github.com/hellofresh/schema-registry-go/stream/customer/order/v1"
	"github.com/redpanda-data/benthos/v4/public/service"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/postaladdress"
)

func Test_deduplicateBatch(t *testing.T) {
	t.<PERSON>llel()

	deGiveMessage := createServiceMessage(
		t,
		"ced857e7-c983-5ccd-897d-57c13fb5c174",
		"1",
		orderPaidTopic,
		"de",
		"",
		"",
		&protoCustomerOrderDelivery.DeliveryDetailsValue{
			ShippingAddress: &postaladdress.PostalAddress{
				RegionCode: "de",
				PostalCode: postcode,
			},
		},
		&protoCustomerOrder.CustomerOrderValue{
			CustomerId: "4390f441-e017-42a4-8019-067546a1f2d4",
			PlanId:     "ced857e7-c983-5ccd-897d-57c13fb5c174",
			RegionCode: businessUnit,
			Items: []*protoCustomerOrder.CustomerOrderValue_Item{
				{
					ProductHandle: sku,
				},
			},
		},
		true,
	)
	deWantMessage := createMsgMap(
		t,
		"ced857e7-c983-5ccd-897d-57c13fb5c174",
		"de",
		"",
		"",
		&protoCustomerOrderDelivery.DeliveryDetailsValue{
			ShippingAddress: &postaladdress.PostalAddress{
				RegionCode: "de",
				PostalCode: postcode,
			},
		},
		&protoCustomerOrder.CustomerOrderValue{
			CustomerId: "4390f441-e017-42a4-8019-067546a1f2d4",
			PlanId:     "ced857e7-c983-5ccd-897d-57c13fb5c174",
			RegionCode: businessUnit,
			Items: []*protoCustomerOrder.CustomerOrderValue_Item{
				{
					ProductHandle: sku,
				},
			},
		},
	)
	nlNoOrderGiveMessage := createServiceMessage(
		t,
		"967f8fef-3299-4e70-a61f-8fe6b7a22420",
		"1",
		orderPaidTopic,
		"nl",
		"",
		"",
		nil,
		nil,
		true,
	)
	nlNoOrderWantMessage := createMsgMap(
		t,
		"967f8fef-3299-4e70-a61f-8fe6b7a22420",
		"nl",
		"",
		"",
		nil,
		nil,
	)
	usGiveMessage := createServiceMessage(
		t,
		"caed8953-2338-5037-8c14-2939c54d3704",
		"1",
		orderPaidTopic,
		"us",
		"",
		"",
		&protoCustomerOrderDelivery.DeliveryDetailsValue{
			ShippingAddress: &postaladdress.PostalAddress{
				RegionCode: businessUnit,
				PostalCode: postcode,
			},
		},
		&protoCustomerOrder.CustomerOrderValue{
			CustomerId: "e2899282-d51a-45c5-a041-d4c3e2ab4bfc",
			PlanId:     "caed8953-2338-5037-8c14-2939c54d3704",
			RegionCode: businessUnit,
			Items: []*protoCustomerOrder.CustomerOrderValue_Item{
				{
					ProductHandle: sku,
				},
			},
		},
		true,
	)
	usWantMessage := createMsgMap(
		t,
		"caed8953-2338-5037-8c14-2939c54d3704",
		"us",
		"",
		"",
		&protoCustomerOrderDelivery.DeliveryDetailsValue{
			ShippingAddress: &postaladdress.PostalAddress{
				RegionCode: businessUnit,
				PostalCode: postcode,
			},
		},
		&protoCustomerOrder.CustomerOrderValue{
			CustomerId: "e2899282-d51a-45c5-a041-d4c3e2ab4bfc",
			PlanId:     "caed8953-2338-5037-8c14-2939c54d3704",
			RegionCode: businessUnit,
			Items: []*protoCustomerOrder.CustomerOrderValue_Item{
				{
					ProductHandle: sku,
				},
			},
		},
	)
	fjGiveBenefitAttachmentMessage := createServiceMessage(
		t,
		"22c54e3c-1343-40f5-8e91-4ff48ffae787",
		"1",
		benefitAttachedTopic,
		"fj",
		"ABC123",
		"0",
		nil,
		nil,
		true,
	)
	fjWantBenefitAttachmentMessage := createMsgMap(
		t,
		"22c54e3c-1343-40f5-8e91-4ff48ffae787",
		"fj",
		"ABC123",
		"0",
		nil,
		nil,
	)

	tests := []struct {
		name    string
		give    service.MessageBatch
		want    map[string]map[string]interface{}
		wantErr string
	}{
		{
			name: "do nothing with empty batch",
			give: []*service.Message{},
			want: map[string]map[string]interface{}{},
		},
		{
			name: "no partition id error",
			give: []*service.Message{
				createServiceMessage(
					t,
					customerPlanID,
					"",
					orderPaidTopic,
					"us",
					"",
					"",
					delivery,
					order,
					true,
				),
			},
			wantErr: "message missing kafka_partition",
		},
		{
			name: "message not structured error",
			give: []*service.Message{
				createServiceMessage(
					t,
					customerPlanID,
					"1",
					orderPaidTopic,
					"us",
					"",
					"",
					delivery,
					order,
					false,
				),
			},
			wantErr: "failed to convert message to structured: target message part does not exist",
		},
		{
			name: "message not a map error",
			give: []*service.Message{
				createNonMapMessage(t, "us"),
			},
			wantErr: "failed to convert message to map",
		},
		{
			name: "no customer plan id error",
			give: []*service.Message{
				createServiceMessage(
					t,
					"",
					"1",
					orderPaidTopic,
					"us",
					"",
					"",
					delivery,
					order,
					true,
				),
			},
			wantErr: "customer_plan_id is empty",
		},
		{
			name: "return all messages if there are no duplicates",
			give: []*service.Message{
				deGiveMessage,
				usGiveMessage,
				nlNoOrderGiveMessage,
				fjGiveBenefitAttachmentMessage,
			},
			want: map[string]map[string]interface{}{
				"ced857e7-c983-5ccd-897d-57c13fb5c174": deWantMessage,
				"caed8953-2338-5037-8c14-2939c54d3704": usWantMessage,
				"967f8fef-3299-4e70-a61f-8fe6b7a22420": nlNoOrderWantMessage,
				"22c54e3c-1343-40f5-8e91-4ff48ffae787": fjWantBenefitAttachmentMessage,
			},
		},
		{
			name: "deduplicate any duplicates",
			give: []*service.Message{
				usGiveMessage,
				deGiveMessage,
				deGiveMessage,
				deGiveMessage,
				deGiveMessage,
				deGiveMessage,
				deGiveMessage,
				usGiveMessage,
			},
			want: map[string]map[string]interface{}{
				"ced857e7-c983-5ccd-897d-57c13fb5c174": deWantMessage,
				"caed8953-2338-5037-8c14-2939c54d3704": usWantMessage,
			},
		},
		{
			name: "return error if partition ids don't match",
			give: []*service.Message{
				deGiveMessage,
				createServiceMessage(
					t,
					"ced857e7-c983-5ccd-897d-57c13fb5c174",
					"2",
					orderPaidTopic,
					"de",
					"",
					"",
					delivery,
					order,
					true,
				),
				createServiceMessage(
					t,
					"caed8953-2338-5037-8c14-2939c54d3704",
					"3",
					orderPaidTopic,
					"de",
					"",
					"",
					delivery,
					order,
					true,
				),
			},
			wantErr: "message has different kafka_partition 2 than the previous message 1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			got, err := deduplicateBatch(tt.give)
			if tt.wantErr == "" {
				assert.NoError(t, err)
			} else {
				assert.ErrorContains(t, err, tt.wantErr)
			}
			matchMessages(t, tt.want, got)
		})
	}
}
