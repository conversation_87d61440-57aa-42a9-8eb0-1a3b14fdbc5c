package main

import (
	"context"
	"os"

	"github.com/hellofresh/growth-go-kit/logger"
	"go.uber.org/zap"
)

func prepareKafkaCaCert(kafkaCaCert, pattern string) string {
	kafkaCaCertFile, err := os.CreateTemp("", pattern)
	if err != nil {
		logger.From(context.Background()).Panic("error creating kafka ca cert temp file", zap.Error(err))
	}

	writeFile(kafkaCaCertFile, kafkaCaCert)

	closeFile(kafkaCaCertFile)

	return kafkaCaCertFile.Name()
}

func writeFile(f *os.File, s string) {
	_, err := f.WriteString(s)
	if err != nil {
		logger.From(context.Background()).Panic("error writing kafka ca cert temp file", zap.Error(err))
	}
}

func closeFile(f *os.File) {
	err := f.Close()
	if err != nil {
		logger.From(context.Background()).Panic("error closing kafka ca cert temp file", zap.Error(err))
	}
}
