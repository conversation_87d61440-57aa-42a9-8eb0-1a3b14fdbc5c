package main

import (
	"testing"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/credit-autocancel-service/internal/campaign"
	"github.com/hellofresh/credit-autocancel-service/internal/subscription"
	"github.com/redpanda-data/benthos/v4/public/service"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

var (
	benefitAttachedTopic = "credit-autocancel-service.benthos.trigger-benefit-attached"
	country              = "fr"
)

func Test_BenefitAttached_WriteBatch(t *testing.T) {
	t.Parallel()

	giveBenefitAttachment := service.MessageBatch{
		createServiceMessage(
			t,
			customerPlanID,
			partitionID,
			benefitAttachedTopic,
			country,
			voucherCode,
			"0",
			nil,
			nil,
			true,
		),
	}

	customerPlanUUID, err := uuid.FromString(customerPlanID)
	require.NoError(t, err)
	autoCancel := &subscription.AutoCancel{
		Country:        "fr",
		CustomerPlanID: uuid.NullUUID{UUID: customerPlanUUID, Valid: true},
		Voucher:        &voucherCode,
	}

	tests := []struct {
		name    string
		give    service.MessageBatch
		setup   func(*mockBenthosHandler)
		wantErr string
	}{
		{
			name: "missing kafka topic error",
			give: service.MessageBatch{
				createServiceMessage(
					t,
					customerPlanID,
					"1",
					"",
					businessUnit,
					"",
					"",
					nil,
					nil,
					true,
				),
			},
			wantErr: "message missing kafka_topic",
		},
		{
			name: "missing partition id error",
			give: service.MessageBatch{
				createServiceMessage(
					t,
					customerPlanID,
					"",
					benefitAttachedTopic,
					businessUnit,
					"",
					"",
					nil,
					nil,
					true,
				),
			},
			wantErr: "failed to deduplicate benefit attached batch: message missing kafka_partition",
		},
		{
			name: "error",
			give: giveBenefitAttachment,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleBenefitAttached", mock.Anything, autoCancel).Return(assert.AnError)
			},
			wantErr: "failed to process benefit attached: assert.AnError general error for testing",
		},
		{
			name: "empty voucher",
			give: service.MessageBatch{
				createServiceMessage(
					t,
					customerPlanID,
					partitionID,
					benefitAttachedTopic,
					country,
					"",
					"0",
					nil,
					nil,
					true,
				),
			},
		},
		{
			name: "failed benefit attached",
			give: service.MessageBatch{
				createServiceMessage(
					t,
					customerPlanID,
					partitionID,
					benefitAttachedTopic,
					country,
					voucherCode,
					"wrongwrongwrong",
					nil,
					nil,
					true,
				),
			},
		},
		{
			name: "voucher not part of campaign",
			give: giveBenefitAttachment,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleBenefitAttached", mock.Anything, autoCancel).Return(campaign.ErrVoucherNotPartOfCampaign)
			},
		},
		{
			name: "success",
			give: giveBenefitAttachment,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleBenefitAttached", mock.Anything, autoCancel).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockBenthosHandlerMock := newMockBenthosHandler(t)
			if tt.setup != nil {
				tt.setup(mockBenthosHandlerMock)
			}

			a := &hfProcessBenefitAttached{
				benthosHandler: mockBenthosHandlerMock,
			}

			ctx := t.Context()

			err := a.WriteBatch(ctx, tt.give)
			if tt.wantErr == "" {
				assert.NoError(t, err)
			} else {
				assert.EqualError(t, err, tt.wantErr)
			}
			require.NoError(t, a.Connect(ctx))
			require.NoError(t, a.Close(ctx))
		})
	}
}
