package main

import (
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestPrepareKafkaCaCert(t *testing.T) {
	t.<PERSON><PERSON>()

	assert.Contains(t, prepareKafkaCaCert("test cert", "kafka-ca-cert-"), "/kafka-ca-cert-")
}

func TestPrepareKafkaCaCertCreateError(t *testing.T) {
	t.<PERSON>()

	defer func() {
		err, ok := recover().(string)
		require.True(t, ok)
		require.Equal(t, err, "error creating kafka ca cert temp file")
	}()

	prepareKafkaCaCert("test cert", "/")
	require.NoError(t, errors.New("did not panic"))
}

func TestWrite(t *testing.T) {
	t.<PERSON>()

	defer func() {
		err, ok := recover().(string)
		require.True(t, ok)
		require.Equal(t, err, "error writing kafka ca cert temp file")
	}()

	writeFile(nil, "test cert")
	require.NoError(t, errors.New("did not panic"))
}

func TestClose(t *testing.T) {
	t.<PERSON>()

	defer func() {
		err, ok := recover().(string)
		require.True(t, ok)
		require.Equal(t, err, "error closing kafka ca cert temp file")
	}()

	closeFile(nil)
	require.NoError(t, errors.New("did not panic"))
}
