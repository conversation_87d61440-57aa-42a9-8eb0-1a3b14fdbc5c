// nolint:revive
package main

import (
	"strings"

	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/kelseyhightower/envconfig"

	"github.com/hellofresh/credit-autocancel-service/internal/database"
)

// config represents config with variables needed for an app
type config struct {
	Port              int         `envconfig:"PORT"            default:"9090"`
	AppEnv            string      `envconfig:"APP_ENV"         default:"development"`
	Kafka             kafkaConfig `envconfig:"KAFKA"`
	VoucherServiceURL string      `envconfig:"VOUCHER_SERVICE_URL" default:"http://voucher-service.staging-k8s.hellofresh.io"`
	AMQPDSN           string      `envconfig:"AMQP_DSN"`
	AuthURL           string      `envconfig:"AUTH_URL" default:"http://auth-service.staging-k8s.hellofresh.io"`
	PriceServiceURL   string      `envconfig:"PRICE_SERVICE_URL" default:"http://price-service.staging-k8s.hellofresh.io"`
	BalanceServiceURL string      `envconfig:"BALANCE_SERVICE_URL" default:"http://balance-service.staging-k8s.hellofresh.io"`
	AuthClientID      string      `envconfig:"CLIENT_ID" required:"true"`
	AuthClientSecret  string      `envconfig:"CLIENT_SECRET" required:"true"`

	Database database.Config
	Trace    observability.TraceConfig
	View     observability.ViewConfig
}

// getConfig returns config, filled from environment variables
func getConfig() (*config, error) {
	var cfg config
	err := envconfig.Process("", &cfg)
	if err != nil {
		return nil, err
	}

	return &cfg, nil
}

// kafkaConfig represents the kafka configuration required for the kafka consumer
type kafkaConfig struct {
	DSN           string `envconfig:"DSN"`
	Username      string `envconfig:"USERNAME"`
	Password      string `envconfig:"PASSWORD"`
	SaslMechanism string `envconfig:"SASL_MECHANISM" default:"PLAIN"`
	CaCert        string `envconfig:"CA_CERT"`
}

// translateNoneSaslMechanism translates "none", which bypasses envconfig default, to "", which growth-go-kit/kafka expects
func (cfg kafkaConfig) translateNoneSaslMechanism() string {
	if strings.ToLower(cfg.SaslMechanism) == "none" {
		return ""
	}
	return cfg.SaslMechanism
}
