package main

import (
	"os"
	"strings"
	"testing"

	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/hellofresh/credit-autocancel-service/internal/database"
)

// nolint: paralleltest // this test is not parallel because it sets env vars
func TestGetConfigError(t *testing.T) {
	env := os.Environ()
	os.Clearenv()
	t.Cleanup(func() {
		os.Clearenv()
		for _, v := range env {
			parts := strings.SplitN(v, "=", 2)
			assert.NoError(t, os.Setenv(parts[0], parts[1]))
		}
	})

	t.Setenv("PORT", "eighty")
	_, err := getConfig()
	require.EqualError(t, err, "envconfig.Process: assigning PORT to Port: converting 'eighty' to type int. details: strconv.ParseInt: parsing \"eighty\": invalid syntax")
}

// nolint: paralleltest // this test is not parallel because it sets env vars
func TestGetConfig(t *testing.T) {
	env := os.Environ()
	os.Clearenv()
	t.Cleanup(func() {
		os.Clearenv()
		for _, v := range env {
			parts := strings.SplitN(v, "=", 2)
			assert.NoError(t, os.Setenv(parts[0], parts[1]))
		}
	})

	setGlobalConfigEnv(t)

	got, err := getConfig()
	require.NoError(t, err)
	assert.Equal(
		t,
		&config{
			Port:   9090,
			AppEnv: "development",
			Kafka: kafkaConfig{
				SaslMechanism: "PLAIN",
			},
			View: observability.ViewConfig{
				EnablePrometheus: true,
			},
			Trace: observability.TraceConfig{
				EnableOTLPGRPC:     true,
				OTLPHost:           "localhost",
				OTLPGRPCPort:       "4317",
				OTLPHTTPPort:       "4318",
				SamplerProbability: 1,
			},
			Database: database.Config{
				DBHost:          "postgres",
				DBPort:          5430,
				DBUser:          "user",
				DBPassword:      "fresh4you",
				DBName:          "credit_autocancel_db",
				Retries:         5,
				MaxOpenConns:    20,
				MaxIdleConns:    20,
				MaxConnLifetime: 600000000000,
			},
			VoucherServiceURL: "http://voucher-service.staging-k8s.hellofresh.io",
			AuthURL:           "http://auth-service.staging-k8s.hellofresh.io",
			PriceServiceURL:   "http://price-service.staging-k8s.hellofresh.io",
			BalanceServiceURL: "http://balance-service.staging-k8s.hellofresh.io",
			AuthClientID:      "client_id",
			AuthClientSecret:  "client_secret",
		},
		got,
	)
}

func TestTranslateNoneSaslMechanism(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name string
		give kafkaConfig
		want string
	}{
		{
			name: "plain returns plain",
			give: kafkaConfig{
				SaslMechanism: "PLAIN",
			},
			want: "PLAIN",
		},
		{
			name: "none returns empty string",
			give: kafkaConfig{
				SaslMechanism: "none",
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			assert.Equal(t, tt.want, tt.give.translateNoneSaslMechanism())
		})
	}
}

// setGlobalConfigEnv sets the environment variables to the given strings
// nolint:usetesting
func setGlobalConfigEnv(t *testing.T) {
	t.Helper()

	assert.NoError(t, os.Setenv("DB_HOST", "postgres"))
	assert.NoError(t, os.Setenv("DB_PORT", "5430"))
	assert.NoError(t, os.Setenv("DB_USER", "user"))
	assert.NoError(t, os.Setenv("DB_NAME", "credit_autocancel_db"))
	assert.NoError(t, os.Setenv("DB_PASSWORD", "fresh4you"))
	assert.NoError(t, os.Setenv("CLIENT_ID", "client_id"))
	assert.NoError(t, os.Setenv("CLIENT_SECRET", "client_secret"))

	// viewconfig
	assert.NoError(t, os.Setenv("ENABLE_PROMETHEUS", "true"))

	// traceconfig
	assert.NoError(t, os.Setenv("ENABLE_OTLP_GRPC", "true"))
	assert.NoError(t, os.Setenv("OTLP_EXPORTER_HOST", "localhost"))
	assert.NoError(t, os.Setenv("OTLP_EXPORTER_GRPC_PORT", "4317"))
	assert.NoError(t, os.Setenv("OTLP_EXPORTER_HTTP_PORT", "4318"))
	assert.NoError(t, os.Setenv("SAMPLE_PROBABILITY", "1"))
}
