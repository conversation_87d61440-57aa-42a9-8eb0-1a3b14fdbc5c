// Code generated by mockery v2.53.3. DO NOT EDIT.

package main

import (
	context "context"

	subscription "github.com/hellofresh/credit-autocancel-service/internal/subscription"
	mock "github.com/stretchr/testify/mock"
)

// mockBenthosHandler is an autogenerated mock type for the benthosHandler type
type mockBenthosHandler struct {
	mock.Mock
}

// HandleBenefitAttached provides a mock function with given fields: ctx, autoCancel
func (_m *mockBenthosHandler) HandleBenefitAttached(ctx context.Context, autoCancel *subscription.AutoCancel) error {
	ret := _m.Called(ctx, autoCancel)

	if len(ret) == 0 {
		panic("no return value specified for HandleBenefitAttached")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *subscription.AutoCancel) error); ok {
		r0 = rf(ctx, autoCancel)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// HandleOrderPaid provides a mock function with given fields: ctx, autoCancel
func (_m *mockBenthosHandler) HandleOrderPaid(ctx context.Context, autoCancel *subscription.AutoCancel) error {
	ret := _m.Called(ctx, autoCancel)

	if len(ret) == 0 {
		panic("no return value specified for HandleOrderPaid")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *subscription.AutoCancel) error); ok {
		r0 = rf(ctx, autoCancel)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// newMockBenthosHandler creates a new instance of mockBenthosHandler. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newMockBenthosHandler(t interface {
	mock.TestingT
	Cleanup(func())
}) *mockBenthosHandler {
	mock := &mockBenthosHandler{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
