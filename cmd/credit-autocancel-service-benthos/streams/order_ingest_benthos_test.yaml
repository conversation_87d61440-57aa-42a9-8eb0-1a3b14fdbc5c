tests:
  - name: test order ingest pipeline
    target_processors: '/pipeline/processors'
    mocks:
      unit_test_mock:
        bloblang: |
          root = content().decode("base64").string()
          meta kafka_key = meta("kafka_key").decode("base64").string()
    input_batch:
      - content: 'CO/e7gcQkb+tiwEYgrccIiQ1ZDQ3NzA0ZS0yZDE0LTQ4YWMtODZkMy1jMDczMDY1MDQ5ZjEqAkRFMgcI6A8QARgBOgcKA0VVUhAeQg0KA0VVUhAFGIDniNgDSg0KA0VVUhAFGIDniNgDUgUKA0VVUlpICgpERTE2NTAyMTUwEg5ERS1DQlQxNi0zLTItMBoHCgNFVVIQKCIHCgNFVVIQHioFCgNFVVIyBwoDRVVSEAo6BkFVVE9RQUAFaAFyJGEwODkzODQ3LWMzYzctNDliMC04ZTY4LWQ2NzMzMGRkNTEzNw==' #gitleaks:allow
        metadata:
          kafka_topic: 'public.customer.order.v1'
          kafka_key: 'CgJkZRIkNmI2NThmNzktODcyOC01YTk5LWIwMDktNGUwY2JiMjJjY2E5' #gitleaks:allow
    output_batches:
      -
        - # public.customer.order.v1 event
          content_equals: '{"business_unit":"de","customer_plan_id":"a0893847-c3c7-49b0-8e68-d67330dd5137","order_uuid":"6b658f79-8728-5a99-b009-4e0cbb22cca9"}'
          metadata_equals:
            hf_business_unit: 'de'
            hf_order_uuid: '6b658f79-8728-5a99-b009-4e0cbb22cca9'
            hf_customer_plan_id: 'a0893847-c3c7-49b0-8e68-d67330dd5137'
            hf_order_voucher_code: 'AUTOQA'
            hf_is_subscription_initial_order: "true"
          bloblang:
            'meta("hf_raw_content").length() > 0'
