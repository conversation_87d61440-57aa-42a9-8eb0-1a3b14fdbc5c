input:
  kafka:
    addresses:
      - ${KAFKA_DSN}
    tls:
      enabled: ${TLS_ENABLED:true}
      root_cas_file: ${KAFKA_CA_CERT_FILE}
    sasl:
      mechanism: ${KAFKA_SASL_MECHANISM:PLAIN}
      user: ${KAFKA_USERNAME}
      password: ${KAFKA_PASSWORD}
    target_version: 3.4.1
    topics:
      - public.customer.order.delivery.v1
    consumer_group: ${BENTHOS_KAFKA_CONSUMER_GROUP_INGEST:credit-autocancel-service-benthos-development}
    start_from_oldest: ${BENTHOS_KAFKA_START_FROM_OLDEST:false}
    max_processing_period: 10m
    checkpoint_limit: 400 # per partition
    batching: # per partition
      count: ${BENTHOS_KAFKA_INGEST_BATCHING_COUNT:400}
      period: 1m

pipeline:
  processors:
    - label: "unit_test_mock"
      noop: {}
    - hf_protobuf_parser: {}
    - log:
        level: DEBUG
        message: 'received message from topic: ${! meta("kafka_topic") }'
        fields_mapping: |-
          root.business_unit = meta("hf_business_unit")
          root.order_uuid = meta("hf_order_uuid")
    - label: "store_content_in_meta"
      bloblang: |-
        meta hf_raw_content = content().bytes()
    - label: "create_trigger_message"
      bloblang: '{"business_unit": meta("hf_business_unit"), "order_uuid": meta("hf_order_uuid")}'

output:
  label: check_metadata
  switch:
    cases:
      - check: (meta("hf_business_unit") | "") != "" && (meta("hf_order_uuid") | "") != ""
        output:
          label: insert_and_publish
          broker:
            pattern: fan_out_sequential
            outputs:
              - label: insert_order_delivery_event_into_db
                sql_insert:
                  driver: postgres
                  dsn: postgres://${DB_USER:credit-autocancel-service}:${DB_PASSWORD:freshforyou}@${DB_HOST:postgres}:${DB_PORT:5432}/${DB_NAME:creditautocanceldb}?sslmode=disable
                  max_in_flight: 6
                  table: events_customer_order_delivery_v1
                  columns: [ business_unit, order_uuid, raw, metadata ]
                  args_mapping: |
                    root = [
                      meta("hf_business_unit"),
                      meta("hf_order_uuid"),
                      meta("hf_raw_content").bytes(),
                      meta().filter(item -> !item.key.has_prefix("hf_")).string()
                    ]
              - label: publish_order_paid
                kafka:
                  addresses:
                    - ${KAFKA_DSN}
                  tls:
                    enabled: ${TLS_ENABLED:true}
                    root_cas_file: ${KAFKA_CA_CERT_FILE}
                  sasl:
                    mechanism: ${KAFKA_SASL_MECHANISM:PLAIN}
                    user: ${KAFKA_USERNAME}
                    password: ${KAFKA_PASSWORD}
                  metadata:
                    exclude_prefixes: [ "" ] # do not forward any of the metadata fields
                  target_version: 3.4.1
                  topic: credit-autocancel-service.benthos.trigger-order-paid
                  key: ${! (meta("hf_business_unit") + meta("hf_order_uuid")).hash("xxhash64") }
      - output:
          processors:
            - log:
                level: ERROR
                message: 'dropping message because it is missing data'
                fields_mapping: |-
                  root.topic = meta("kafka_topic")
                  root.partition = meta("kafka_partition")
                  root.offset = meta("kafka_offset")
                  root.business_unit = meta("hf_business_unit")
                  root.order_uuid = meta("hf_order_uuid")
          drop: {}

