tests:
  - name: test order payment ingest pipeline
    target_processors: '/pipeline/processors'
    mocks:
      unit_test_mock:
        bloblang: |
          root = content().decode("base64").string()
          meta kafka_key = meta("kafka_key").decode("base64").string()
    input_batch:
      - content: 'CAEQAhiXs+QBIAEqJDVkODc2ZDUxLTE3ZjgtNDVkNS1iOGMwLTNkMzNhOGUzODUzZQ==' #gitleaks:allow
        metadata:
          kafka_topic: 'public.customer.order.payment.v1'
          kafka_key: 'CgJ1cxIkY2FlZDg5NTMtMjMzOC01MDM3LThjMTQtMjkzOWM1NGQzNzA0' #gitleaks:allow
    output_batches:
      -
        - # public.customer.order.payment.v1 event
          content_equals: '{"business_unit":"us","order_uuid":"caed8953-2338-5037-8c14-2939c54d3704"}'
          metadata_equals:
            hf_business_unit: 'us'
            hf_order_uuid: 'caed8953-2338-5037-8c14-2939c54d3704'
            hf_payment_state: '1'
          bloblang:
            'meta("hf_raw_content").length() > 0'
