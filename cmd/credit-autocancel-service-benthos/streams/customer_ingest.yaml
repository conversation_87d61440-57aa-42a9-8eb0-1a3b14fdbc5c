input:
  kafka:
    addresses:
      - ${KAFKA_DSN}
    tls:
      enabled: ${TLS_ENABLED:true}
      root_cas_file: ${KAFKA_CA_CERT_FILE}
    sasl:
      mechanism: ${KAFKA_SASL_MECHANISM:PLAIN}
      user: ${KAFKA_USERNAME}
      password: ${KAFKA_PASSWORD}
    target_version: 3.4.1
    topics:
      - public.customer.v1beta2
    consumer_group: ${BENTHOS_KAFKA_CONSUMER_GROUP_INGEST:credit-autocancel-service-benthos-development}-v2
    start_from_oldest: ${BENTHOS_KAFKA_START_FROM_OLDEST:false}
    max_processing_period: 10m
    checkpoint_limit: 400 # per partition
    batching: # per partition
      count: ${BENTHOS_KAFKA_INGEST_BATCHING_COUNT:400}
      period: 1m

pipeline:
  processors:
    # This processor does nothing but exists to be mocked in unit-tests to convert base64 encoded protobuf messages to strings.
    - label: "unit_test_mock"
      noop: {}
    - label: deduplicate_customer
      dedupe:
        cache: customer_key_cache
        key: ${! meta("kafka_key") }
    - label: delete_customer_keys_from_cache
      cache:
        resource: customer_key_cache
        operator: delete
        key: ${! meta("kafka_key") }
    - hf_protobuf_parser: {}
    - log:
        level: DEBUG
        message: 'received message from topic: ${! meta("kafka_topic") }'
        fields_mapping: |-
          root.business_unit = meta("hf_business_unit")
          root.customer_id = meta("hf_customer_id")
          root.customer_uuid = meta("hf_customer_uuid")
    - label: "create_trigger_message"
      bloblang: '{"business_unit": meta("hf_business_unit"), "customer_id": meta("hf_customer_id"), "customer_uuid": meta("hf_customer_uuid")}'

output:
  label: check_metadata
  switch:
    cases:
      - check: ((meta("hf_business_unit") | "") != "" && (meta("hf_customer_uuid") | "") != "")
        output:
          label: insert_and_publish
          broker:
            pattern: fan_out_sequential
            outputs:
              - label: insert_customer_event_into_db
                sql_insert:
                  driver: postgres
                  dsn: postgres://${DB_USER:credit-autocancel-service}:${DB_PASSWORD:freshforyou}@${DB_HOST:postgres}:${DB_PORT:5432}/${DB_NAME:creditautocanceldb}?sslmode=disable
                  max_in_flight: 32
                  table: events_customer_v1beta2
                  columns: [ business_unit, customer_id, customer_uuid, metadata ]
                  args_mapping: |
                    root = [
                      meta("hf_business_unit"),
                      meta("hf_customer_id"),
                      meta("hf_customer_uuid"),
                      meta().filter(item -> !item.key.has_prefix("hf_")).string()
                    ]
      - output:
          label: handle_missing_metadata
          processors:
            - log:
                level: ERROR
                message: 'dropping message because it is missing data'
                fields_mapping: |-
                  root.topic = meta("kafka_topic")
                  root.partition = meta("kafka_partition")
                  root.offset = meta("kafka_offset")
                  root.business_unit = meta("hf_business_unit")
                  root.customer_uuid = meta("hf_customer_uuid")
          drop: {}
