input:
  kafka:
    addresses:
      - ${KAFKA_DSN}
    tls:
      enabled: ${TLS_ENABLED:true}
      root_cas_file: ${KAFKA_CA_CERT_FILE}
    sasl:
      mechanism: ${KAFKA_SASL_MECHANISM:PLAIN}
      user: ${KAFKA_USERNAME}
      password: ${KAFKA_PASSWORD}
    target_version: 3.4.1
    topics:
      - credit-autocancel-service.benthos.trigger-benefit-attachment
    consumer_group: ${BENTHOS_KAFKA_CONSUMER_GROUP_BENEFIT_ATTACHED:credit-autocancel-service-benthos-benefit-attached-development}
    start_from_oldest: ${BENTHOS_KAFKA_START_FROM_OLDEST:false}
    max_processing_period: 10m
    checkpoint_limit: 1 # per partition
    batching: # per partition
      count: ${BENTHOS_KAFKA_BATCHING_COUNT_ORDER:1500}
      period: 1m

pipeline:
  processors:
    - label: deduplicate_benefit_attached
      dedupe:
        cache: benefit_attached_key_cache
        key: ${! meta("kafka_key") }
    - label: delete_benefit_attached_keys_from_cache
      cache:
        resource: benefit_attached_key_cache
        operator: delete
        key: ${! meta("kafka_key") }
    - label: "find_autocancel"
      branch:
        processors:
          - label: query_autocancel
            sql_raw:
              driver: postgres
              dsn: postgres://${DB_USER:credit-autocancel-service}:${DB_PASSWORD:freshforyou}@${DB_HOST:postgres}:${DB_PORT:5432}/${DB_NAME:creditautocanceldb}?sslmode=disable
              conn_max_open: 32
              conn_max_idle: 32
              conn_max_life_time: 4h
              query: |
                SELECT 1
                FROM autocancel ac
                WHERE ac.customer_plan_id = $1 AND ac.status != 'running'
                ;
              args_mapping: |
                [
                  this.customer_plan_id
                ]
        result_map: |-
          root.find_autocancel = this.or([])
    - label: "delete_if_autocancel"
      bloblang: |-
        root = if this.find_autocancel.length() > 0 {
          deleted()
        }
    - label: "log_benefit_attached_event"
      log:
        level: DEBUG
        message: 'received attachment event for benefit attached'
        fields_mapping: |-
          root.business_unit = this.business_unit
          root.customer_plan_id = this.customer_plan_id
          root.voucher_code = this.voucher_code
          root.partition = meta("kafka_partition")
          root.error_code = this.error_code
output:
  switch:
    retry_until_success: false
    cases:
      # reject errored messages, will be retried from the beginning
      - check: errored()
        output:
          reject: "Message failed due to: ${! error() }"
      - output:
          hf_process_benefit_attached:
            max_in_flight: 16 # same as partitions on topic
