tests:
  - name: test order delivery ingest pipeline
    target_processors: '/pipeline/processors'
    mocks:
      unit_test_mock:
        bloblang: |
          root = content().decode("base64").string()
          meta kafka_key = meta("kafka_key").decode("base64").string()
    input_batch:
      - content: 'ChhUZXN0IFVzZXIgQXV0b21hdGVkIFRlc3QSDSszMTIxMjY4MzQ5ODgaB05MLTYtTlAiBwjmDxAHGB4qOBICTkwiBjM3NzNBTjoJQmFybmV2ZWxkSg5MYW5nZSBWb3JlbiAzM0oLTGFuZ2UgVm9yZW5KAjMzMAE6HkluZGllbiBuaWV0IHRodWlzIHZvb3IgZGUgZGV1cg==' #gitleaks:allow
        metadata:
          kafka_topic: 'public.customer.order.delivery.v1'
          kafka_key: 'CgJubBIkMTY1Zjg5YjEtYTc2Mi01YTBiLWJiOTMtMTY1N2UzZjM5MzBk' #gitleaks:allow
    output_batches:
      -
        - # public.customer.order.delivery.v1 event
          content_equals: '{"business_unit":"nl","order_uuid":"165f89b1-a762-5a0b-bb93-1657e3f3930d"}'
          metadata_equals:
            hf_business_unit: 'nl'
            hf_order_uuid: '165f89b1-a762-5a0b-bb93-1657e3f3930d'
          bloblang:
            'meta("hf_raw_content").length() > 0'
