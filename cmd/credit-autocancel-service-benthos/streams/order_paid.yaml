input:
  kafka:
    addresses:
      - ${KAFKA_DSN}
    tls:
      enabled: ${TLS_ENABLED:true}
      root_cas_file: ${KAFKA_CA_CERT_FILE}
    sasl:
      mechanism: ${KAFKA_SASL_MECHANISM:PLAIN}
      user: ${KAFKA_USERNAME}
      password: ${KAFKA_PASSWORD}
    target_version: 3.4.1
    topics:
      - credit-autocancel-service.benthos.trigger-order-paid
    consumer_group: ${BENTHOS_KAFKA_CONSUMER_GROUP_ORDER_PAID:credit-autocancel-service-benthos-order-paid-development}
    start_from_oldest: ${BENTHOS_KAFKA_START_FROM_OLDEST:false}
    max_processing_period: 10m
    checkpoint_limit: 1 # per partition
    batching: # per partition
      count: ${BENTHOS_KAFKA_BATCHING_COUNT_ORDER:1500}
      period: 1m

pipeline:
  processors:
    - label: deduplicate_order_paid
      dedupe:
        cache: order_paid_key_cache
        key: ${! meta("kafka_key") }
    - label: delete_order_paid_keys_from_cache
      cache:
        resource: order_paid_key_cache
        operator: delete
        key: ${! meta("kafka_key") }
    - label: "find_payment_event"
      branch:
        processors:
          - label: query_payment_event
            sql_raw:
              driver: postgres
              dsn: postgres://${DB_USER:credit-autocancel-service}:${DB_PASSWORD:freshforyou}@${DB_HOST:postgres}:${DB_PORT:5432}/${DB_NAME:creditautocanceldb}?sslmode=disable
              conn_max_open: 32
              conn_max_idle: 32
              conn_max_life_time: 4h
              query: |
                SELECT 1
                FROM events_customer_order_payment_v1 e
                WHERE lower(e.business_unit) = lower($1)
                AND lower(e.order_uuid) = lower($2)
                AND e.payment_state = 3 /* paid */
                ORDER BY CAST(e.metadata->>'kafka_offset' AS bigint) DESC
                LIMIT 1
                ;
              args_mapping: |
                [
                  this.business_unit,
                  this.order_uuid
                ]
        result_map: |-
          root.find_payment = this.or([])
    - label: "delete_if_no_payment_events"
      bloblang: |-
        root = if this.find_payment.length() == 0 {
          deleted()
        }
    - label: "log_found_payment_events"
      log:
        level: DEBUG
        message: 'found payment event'
        fields_mapping: |-
          root.business_unit = this.business_unit
          root.order_uuid = this.order_uuid
          root.partition = meta("kafka_partition")
    - label: "find_delivery_event"
      branch:
        processors:
          - label: query_delivery_event
            sql_raw:
              driver: postgres
              dsn: postgres://${DB_USER:credit-autocancel-service}:${DB_PASSWORD:freshforyou}@${DB_HOST:postgres}:${DB_PORT:5432}/${DB_NAME:creditautocanceldb}?sslmode=disable
              conn_max_open: 32
              conn_max_idle: 32
              conn_max_life_time: 4h
              query: |
                SELECT e.raw
                FROM events_customer_order_delivery_v1 e
                WHERE lower(e.business_unit) = lower($1)
                AND lower(e.order_uuid) = lower($2)
                ORDER BY CAST(e.metadata->>'kafka_offset' AS bigint) DESC
                LIMIT 1
                ;
              args_mapping: |
                [
                  this.business_unit,
                  this.order_uuid
                ]
        result_map: |-
          root.find_delivery = this.or([])
          root.delivery_raw = this.0.raw.or("").bytes()
    - label: "delete_if_no_delivery_events"
      bloblang: |-
        root = if this.find_delivery.length() == 0 {
          deleted()
        }
    - label: "log_found_delivery_events"
      log:
        level: DEBUG
        message: 'found delivery event for order paid'
        fields_mapping: |-
          root.business_unit = this.business_unit
          root.order_uuid = this.order_uuid
          root.partition = meta("kafka_partition")
    - label: "find_order_event"
      branch:
        processors:
          - label: query_order_event
            sql_raw:
              driver: postgres
              dsn: postgres://${DB_USER:credit-autocancel-service}:${DB_PASSWORD:freshforyou}@${DB_HOST:postgres}:${DB_PORT:5432}/${DB_NAME:creditautocanceldb}?sslmode=disable
              conn_max_open: 32
              conn_max_idle: 32
              conn_max_life_time: 4h
              query: |
                SELECT  e.customer_plan_id,
                        e.voucher_code,
                        e.raw
                FROM events_customer_order_v1 e
                WHERE lower(e.business_unit) = lower($1)
                  AND lower(e.order_uuid) = lower($2)
                ORDER BY CAST(e.metadata->>'kafka_offset' AS bigint) DESC
                LIMIT 1
                ;
              args_mapping: |
                [
                  this.business_unit,
                  this.order_uuid
                ]
        result_map: |-
          root.customer_plan_id = this.0.customer_plan_id.or("")
          root.voucher_code = this.0.voucher_code.or("")
          root.order_raw = this.0.raw.or("").bytes()
    - label: "delete_if_no_order"
      bloblang: |-
        root = if this.customer_plan_id == "" {
          deleted()
        }
    - label: "log_found_order_event"
      log:
        level: DEBUG
        message: 'found order event for order paid'
        fields_mapping: |-
          root.customer_plan_id = this.customer_plan_id
          root.voucher_code = this.voucher_code
          root.business_unit = this.business_unit
          root.partition = meta("kafka_partition")
    - label: "find_autocancel"
      branch:
        processors:
          - label: query_autocancel
            sql_raw:
              driver: postgres
              dsn: postgres://${DB_USER:credit-autocancel-service}:${DB_PASSWORD:freshforyou}@${DB_HOST:postgres}:${DB_PORT:5432}/${DB_NAME:creditautocanceldb}?sslmode=disable
              conn_max_open: 32
              conn_max_idle: 32
              conn_max_life_time: 4h
              query: |
                SELECT 1
                FROM autocancel ac
                WHERE ac.customer_plan_id = $1 AND ac.voucher_code = $2 AND ac.status != 'running'
                ;
              args_mapping: |
                [
                  this.customer_plan_id,
                  this.voucher_code
                ]
        result_map: |-
          root.find_autocancel = this.or([])
    - label: "delete_if_autocancel"
      bloblang: |-
        root = if this.find_autocancel.length() > 0 {
          deleted()
        }
    - label: "log_found_order_paid_event"
      log:
        level: DEBUG
        message: 'received order event for order paid'
        fields_mapping: |-
          root.business_unit = this.business_unit
          root.customer_plan_id = this.customer_plan_id
          root.voucher_code = this.voucher_code
          root.order_uuid = this.order_uuid.or("")
          root.partition = meta("kafka_partition")

output:
  switch:
    retry_until_success: false
    cases:
      # reject errored messages, will be retried from the beginning
      - check: errored()
        output:
          reject: "Message failed due to: ${! error() }"

      - output:
          hf_process_order_paid:
            max_in_flight: 16 # same as partitions on topic
