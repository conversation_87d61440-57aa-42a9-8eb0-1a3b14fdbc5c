input:
  kafka:
    addresses:
      - ${KAFKA_DSN}
    tls:
      enabled: ${TLS_ENABLED:true}
      root_cas_file: ${KAFKA_CA_CERT_FILE}
    sasl:
      mechanism: ${KAFKA_SASL_MECHANISM:PLAIN}
      user: ${KAFKA_USERNAME}
      password: ${KAFKA_PASSWORD}
    target_version: 3.4.1
    topics:
      - public.customer.benefit.attachment.v2beta1
    consumer_group: ${BENTHOS_KAFKA_CONSUMER_GROUP_INGEST:credit-autocancel-service-benthos-development}
    start_from_oldest: ${BENTHOS_KAFKA_START_FROM_OLDEST:false}
    max_processing_period: 10m
    checkpoint_limit: 400 # per partition
    batching: # per partition
      count: ${BENTHOS_KAFKA_INGEST_BATCHING_COUNT:400}
      period: 1m

pipeline:
  processors:
    # This processor does nothing but exists to be mocked in unit-tests to convert base64 encoded protobuf messages to strings.
    - label: "unit_test_mock"
      noop: {}
    - hf_protobuf_parser: {}
    - log:
        level: DEBUG
        message: 'received message from topic: ${! meta("kafka_topic") }'
        fields_mapping: |-
          root.business_unit = meta("hf_business_unit")
          root.customer_plan_id = meta("hf_customer_plan_id")
          root.voucher_code = meta("hf_voucher_code")
          root.error_code = meta("hf_error_code")
    - label: "store_content_in_meta"
      bloblang: |-
        meta hf_raw_content = content().bytes()
    - label: "create_trigger_message"
      bloblang: '{"business_unit": meta("hf_business_unit"), "customer_plan_id": meta("hf_customer_plan_id"), "voucher_code": meta("hf_voucher_code"), "error_code": meta("hf_error_code")}'

output:
  label: check_metadata
  switch:
    cases:
      - check: ((meta("hf_business_unit") | "") != "" && (meta("hf_customer_plan_id") | "") != "")
        output:
          label: insert_and_publish
          broker:
            pattern: fan_out_sequential
            outputs:
              - label: insert_benefit_attachment_event_into_db
                sql_insert:
                  driver: postgres
                  dsn: postgres://${DB_USER:credit-autocancel-service}:${DB_PASSWORD:freshforyou}@${DB_HOST:postgres}:${DB_PORT:5432}/${DB_NAME:creditautocanceldb}?sslmode=disable
                  max_in_flight: 32
                  table: events_benefit_attachment_v2beta1
                  columns: [ business_unit, customer_plan_id, voucher_code, raw, metadata]
                  args_mapping: |
                    root = [
                      meta("hf_business_unit"),
                      meta("hf_customer_plan_id"),
                      meta("hf_voucher_code"),
                      meta("hf_raw_content").bytes(),
                      meta().filter(item -> !item.key.has_prefix("hf_") && !item.key.has_prefix("grpc-trace-bin")).string()
                    ]
              - label: publish_benefit_attached
                kafka:
                  addresses:
                    - ${KAFKA_DSN}
                  tls:
                    enabled: ${TLS_ENABLED:true}
                    root_cas_file: ${KAFKA_CA_CERT_FILE}
                  sasl:
                    mechanism: ${KAFKA_SASL_MECHANISM:PLAIN}
                    user: ${KAFKA_USERNAME}
                    password: ${KAFKA_PASSWORD}
                  metadata:
                    exclude_prefixes: [ "" ] # do not forward any of the metadata fields
                  target_version: 3.4.1
                  topic: credit-autocancel-service.benthos.trigger-benefit-attachment
                  key: ${! (meta("hf_business_unit") + meta("hf_customer_plan_id")).hash("xxhash64") }
      - output:
          label: handle_missing_metadata
          processors:
            - log:
                level: ERROR
                message: 'dropping message because it is missing data'
                fields_mapping: |-
                  root.topic = meta("kafka_topic")
                  root.partition = meta("kafka_partition")
                  root.offset = meta("kafka_offset")
                  root.business_unit = meta("hf_business_unit")
                  root.customer_plan_id = meta("hf_customer_plan_id")
                  root.voucher_code = meta("hf_voucher_code")
                  root.error_code = meta("hf_error_code")
          drop: {}
