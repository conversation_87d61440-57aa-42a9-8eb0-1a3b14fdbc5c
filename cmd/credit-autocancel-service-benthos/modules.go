package main

import (
	"context"
	"fmt"

	"github.com/hellofresh/credit-autocancel-service/internal/app/command"
	"github.com/hellofresh/credit-autocancel-service/pkg/platform/protobuf"
	"github.com/hellofresh/growth-go-kit/logger"
	"github.com/redpanda-data/benthos/v4/public/service"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
)

func initModules(benthosHandler *command.BenthosHandler) {
	ctx := context.Background()

	err := service.RegisterMetricsExporter(
		"otel_metrics_benthos_bridge",
		service.NewConfigSpec(),
		func(_ *service.ParsedConfig, _ *service.Logger) (service.MetricsExporter, error) {
			return &benthosMetricsProvider{}, nil
		},
	)
	if err != nil {
		logger.From(ctx).Fatal("error initializing benthos metrics provider", zap.Error(err))
	}

	err = service.RegisterOtelTracerProvider(
		"otel_tracing_benthos_bridge",
		service.NewConfigSpec(),
		func(_ *service.ParsedConfig) (trace.TracerProvider, error) {
			return otel.GetTracerProvider(), nil
		},
	)
	if err != nil {
		logger.From(ctx).Fatal("error initializing benthos trace provider", zap.Error(err))
	}

	err = service.RegisterProcessor(
		"hf_protobuf_parser",
		service.NewConfigSpec(),
		func(_ *service.ParsedConfig, res *service.Resources) (service.Processor, error) {
			return protobuf.NewBenthosParser(res), nil
		},
	)
	if err != nil {
		logger.From(ctx).Fatal("error initializing benthos plugin hf_protobuf_parser", zap.Error(err))
	}

	err = service.RegisterBatchOutput(
		"hf_process_order_paid",
		service.NewConfigSpec().
			Field(service.NewIntField("max_in_flight").
				Description("The maximum number of messages to have in flight at a given time. Increase this to improve throughput.").
				Default(1)),
		func(conf *service.ParsedConfig, _ *service.Resources) (out service.BatchOutput, batchPolicy service.BatchPolicy, maxInFlight int, err error) {
			maxInFlightCfg, err := conf.FieldInt("max_in_flight")
			if err != nil {
				return nil, service.BatchPolicy{}, 0, fmt.Errorf("error parsing max_in_flight: %w", err)
			}

			return &hfProcessOrderPaid{
					benthosHandler: benthosHandler,
				}, service.BatchPolicy{
					Count:  1,
					Period: "1m",
				}, maxInFlightCfg, nil
		},
	)
	if err != nil {
		logger.From(ctx).Fatal("error initializing benthos plugin hf_process_order_paid", zap.Error(err))
	}

	err = service.RegisterBatchOutput(
		"hf_process_benefit_attached",
		service.NewConfigSpec().
			Field(service.NewIntField("max_in_flight").
				Description("The maximum number of messages to have in flight at a given time. Increase this to improve throughput.").
				Default(1)),
		func(conf *service.ParsedConfig, _ *service.Resources) (out service.BatchOutput, batchPolicy service.BatchPolicy, maxInFlight int, err error) {
			maxInFlightCfg, err := conf.FieldInt("max_in_flight")
			if err != nil {
				return nil, service.BatchPolicy{}, 0, fmt.Errorf("error parsing max_in_flight: %w", err)
			}

			return &hfProcessBenefitAttached{
					benthosHandler: benthosHandler,
				}, service.BatchPolicy{
					Count:  1,
					Period: "1m",
				}, maxInFlightCfg, nil
		},
	)
	if err != nil {
		logger.From(ctx).Fatal("error initializing benthos plugin hf_process_benefit_attached", zap.Error(err))
	}
}
