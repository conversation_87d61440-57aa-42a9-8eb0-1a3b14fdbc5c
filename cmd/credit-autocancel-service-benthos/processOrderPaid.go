package main

import (
	"context"
	"fmt"
	"strings"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/credit-autocancel-service/internal/subscription"
	"github.com/hellofresh/growth-go-kit/observability"
	protoCustomerOrderDelivery "github.com/hellofresh/schema-registry-go/stream/customer/order/delivery/v1"
	protoCustomerOrder "github.com/hellofresh/schema-registry-go/stream/customer/order/v1"
	"github.com/redpanda-data/benthos/v4/public/service"
	"google.golang.org/protobuf/proto"
)

//go:generate mockery --name benthosHandler --output . --outpkg=main --filename benthosHandler_mock_test.go --inpackage
type benthosHandler interface {
	HandleOrderPaid(ctx context.Context, autoCancel *subscription.AutoCancel) error
	HandleBenefitAttached(ctx context.Context, autoCancel *subscription.AutoCancel) error
}

type hfProcessOrderPaid struct {
	benthosHandler benthosHandler
}

var (
	errEmptyVoucher         = fmt.Errorf("voucher must not be empty")
	errEmptyPostCode        = fmt.Errorf("postcode must not be empty")
	errEmptyOrderItems      = fmt.Errorf("order items must not be empty")
	errEmptyShippingAddress = fmt.Errorf("shipping address must not be empty")
)

// WriteBatch writes a batch of messages to the output.
func (h *hfProcessOrderPaid) WriteBatch(ctx context.Context, inputBatch service.MessageBatch) error {
	ctx, span := observability.StartSpan(ctx, "main.hfProcessOrderPaid.WriteBatch")
	defer span.End()

	batch := inputBatch.Copy()

	topic, err := topicFromBatch(batch)
	if err != nil {
		return err
	}

	msgMaps, err := deduplicateBatch(batch)
	if err != nil {
		return fmt.Errorf("failed to deduplicate order paid batch: %w", err)
	}

	for _, msgMap := range msgMaps {
		autoCancel, err := convertOrderPaidToAutoCancel(msgMap)
		if err != nil {
			if logAndMeasure(ctx, err, topic, statusTagForCancellation, autoCancel) != nil {
				return span.Fail(fmt.Errorf("failed to convert benefit attached to auto cancel: %w", err))
			}
			continue
		}

		err = h.benthosHandler.HandleOrderPaid(ctx, autoCancel)
		if logAndMeasure(ctx, err, topic, statusTagForCancellation, autoCancel) != nil {
			return span.Fail(fmt.Errorf("failed to process order paid: %w", err))
		}
	}
	return nil
}

func (h *hfProcessOrderPaid) Connect(_ context.Context) error {
	return nil
}

func (h *hfProcessOrderPaid) Close(_ context.Context) error {
	return nil
}

func convertOrderPaidToAutoCancel(msgMap map[string]interface{}) (*subscription.AutoCancel, error) {
	businessUnit, _ := msgMap["business_unit"].(string)

	planIDStr, _ := msgMap["customer_plan_id"].(string)
	planID, planIDErr := uuid.FromString(planIDStr)

	autoCancel := subscription.AutoCancel{
		Country:        strings.ToUpper(businessUnit),
		CustomerPlanID: uuid.NullUUID{UUID: planID, Valid: planIDErr == nil},
	}

	orderRaw, _ := msgMap["order_raw"].([]byte)
	var order protoCustomerOrder.CustomerOrderValue
	err := proto.Unmarshal(orderRaw, &order)
	if err != nil {
		return &autoCancel, fmt.Errorf("failed to unmarshal order: %w", err)
	}

	deliveryRaw, _ := msgMap["delivery_raw"].([]byte)
	var delivery protoCustomerOrderDelivery.DeliveryDetailsValue
	err = proto.Unmarshal(deliveryRaw, &delivery)
	if err != nil {
		return &autoCancel, fmt.Errorf("failed to unmarshal delivery: %w", err)
	}

	if order.GetItems() == nil || len(order.GetItems()) <= 0 {
		return &autoCancel, errEmptyOrderItems
	}

	autoCancel.Voucher, err = getVoucher(&order)
	if err != nil {
		return &autoCancel, err
	}

	customerUUID, customerUUIDErr := uuid.FromString(order.GetCustomerId())
	autoCancel.CustomerUUID = uuid.NullUUID{UUID: customerUUID, Valid: customerUUIDErr == nil}

	subscriptionID := order.GetSubscriptionId() //nolint:staticcheck
	autoCancel.SubscriptionID = &subscriptionID

	for _, item := range order.GetItems() {
		if item.GetProductHandle() != "" {
			autoCancel.Products = append(autoCancel.Products, item.GetProductHandle())
		}
	}

	if delivery.GetShippingAddress() == nil {
		return &autoCancel, errEmptyShippingAddress
	}

	postcode := delivery.GetShippingAddress().GetPostalCode()
	if postcode == "" {
		return &autoCancel, errEmptyPostCode
	}
	autoCancel.Postcode = &postcode

	autoCancel.IsSubscriptionInitialOrder = order.GetIsSubscriptionInitialOrder()

	return &autoCancel, nil
}

func getVoucher(order *protoCustomerOrder.CustomerOrderValue) (*string, error) {
	for _, item := range order.GetItems() {
		voucherCode := item.GetVoucherCode()
		if voucherCode != "" {
			return &voucherCode, nil
		}
	}

	return nil, errEmptyVoucher
}
