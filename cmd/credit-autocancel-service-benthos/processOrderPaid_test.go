package main

import (
	"testing"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/credit-autocancel-service/internal/campaign"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/priceservice"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/voucherservice"
	"github.com/hellofresh/credit-autocancel-service/internal/customer"
	"github.com/hellofresh/credit-autocancel-service/internal/subscription"
	"github.com/redpanda-data/benthos/v4/public/service"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/postaladdress"

	protoCustomerOrderDelivery "github.com/hellofresh/schema-registry-go/stream/customer/order/delivery/v1"
	protoCustomerOrder "github.com/hellofresh/schema-registry-go/stream/customer/order/v1"
)

var (
	businessUnit   = "US"
	partitionID    = "1"
	customerPlanID = "caed8953-2338-5037-8c14-2939c54d3704"
	customerUUID   = "e2899282-d51a-45c5-a041-d4c3e2ab4bfc"
	sku            = "US-CB-1"
	postcode       = "11101"
	voucherCode    = "ABC123"
	subscriptionID = int64(0)
	delivery       = &protoCustomerOrderDelivery.DeliveryDetailsValue{
		ShippingAddress: &postaladdress.PostalAddress{
			RegionCode: businessUnit,
			PostalCode: postcode,
		},
	}
	order = &protoCustomerOrder.CustomerOrderValue{
		CustomerId: customerUUID,
		PlanId:     customerPlanID,
		RegionCode: businessUnit,
		Items: []*protoCustomerOrder.CustomerOrderValue_Item{
			{
				ProductHandle: sku,
				VoucherCode:   voucherCode,
			},
		},
	}
	orderPaidTopic = "credit-autocancel-service.benthos.trigger-order-paid"
)

func Test_OrderPaid_WriteBatch(t *testing.T) {
	t.Parallel()

	give := service.MessageBatch{
		createServiceMessage(
			t,
			customerPlanID,
			partitionID,
			orderPaidTopic,
			businessUnit,
			"",
			"",
			delivery,
			order,
			true,
		),
	}

	autoCancel := &subscription.AutoCancel{
		Country:        businessUnit,
		CustomerPlanID: uuid.NullUUID{UUID: uuid.FromStringOrNil(customerPlanID), Valid: true},
		CustomerUUID:   uuid.NullUUID{UUID: uuid.FromStringOrNil(customerUUID), Valid: true},
		Voucher:        &voucherCode,
		Postcode:       &postcode,
		Products:       []string{sku},
		SubscriptionID: &subscriptionID,
	}

	tests := []struct {
		name    string
		give    service.MessageBatch
		setup   func(*mockBenthosHandler)
		wantErr string
	}{
		{
			name: "missing kafka topic error",
			give: service.MessageBatch{
				createServiceMessage(
					t,
					customerPlanID,
					"1",
					"",
					businessUnit,
					"",
					"",
					delivery,
					order,
					true,
				),
			},
			wantErr: "message missing kafka_topic",
		},
		{
			name: "missing partition id error",
			give: service.MessageBatch{
				createServiceMessage(
					t,
					customerPlanID,
					"",
					orderPaidTopic,
					businessUnit,
					"",
					"",
					delivery,
					order,
					true,
				),
			},
			wantErr: "failed to deduplicate order paid batch: message missing kafka_partition",
		},
		{
			name: "order unmarshal error",
			give: []*service.Message{
				createServiceMessageFromMarshalledEvents(
					t,
					"caed8953-2338-5037-8c14-2939c54d3704",
					"1",
					orderPaidTopic,
					businessUnit,
					"",
					"",
					nil,
					[]byte("invalid order"),
					true,
				),
			},
			wantErr: "failed to convert benefit attached to auto cancel: failed to unmarshal order",
		},
		{
			name: "error on empty order items",
			give: service.MessageBatch{
				createServiceMessage(
					t,
					customerPlanID,
					"1",
					orderPaidTopic,
					"us",
					"",
					"",
					delivery,
					&protoCustomerOrder.CustomerOrderValue{
						CustomerId: customerUUID,
						PlanId:     customerPlanID,
						RegionCode: businessUnit,
					},
					true,
				),
			},
			wantErr: "failed to convert benefit attached to auto cancel: order items must not be empty",
		},
		{
			name: "delivery unmarshal error",
			give: []*service.Message{
				createServiceMessageFromMarshalledEvents(
					t,
					"caed8953-2338-5037-8c14-2939c54d3704",
					"1",
					orderPaidTopic,
					businessUnit,
					"",
					"",
					[]byte("invalid delivery"),
					nil,
					true,
				),
			},
			wantErr: "failed to convert benefit attached to auto cancel: failed to unmarshal delivery",
		},
		{
			name: "voucher not found",
			give: give,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleOrderPaid", mock.Anything, autoCancel).Return(voucherservice.ErrNotFound)
			},
		},
		{
			name: "empty voucher code",
			give: service.MessageBatch{
				createServiceMessage(
					t,
					customerPlanID,
					"1",
					orderPaidTopic,
					"us",
					"",
					"",
					delivery,
					&protoCustomerOrder.CustomerOrderValue{
						CustomerId: customerUUID,
						PlanId:     customerPlanID,
						RegionCode: businessUnit,
						Items: []*protoCustomerOrder.CustomerOrderValue_Item{
							{
								ProductHandle: sku,
								VoucherCode:   "",
							},
						},
					},
					true,
				),
			},
		},
		{
			name: "error",
			give: give,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleOrderPaid", mock.Anything, autoCancel).Return(assert.AnError)
			},
			wantErr: "failed to process order paid: assert.AnError general error for testing",
		},
		{
			name: "voucher not part of campaign",
			give: give,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleOrderPaid", mock.Anything, autoCancel).Return(campaign.ErrVoucherNotPartOfCampaign)
			},
		},
		{
			name: "empty shipping address",
			give: service.MessageBatch{
				createServiceMessage(
					t,
					customerPlanID,
					"1",
					orderPaidTopic,
					"us",
					"",
					"",
					&protoCustomerOrderDelivery.DeliveryDetailsValue{},
					&protoCustomerOrder.CustomerOrderValue{
						CustomerId: customerUUID,
						PlanId:     customerPlanID,
						RegionCode: businessUnit,
						Items: []*protoCustomerOrder.CustomerOrderValue_Item{
							{
								ProductHandle: sku,
								VoucherCode:   voucherCode,
							},
						},
					},
					true,
				),
			},
			wantErr: "failed to convert benefit attached to auto cancel: shipping address must not be empty",
		},
		{
			name: "empty postcode",
			give: service.MessageBatch{
				createServiceMessage(
					t,
					customerPlanID,
					"1",
					orderPaidTopic,
					"us",
					"",
					"",
					&protoCustomerOrderDelivery.DeliveryDetailsValue{
						ShippingAddress: &postaladdress.PostalAddress{
							RegionCode: businessUnit,
							PostalCode: "",
						},
					},
					&protoCustomerOrder.CustomerOrderValue{
						CustomerId: customerUUID,
						PlanId:     customerPlanID,
						RegionCode: businessUnit,
						Items: []*protoCustomerOrder.CustomerOrderValue_Item{
							{
								ProductHandle: sku,
								VoucherCode:   voucherCode,
							},
						},
					},
					true,
				),
			},
		},
		{
			name: "next order covered",
			give: give,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleOrderPaid", mock.Anything, autoCancel).Return(nil)
			},
		},
		{
			name: "already cancelled",
			give: give,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleOrderPaid", mock.Anything, autoCancel).Return(subscription.ErrAlreadyCancelled)
			},
		},
		{
			name: "customer not found",
			give: give,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleOrderPaid", mock.Anything, autoCancel).Return(customer.ErrCustomerNotFound)
			},
		},
		{
			name: "addons not supported",
			give: give,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleOrderPaid", mock.Anything, autoCancel).Return(priceservice.ErrTestAddonCalculate)
			},
		},
		{
			name: "success",
			give: give,
			setup: func(m *mockBenthosHandler) {
				m.On("HandleOrderPaid", mock.Anything, autoCancel).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockBenthosHandlerMock := newMockBenthosHandler(t)
			if tt.setup != nil {
				tt.setup(mockBenthosHandlerMock)
			}

			a := &hfProcessOrderPaid{
				benthosHandler: mockBenthosHandlerMock,
			}

			ctx := t.Context()

			err := a.WriteBatch(ctx, tt.give)
			if tt.wantErr == "" {
				assert.NoError(t, err)
			} else {
				assert.ErrorContains(t, err, tt.wantErr)
			}
			require.NoError(t, a.Connect(ctx))
			require.NoError(t, a.Close(ctx))
		})
	}
}
