package main

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"

	"github.com/hellofresh/credit-autocancel-service/internal/campaign"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/priceservice"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/voucherservice"
	"github.com/hellofresh/credit-autocancel-service/internal/customer"
	"github.com/hellofresh/credit-autocancel-service/internal/subscription"
	"github.com/hellofresh/growth-go-kit/logger"
	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/redpanda-data/benthos/v4/public/service"
	"go.uber.org/zap"
)

const (
	metricPrefix = "benthos_"
	nsToMs       = 1e6 // conversion factor from nanoseconds to milliseconds
)

var latencyBuckets = []float64{
	1, 2, 4, 6, 8, 10, // 1-10ms in 2ms increments
	20, 30, 40, 50, 60, 70, 80, 90, 100, // 10-100ms in 10ms increments
	125, 150, 175, 200, 225, 250, // 100-250ms in 25ms increments
	300, 350, 400, 450, 500, // 250ms-500ms in 50ms increments
	600, 700, 800, 900, 1000, // 500ms-1s in 100ms increments
	1250, 1500, 1750, 2000, 2250, 2500, 2750, 3000, // 1s-3s in 250ms increments
}

// getOrCreateMetric safely retrieves or creates a metric of the specified type T
//
//nolint:ireturn
func getOrCreateMetric[T any](registry *sync.Map, name string, creator func() T) T {
	if metric, ok := registry.Load(name); ok {
		if typedMetric, ok := metric.(T); ok {
			return typedMetric
		}
		panic(fmt.Sprintf("metric %q exists but has wrong type: want %T, got %T", name, *new(T), metric))
	}

	metric := creator()
	registry.Store(name, metric)
	return metric
}

var _ service.MetricsExporter = (*benthosMetricsProvider)(nil)

// benthosMetricsProvider connects Benthos metrics to Growth Go Kit Observability.
type benthosMetricsProvider struct {
	registry sync.Map
}

// NewCounterCtor creates a new counter.
func (b *benthosMetricsProvider) NewCounterCtor(name string, labelKeys ...string) service.MetricsExporterCounterCtor {
	name = metricPrefix + name
	metricLabels := newMetricLabels(labelKeys)
	creator := func() *observability.Int64Metric {
		return observability.NewInt64Metric(
			name,
			fmt.Sprintf("Benthos counter metric: %q", name),
			observability.UnitDimensionless,
			observability.AggregationCount(),
		)
	}

	metric := getOrCreateMetric(&b.registry, name, creator)
	return func(labels ...string) service.MetricsExporterCounter {
		return &benthosMetricCounter{
			metric: metric,
			attrs:  metricLabels.Apply(labels),
		}
	}
}

// NewTimerCtor creates a new timer.
func (b *benthosMetricsProvider) NewTimerCtor(name string, labelKeys ...string) service.MetricsExporterTimerCtor {
	name = metricPrefix + name
	name = strings.ReplaceAll(name, "_ns", "_milliseconds")
	metricLabels := newMetricLabels(labelKeys)
	creator := func() *observability.Float64Metric {
		return observability.NewFloat64Metric(
			name,
			fmt.Sprintf("Benthos timer metric: %q", name),
			observability.UnitMilliseconds,
			observability.AggregationDistribution(latencyBuckets...),
		)
	}

	metric := getOrCreateMetric(&b.registry, name, creator)
	return func(labels ...string) service.MetricsExporterTimer {
		return &benthosMetricTimer{
			metric: metric,
			attrs:  metricLabels.Apply(labels),
		}
	}
}

// NewGaugeCtor creates a new gauge.
func (b *benthosMetricsProvider) NewGaugeCtor(name string, labelKeys ...string) service.MetricsExporterGaugeCtor {
	name = metricPrefix + name
	metricLabels := newMetricLabels(labelKeys)
	creator := func() *observability.Int64Metric {
		return observability.NewInt64Metric(
			name,
			fmt.Sprintf("Benthos gauge metric: %q", name),
			observability.UnitDimensionless,
			observability.AggregationLastValue(),
		)
	}

	metric := getOrCreateMetric(&b.registry, name, creator)
	return func(labels ...string) service.MetricsExporterGauge {
		return &benthosMetricGauge{
			metric: metric,
			attrs:  metricLabels.Apply(labels),
		}
	}
}

// Close is a stub implementation.
func (b *benthosMetricsProvider) Close(_ context.Context) error {
	return nil
}

type metricLabels struct {
	keys []*observability.Tag
}

func newMetricLabels(labelKeys []string) *metricLabels {
	tags := make([]*observability.Tag, 0, len(labelKeys))

	for _, labelKey := range labelKeys {
		tags = append(tags, observability.NewTagKey(labelKey))
	}

	return &metricLabels{
		keys: tags,
	}
}

func (ml *metricLabels) Apply(values []string) []*observability.TagValue {
	attrs := make([]*observability.TagValue, 0, len(ml.keys))

	for i, value := range values {
		if i > len(ml.keys)-1 {
			break
		}

		attrs = append(attrs, ml.keys[i].V(value))
	}

	return attrs
}

type benthosMetricCounter struct {
	metric *observability.Int64Metric
	attrs  []*observability.TagValue
}

func (b *benthosMetricCounter) Incr(count int64) {
	b.metric.M(context.Background(), count, b.attrs...)
}

type benthosMetricTimer struct {
	metric *observability.Float64Metric
	attrs  []*observability.TagValue
}

func (b *benthosMetricTimer) Timing(delta int64) {
	// convert from nanoseconds to milliseconds
	b.metric.M(context.Background(), float64(delta)/nsToMs, b.attrs...)
}

type benthosMetricGauge struct {
	metric *observability.Int64Metric
	attrs  []*observability.TagValue
}

func (b *benthosMetricGauge) Set(value int64) {
	b.metric.M(context.Background(), value, b.attrs...)
}

var (
	topicTag   = observability.NewTagKey("topic")
	resultTag  = observability.NewTagKey("result")
	countryTag = observability.NewTagKey("country")
	statusTag  = observability.NewTagKey("status")

	subscriptionSetCounter = observability.NewInt64Metric(
		fmt.Sprintf("%scredit_autocancel_service/subscription_set/count", metricPrefix),
		"SubscriptionSet counts the total number of subscriptions set by the benthos consumer",
		observability.UnitDimensionless,
		observability.AggregationCount(),
	)
)

const (
	statusTagForCancellation = "for_cancellation"
	statusTagRunning         = "running"

	resultTagSuccess                  = "success"
	resultTagVoucherNotFound          = "voucher_not_found"
	resultTagEmptyVoucher             = "empty_voucher"
	resultTagVoucherNotPartOfCampaign = "voucher_not_part_of_campaign"
	resultTagAlreadyCancelled         = "already_cancelled"
	resultTagEmptyPostCode            = "empty_postcode"
	resultTagNextOrderCovered         = "next_order_covered"
	resultTagCustomerNotFound         = "customer_not_found"
	resultTagAddonsNotSupported       = "addons_not_supported"
	resultTagFailedBenefitAttachment  = "failed_benefit_attachment"
	resultTagError                    = "error"

	logActionSkipped = "skipped"
)

// logAndMeasure logs expected error types at Warn level, nil error (success) at Debug level, otherwise at Debug,
// level (errors will be logged and retied in the benthos pipeline) with relevant information from the given message
// and records subscription_set_for_cancellation with the appropriate tag for the error.
func logAndMeasure(ctx context.Context, err error, topic, status string, autoCancel *subscription.AutoCancel) error {
	var (
		calculateErrors priceservice.CalculateErrors
		resTag          string
		logAction       = logActionSkipped
		logLevel        = zap.WarnLevel
		errResponse     error
	)

	switch {
	case err == nil:
		resTag = resultTagSuccess
		logAction = resTag
		logLevel = zap.DebugLevel
	case errors.Is(err, voucherservice.ErrNotFound):
		resTag = resultTagVoucherNotFound
	case errors.Is(err, errEmptyVoucher):
		resTag = resultTagEmptyVoucher
		logLevel = zap.DebugLevel
	case errors.Is(err, campaign.ErrVoucherNotPartOfCampaign):
		resTag = resultTagVoucherNotPartOfCampaign
		logLevel = zap.DebugLevel
	case errors.Is(err, subscription.ErrAlreadyCancelled):
		resTag = resultTagAlreadyCancelled
	case errors.Is(err, errEmptyPostCode):
		resTag = resultTagEmptyPostCode
	case errors.Is(err, customer.ErrCustomerNotFound):
		resTag = resultTagCustomerNotFound
	case errors.Is(err, errFailedBenefitAttachment):
		resTag = resultTagFailedBenefitAttachment
	case errors.As(err, &calculateErrors) && calculateErrors.IsAddonError():
		resTag = resultTagAddonsNotSupported
	default:
		resTag = resultTagError
		logAction = resTag
		logLevel = zap.DebugLevel
		errResponse = err
	}

	subscriptionSetCounter.M(
		ctx,
		1,
		topicTag.V(topic),
		resultTag.V(resTag),
		countryTag.V(strings.ToLower(autoCancel.Country)),
		statusTag.V(status),
	)

	logger.From(ctx).With(
		zap.String("topic", topic),
		zap.Any("autocancel", autoCancel),
		zap.Error(err),
	).Check(logLevel, fmt.Sprintf("%s processing %s event", logAction, topic)).
		Write()

	return errResponse
}
