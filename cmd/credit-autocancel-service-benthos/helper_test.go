package main

import (
	"encoding/json"
	"testing"

	"github.com/redpanda-data/benthos/v4/public/service"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"

	protoCustomerOrderDelivery "github.com/hellofresh/schema-registry-go/stream/customer/order/delivery/v1"
	protoCustomerOrder "github.com/hellofresh/schema-registry-go/stream/customer/order/v1"
)

func createServiceMessage(
	t *testing.T,
	customerPlanID,
	partitionID,
	topic,
	businessUnit,
	voucherCode,
	errorCode string,
	delivery *protoCustomerOrderDelivery.DeliveryDetailsValue,
	order *protoCustomerOrder.CustomerOrderValue,
	setStructured bool,
) *service.Message {
	t.Helper()

	deliveryRaw, err := proto.Marshal(delivery)
	require.NoError(t, err)

	orderRaw, err := proto.Marshal(order)
	require.NoError(t, err)

	return createServiceMessageFromMarshalledEvents(
		t,
		customerPlanID,
		partitionID,
		topic,
		businessUnit,
		voucherCode,
		errorCode,
		deliveryRaw,
		orderRaw,
		setStructured,
	)
}

func createServiceMessageFromMarshalledEvents(
	t *testing.T,
	customerPlanID,
	partitionID,
	topic,
	businessUnit,
	voucherCode,
	errorCode string,
	delivery,
	order []byte,
	setStructured bool,
) *service.Message {
	t.Helper()

	msg := service.NewMessage([]byte{})
	if setStructured {

		msgMap := createMsgMapFromMarshalledEvents(
			customerPlanID,
			businessUnit,
			voucherCode,
			errorCode,
			delivery,
			order,
		)

		msgRaw, err := json.Marshal(msgMap)
		require.NoError(t, err)

		msg = service.NewMessage(msgRaw)
		msg.SetStructured(msgMap)
	}

	if partitionID != "" {
		msg.MetaSet("kafka_partition", partitionID)
	}

	if topic != "" {
		msg.MetaSet("kafka_topic", topic)
	}

	return msg
}

func createMsgMap(
	t *testing.T,
	customerPlanID,
	businessUnit,
	voucherCode,
	errorCode string,
	delivery *protoCustomerOrderDelivery.DeliveryDetailsValue,
	order *protoCustomerOrder.CustomerOrderValue,
) map[string]interface{} {
	deliveryRaw, err := proto.Marshal(delivery)
	require.NoError(t, err)

	orderRaw, err := proto.Marshal(order)
	require.NoError(t, err)

	return createMsgMapFromMarshalledEvents(
		customerPlanID,
		businessUnit,
		voucherCode,
		errorCode,
		deliveryRaw,
		orderRaw,
	)
}

func createMsgMapFromMarshalledEvents(
	customerPlanID,
	businessUnit,
	voucherCode,
	errorCode string,
	delivery,
	order []byte,
) map[string]interface{} {
	msgMap := map[string]interface{}{
		"customer_plan_id": customerPlanID,
		"business_unit":    businessUnit,
	}

	if voucherCode != "" {
		msgMap["voucher_code"] = voucherCode
	}

	if errorCode != "" {
		msgMap["error_code"] = errorCode
	}

	if delivery != nil {
		msgMap["delivery_raw"] = delivery
	}

	if order != nil {
		msgMap["order_raw"] = order
	}

	return msgMap
}

func createNonMapMessage(t *testing.T, businessUnit string) *service.Message {
	t.Helper()

	msg := service.NewMessage([]byte(businessUnit))
	msg.SetStructured(businessUnit)

	msg.MetaSet("kafka_topic", "credit-autocancel-service.benthos.trigger-order-paid")
	msg.MetaSet("kafka_partition", "1")

	return msg
}

func matchMessages(t *testing.T, expected, actual map[string]map[string]interface{}) {
	t.Helper()

	assert.Equal(t, len(expected), len(actual))

	for k, expectedMsg := range expected {
		actualMsg, ok := actual[k]
		require.True(t, ok)

		assert.Equal(t, expectedMsg, actualMsg)
	}
}
