package main

import (
	"context"
	"errors"
	"fmt"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/credit-autocancel-service/internal/subscription"
	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/redpanda-data/benthos/v4/public/service"
)

type hfProcessBenefitAttached struct {
	benthosHandler benthosHandler
}

// ErrFailedBenefitAttachment indicates the benefit attachment was not successful
var errFailedBenefitAttachment = errors.New("benefit attachment must be successful")

// WriteBatch writes a batch of messages to the output.
func (h *hfProcessBenefitAttached) WriteBatch(ctx context.Context, inputBatch service.MessageBatch) error {
	ctx, span := observability.StartSpan(ctx, "main.hfProcessBenefitAttached.WriteBatch")
	defer span.End()

	batch := inputBatch.Copy()

	topic, err := topicFromBatch(batch)
	if err != nil {
		return err
	}

	msgMaps, err := deduplicateBatch(batch)
	if err != nil {
		return fmt.Errorf("failed to deduplicate benefit attached batch: %w", err)
	}

	for _, msgMap := range msgMaps {
		autoCancel, err := convertBenefitAttachedToAutoCancel(msgMap)
		if err != nil {
			if logAndMeasure(ctx, err, topic, statusTagRunning, autoCancel) != nil {
				return span.Fail(fmt.Errorf("failed to convert benefit attached to auto cancel: %w", err))
			}
			continue
		}

		err = h.benthosHandler.HandleBenefitAttached(ctx, autoCancel)
		if logAndMeasure(ctx, err, topic, statusTagRunning, autoCancel) != nil {
			return span.Fail(fmt.Errorf("failed to process benefit attached: %w", err))
		}
	}
	return nil
}

func (h *hfProcessBenefitAttached) Connect(_ context.Context) error {
	return nil
}

func (h *hfProcessBenefitAttached) Close(_ context.Context) error {
	return nil
}

func convertBenefitAttachedToAutoCancel(msgMap map[string]interface{}) (*subscription.AutoCancel, error) {
	businessUnit, _ := msgMap["business_unit"].(string)

	planIDStr, _ := msgMap["customer_plan_id"].(string)
	planID, planIDErr := uuid.FromString(planIDStr)

	autoCancel := subscription.AutoCancel{
		Country:        businessUnit,
		CustomerPlanID: uuid.NullUUID{UUID: planID, Valid: planIDErr == nil},
	}

	voucherCode, _ := msgMap["voucher_code"].(string)
	if voucherCode == "" {
		return &autoCancel, errEmptyVoucher
	}
	autoCancel.Voucher = &voucherCode

	// 0 for successful attachment, other codes mean "failure"
	errorCode, _ := msgMap["error_code"].(string)
	if errorCode != "0" {
		return &autoCancel, fmt.Errorf("%w: error code %s", errFailedBenefitAttachment, errorCode)
	}

	return &autoCancel, nil
}
