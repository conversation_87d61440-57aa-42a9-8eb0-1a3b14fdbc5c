// Command to query events_customer_order_v1 and public.customer.legacy-subscription.v1beta3
// for a given customer_plan_id and parse raw data into proto objects
package main

import (
	"context"
	"encoding/csv"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/hellofresh/growth-go-kit/database"
	"github.com/hellofresh/growth-go-kit/logger"
	"github.com/hellofresh/growth-go-kit/observability"
	protoCustomerOrderDelivery "github.com/hellofresh/schema-registry-go/stream/customer/order/delivery/v1"
	protoCustomerOrderPayment "github.com/hellofresh/schema-registry-go/stream/customer/order/payment/v1"
	protoCustomerOrder "github.com/hellofresh/schema-registry-go/stream/customer/order/v1"
	"github.com/jmoiron/sqlx"
	"github.com/kelseyhightower/envconfig"
	_ "github.com/lib/pq"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

const (
	orderQuery = `
SELECT order_uuid, raw, created_at
FROM events_customer_order_v1
WHERE business_unit = $1
AND customer_plan_id = $2
ORDER BY created_at DESC
;
`
	deliveryQuery = `
SELECT raw, created_at
FROM events_customer_order_delivery_v1
WHERE lower(business_unit) = lower($1)
AND lower(order_uuid) = lower($2)
ORDER BY created_at DESC
;
`
	paymentQuery = `
SELECT raw, created_at
FROM events_customer_order_payment_v1
WHERE lower(business_unit) = lower($1)
AND lower(order_uuid) = lower($2)
ORDER BY created_at DESC
;
`
)

func queryEvents(ctx context.Context, db *sqlx.DB, log *zap.Logger, customerPlanUUID string) error {
	log = log.With(
		zap.String("customer_plan_uuid", customerPlanUUID),
	)

	type result struct {
		Raw       []byte    `db:"raw"`
		CreatedAt time.Time `json:"-" db:"created_at"`
	}

	type orderResult struct {
		OrderUUID string    `db:"order_uuid"`
		Raw       []byte    `db:"raw"`
		CreatedAt time.Time `json:"-" db:"created_at"`
	}
	var (
		orderResults []*orderResult
		orderUUIDs   = make(map[string]interface{})
		i            interface{}
		businessUnit string
	)

	err := db.SelectContext(ctx, &orderResults, orderQuery, businessUnit, customerPlanUUID)
	if err != nil {
		return fmt.Errorf("failed to find order events: %w", err)
	}

	for _, order := range orderResults {
		var orderEvent protoCustomerOrder.CustomerOrderValue

		err = proto.Unmarshal(order.Raw, &orderEvent)
		if err != nil {
			return fmt.Errorf("failed to unmarshal order message: %w", err)
		}

		businessUnit = orderEvent.GetRegionCode()

		log.Info(
			"successfully unmarshalled order message",
			zap.Any("order_uuid", order.OrderUUID),
			zap.Any("business_unit", businessUnit),
			zap.Any("order", orderEvent),
			zap.Any("created_at", order.CreatedAt),
		)

		orderUUIDs[order.OrderUUID] = i
	}

	for orderUUID := range orderUUIDs {
		var deliveryResults []*result
		err = db.SelectContext(ctx, &deliveryResults, deliveryQuery, businessUnit, orderUUID)
		if err != nil {
			return fmt.Errorf("failed to find delivery events: %w", err)
		}

		for _, delivery := range deliveryResults {
			var deliveryEvent protoCustomerOrderDelivery.DeliveryDetailsValue
			err = proto.Unmarshal(delivery.Raw, &deliveryEvent)
			if err != nil {
				return fmt.Errorf("failed to unmarshal delivery message: %w", err)
			}
			log.Info(
				"successfully unmarshalled delivery message",
				zap.Any("order_uuid", orderUUID),
				zap.Any("delivery", deliveryEvent),
				zap.Any("created_at", delivery.CreatedAt),
			)
		}

		var paymentResults []*result
		err = db.SelectContext(ctx, &paymentResults, paymentQuery, businessUnit, orderUUID)
		if err != nil {
			return fmt.Errorf("failed to find payment events: %w", err)
		}

		for _, payment := range paymentResults {
			var paymentEvent protoCustomerOrderPayment.PaymentDetailsValue
			err = proto.Unmarshal(payment.Raw, &paymentEvent)
			if err != nil {
				return fmt.Errorf("failed to unmarshal payment message: %w", err)
			}
			log.Info(
				"successfully unmarshalled payment message",
				zap.Any("order_uuid", orderUUID),
				zap.Any("payment", paymentEvent),
				zap.Any("created_at", payment.CreatedAt),
			)
		}
	}

	return nil
}

func main() {
	ctx := context.Background()

	var cfg database.Config
	err := envconfig.Process("", &cfg)
	if err != nil {
		panic(fmt.Errorf("failed to get config: %w", err))
	}

	log, err := logger.InitLogger(func() logger.InitOption {
		return logger.WithLevel(logger.LevelDebug)
	}())
	if err != nil {
		panic(fmt.Errorf("failed to init logger: %w", err))
	}

	// set up database
	db, err := observability.NewDBX("postgres", cfg)
	if err != nil {
		panic(fmt.Errorf("failed to init database: %w", err))
	}
	err = db.Ping()
	if err != nil {
		panic(fmt.Errorf("could not ping database: %w", err))
	}
	defer db.Close()

	dirName := "./cmd/credit-autocancel-service-event-decoder"

	// read customer_plan_ids from csvs in current directory
	files, err := os.ReadDir(dirName)
	if err != nil {
		panic(fmt.Errorf("failed to read files from directory: %w", err))
	}

	var queryParams [][]string
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".csv") {
			f, err := os.Open(dirName + "/" + file.Name())
			if err != nil {
				log.Error("failed to open file", zap.String("file", file.Name()), zap.Error(err))
				continue
			}
			defer f.Close()

			csvReader := csv.NewReader(f)
			data, err := csvReader.ReadAll()
			if err != nil {
				log.Error("failed to read csv", zap.String("file", file.Name()), zap.Error(err))
				continue
			}
			queryParams = append(queryParams, data...)
			log.Info("read csv", zap.String("file", file.Name()))
		}
	}

	log.Info("searching for events", zap.Any("params", queryParams))
	for _, queryParam := range queryParams {
		if len(queryParam) < 1 {
			continue
		}

		customerPlanUUID, err := uuid.Parse(queryParam[0])
		if err != nil {
			panic(fmt.Errorf("failed to parse customer plan id: %w", err))
		}

		err = queryEvents(ctx, db, log, customerPlanUUID.String())
		if err != nil {
			panic(err)
		}
	}
}
