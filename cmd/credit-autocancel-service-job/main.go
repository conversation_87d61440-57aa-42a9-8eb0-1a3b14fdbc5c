package main

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofrs/uuid"

	"github.com/hellofresh/growth-go-kit/logger"
	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"go.uber.org/zap"

	"github.com/hellofresh/credit-autocancel-service/internal/amqp/publisher"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/cps"
	"github.com/hellofresh/credit-autocancel-service/internal/database"
	"github.com/hellofresh/credit-autocancel-service/internal/subscription"
	"github.com/hellofresh/credit-autocancel-service/pkg/config"
	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/hellofresh/growth-go-kit/os/process"
)

type (
	subscriptionRepo interface {
		AllSetForCancellation(ctx context.Context) ([]subscription.AutoCancel, error)
		SetAsCancelled(ctx context.Context, customerPlanID uuid.NullUUID) error
	}

	canceller interface {
		Cancellation(ctx context.Context, planID uuid.UUID, cancelOrders bool) (bool, error)
	}
)

// These variables contain build information about the Version and a Buildstamp (timestamp, githash, etc.)
// they are injected by the build tools.
var (
	AppName    = "credit-autocancel-service-job"
	Version    = "0.0.0"
	Buildstamp = "dev"
)

func main() {
	ctx, done := process.Init(AppName, Version, Buildstamp)
	defer done()

	cfg, err := config.LoadConfig()
	if err != nil {
		panic(fmt.Errorf("failed to load environment variables: %w", err))
	}

	// Logging Configuration
	log, err := logger.InitLogger(func() logger.InitOption {
		if strings.EqualFold(cfg.AppEnv, "development") {
			return logger.WithLevel(logger.LevelDebug)
		}

		return logger.WithLevel(logger.LevelInfo)
	}())
	if err != nil {
		panic(fmt.Errorf("failed to init logger: %w", err))
	}

	// init routes
	ready := httputil.NewReady(
		httputil.TextHandler(http.StatusServiceUnavailable, "application/json", `"NOT READY"`),
	)
	mux := httputil.NewBaseMux(
		ready.Handler(httputil.TextHandler(http.StatusOK, "application/json", `"READY"`)),
	)

	// init view (metrics) including /metrics endpoint
	err = observability.InitView(
		context.Background(),
		&cfg.View,
		mux,
		"",
	)
	if err != nil {
		log.Fatal("failed to initialise view", zap.Error(err))
	}

	// Tracing configuration
	err = observability.InitTracing(
		ctx,
		&cfg.Trace,
		"",
	)
	if err != nil {
		log.Fatal("failed to initialize tracing", zap.Error(err))
	}

	db, err := database.Connect(ctx, cfg.Database)
	if err != nil {
		log.Fatal("cannot initialize connection with database", zap.Error(err))
	}

	customerConvertedPublisher := publisher.NewOutboxEmitter(db.DB, "topic", 10*time.Second)
	subscriptionRepo := subscription.NewRepo(db.DB, customerConvertedPublisher)
	canceller := cps.NewClient(cfg.CPSBaseURL)

	httpServer := &http.Server{
		Addr:    fmt.Sprintf(":%d", cfg.Port),
		Handler: observability.NewHTTPHandler(mux),
	}

	log.Info("starting HTTP Job server", zap.String("address", httpServer.Addr))

	// start server
	go httputil.StartServer(httpServer, log)
	ready.Ready()

	ticker := time.NewTicker(cfg.JobInterval)

	ctx, span := observability.StartSpan(ctx, "handler.autocancel.job")
	defer span.End()

	go func() {
		for {
			select {
			case <-ctx.Done():
				ticker.Stop()
				return
			case <-ticker.C:
				if err := executeCancellations(ctx, log, subscriptionRepo, canceller); err != nil {
					log.Error("cannot cancel subscription", zap.Error(span.Fail(err)))
				}
			}
		}
	}()

	log.Info("Credit AutoCancel Service Cancellation job running")

	<-ctx.Done()
	log.Info("killing app")

	// allow some time to shut down everything gracefully
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// try shutting down the HTTP server gracefully
	err = httpServer.Shutdown(ctx)
	if err != nil {
		log.Fatal("failed to shutdown server", zap.Error(err))
	}

	log.Info("shutdown reached")
}
