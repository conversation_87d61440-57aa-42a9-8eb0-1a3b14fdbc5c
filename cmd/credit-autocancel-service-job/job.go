package main

import (
	"context"
	"errors"
	"fmt"

	"github.com/hellofresh/growth-go-kit/observability"

	"github.com/hellofresh/credit-autocancel-service/internal/subscription"

	"go.uber.org/zap"
)

func executeCancellations(
	ctx context.Context,
	logger *zap.Logger,
	subRepo subscriptionRepo,
	canceller canceller,
) error {
	ctx, span := observability.StartSpan(ctx, "handler.autocancel.job.execute")
	defer span.End()

	subscriptionList, err := subRepo.AllSetForCancellation(ctx)
	if err != nil {
		return err
	}

	for _, sub := range subscriptionList {
		// cancel subscription
		err = cancelSubscription(ctx, sub, subRepo, logger, canceller)
		if err != nil {
			logger.With(
				zap.String("customer_plan_id", sub.CustomerPlanID.UUID.String())).
				Info("subscription not cancelled")
			subscriptionCancelled.M(ctx, 1, resultTag.V("error"))
			return span.Fail(fmt.Errorf("failed to cancel subscription: %w", err))
		}

		logger.With(
			zap.String("customer_plan_id", sub.CustomerPlanID.UUID.String())).
			Info("subscription cancelled")
		subscriptionCancelled.M(ctx, 1, resultTag.V("ok"))
	}

	return nil
}

func cancelSubscription(
	ctx context.Context,
	sub subscription.AutoCancel,
	subRepo subscriptionRepo,
	logger *zap.Logger,
	canceller canceller,
) error {
	ctx, span := observability.StartSpan(ctx, "handler.autocancel.job.cancel.subscription")
	defer span.End()

	if !sub.CustomerPlanID.Valid {
		return span.Fail(errors.New("error cancelling subscription, the customer_plan_id for the subscription is not valid"))
	}

	logger.Info("Cancelling subscription through CPS", zap.String("customer_plan_id", sub.CustomerPlanID.UUID.String()))
	isCancelled, err := canceller.Cancellation(ctx, sub.CustomerPlanID.UUID, false)
	if err != nil {
		return span.Fail(fmt.Errorf("error during cancellation job of the subscription country: %w", err))
	}

	if !isCancelled {
		return span.Fail(errors.New("unknown error, subscription was not cancelled"))
	}

	// change status to cancelled
	err = subRepo.SetAsCancelled(ctx, sub.CustomerPlanID)
	if err != nil {
		return span.Fail(fmt.Errorf("failed to change internal customer status: %w", err))
	}

	return nil
}
