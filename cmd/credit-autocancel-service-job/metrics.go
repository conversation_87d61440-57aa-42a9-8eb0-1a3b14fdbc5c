package main

import (
	"github.com/hellofresh/growth-go-kit/observability"
)

var (
	resultTag = observability.NewTagKey("result")

	subscriptionCancelled = observability.NewInt64Metric(
		"credit_autocancel_service/subscription_cancelled_total/count",
		"SubscriptionCancelled counts the total number of subscriptions cancelled by the job",
		observability.UnitDimensionless,
		observability.AggregationCount(),
	)
)
