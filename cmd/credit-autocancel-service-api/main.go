package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/go-chi/chi/v5"
	chiMiddleware "github.com/go-chi/chi/v5/middleware"
	"github.com/hellofresh/growth-go-kit/logger"
	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/hellofresh/growth-go-kit/os/process"
	"github.com/kelseyhightower/envconfig"
	"go.uber.org/zap"

	"github.com/hellofresh/credit-autocancel-service/internal/app"
	"github.com/hellofresh/credit-autocancel-service/internal/campaign"
	"github.com/hellofresh/credit-autocancel-service/internal/database"
	http2 "github.com/hellofresh/credit-autocancel-service/internal/inbound/http"
	"github.com/hellofresh/credit-autocancel-service/internal/migrate"
)

// These variables contain build information about the Version and a Buildstamp (timestamp, githash, etc.)
// they are injected by the build tools.
var (
	AppName    = "credit-autocancel-service-api"
	Version    = "0.0.0"
	Buildstamp = "dev"
)

func main() {
	ctx, done := process.Init(AppName, Version, Buildstamp)
	defer done()

	// =============================================================================
	// Application Configuration
	var cfg app.Configuration
	if err := envconfig.Process("", &cfg); err != nil {
		panic(fmt.Errorf("failed to load environment variables: %w", err))
	}

	// =============================================================================
	// Logging Configuration
	log, err := logger.InitLogger(func() logger.InitOption {
		if strings.EqualFold(cfg.AppEnv, "development") {
			return logger.WithLevel(logger.LevelDebug)
		}

		return logger.WithLevel(logger.LevelInfo)
	}())
	if err != nil {
		panic(fmt.Errorf("failed to init logger: %w", err))
	}

	// =============================================================================
	// Ready check and routes setup
	ready := httputil.NewReady(
		httputil.TextHandler(http.StatusServiceUnavailable, "application/json", `"NOT READY"`),
	)
	mux := httputil.NewBaseMux(
		ready.Handler(httputil.TextHandler(http.StatusOK, "application/json", `"READY"`)),
	)

	// =============================================================================
	// Init view (metrics) including /metrics endpoint
	err = observability.InitView(
		context.Background(),
		&cfg.View,
		mux,
		"",
	)
	if err != nil {
		log.Fatal("failed to initialise view", zap.Error(err))
	}

	// =============================================================================
	// Tracing Configuration
	err = observability.InitTracing(
		ctx,
		&cfg.Trace,
		"",
	)
	if err != nil {
		log.Fatal("failed to initialize tracing", zap.Error(err))
	}

	// =============================================================================
	// Database Migrations
	migrateFlag := flag.Bool("migrate", false, "run up migrations")
	flag.Parse()

	if *migrateFlag {
		if err := migrate.Up(cfg.Database); err != nil {
			log.Fatal("cannot migrate database", zap.Error(err))
		}

		log.Info("Migrated database to latest version")
		os.Exit(0)
	}

	// =============================================================================
	// Database Connection
	db, err := database.Connect(ctx, cfg.Database)
	if err != nil {
		log.Fatal("cannot initialize connection with database", zap.Error(err))
	}
	defer db.DB.Close()

	// DB Repos
	campaignRepo := campaign.NewRepo(db.DB)

	// Handlers
	campaignsHandler := http2.NewCampaigns(ctx, campaignRepo)

	// Setup campaign routes using chi router
	router := chi.NewRouter()
	router.Use(chiMiddleware.RequestID)
	router.Use(chiMiddleware.RealIP)
	router.Use(chiMiddleware.Recoverer)

	// Add campaign endpoints
	router.Post("/campaigns", campaignsHandler.HandleCreate)
	router.Get("/campaigns", campaignsHandler.HandleGet)
	router.Patch("/campaigns/{campaignId}", campaignsHandler.HandleUpdate)

	// Mount the chi router to the base mux
	mux.Handle("/", router)

	// =============================================================================
	// HTTP Server setup
	httpServer := &http.Server{
		Addr:    ":9090",
		Handler: observability.NewHTTPHandler(mux),
	}

	log.Info("starting HTTP API server", zap.String("address", httpServer.Addr))

	// Mark service as ready after initialization
	ready.Ready()

	// Start the server in a goroutine
	go httputil.StartServer(httpServer, log)

	// =============================================================================
	// Start the shutdown listener
	<-ctx.Done()
	log.Info("shutting down")
	ready.Unready()

	// Allow some time to shut down everything gracefully
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err = httpServer.Shutdown(ctx)
	if err != nil {
		log.Fatal("failed to shutdown server", zap.Error(err))
	}

	log.Info("shutdown reached")
	log.Info("killing app")
}
