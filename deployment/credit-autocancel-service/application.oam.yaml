---
apiVersion: core.oam.dev/v1beta1
kind: Application
metadata:
  labels:
    environment: stage
    partition: tam-expansion
    region: eu-west-1
    squad: rte-platform
    tribe: rte-expansion
  name: credit-autocancel-service
spec:
  components:
  - name: api
    type: hf-webservice
    properties:
      env:
        PORT: "9090"
        APP_ENV: "development"
        AUTH_URL: "http://auth-service-api.consumer-core.legacy"
        PRICE_SERVICE_URL: "http://price-service-api.consumer-core.legacy"
        VOUCHER_SERVICE_URL: "http://voucher-service.conversions.legacy"
        CPS_BASE_URL: "http://customer-plans-service-api.consumer-core.legacy"
        BALANCE_SERVICE_URL: "http://balance-service-api.payments.legacy"
        COUNTRY_CODES: "AO,AT,AU,BE,CA,CF,CG,CH,CK,DE,DK,ER,ES,FJ,FR,GB,GN,GQ,IE,IT,KN,LU,MR,NL,NO,NZ,SE,TK,TV,TZ,US,YE"
        DB_HOST: "credit-autocancel-service-db000.staging.hellofresh.io"
        DB_PORT: "5432"
        DB_USER: "creditautocancelservice"
        DB_NAME: creditautocancelservicedb

      image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-api
      version: latest
      ports:
      - containerPort: 9090
        port: 80
        protocol: http
      cpu: 0.2
      memory: 200Mi
      probes:
        liveness:
          failureThreshold: 3
          initialDelaySeconds: 15
          path: /alive
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 3
        readiness:
          failureThreshold: 3
          initialDelaySeconds: 15
          path: /health
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 3
    traits:
    - properties:
        secrets:
        - name: app
      type: secrets
    - type: east-west-auth
      properties:
        externalIdentities:
        - partition: legacy
          namespace: conversions
          name: credit-autocancel-service-api
  - name: job
    type: hf-webservice
    properties:
      env:
        PORT: "9090"
        APP_ENV: "development"
        JOB_INTERVAL: "12h"
        DB_HOST: "credit-autocancel-service-db000.staging.hellofresh.io"
        DB_PORT: "5432"
        DB_USER: "creditautocancelservice"
        DB_NAME: creditautocancelservicedb

      image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-job
      version: latest
      ports:
      - containerPort: 9090
        port: 80
        protocol: http
      cpu: 0.2
      memory: 200Mi
      probes:
        liveness:
          failureThreshold: 3
          initialDelaySeconds: 15
          path: /alive
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 3
        readiness:
          failureThreshold: 3
          initialDelaySeconds: 15
          path: /health
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 3
    traits:
    - properties:
        secrets:
        - name: app
      type: secrets

  - name: benthos
    type: hf-webservice
    properties:
      env:
        PORT: "9090"
        APP_ENV: "development"
        AUTH_URL: "http://auth-service-api.consumer-core.legacy"
        PRICE_SERVICE_URL: "http://price-service-api.consumer-core.legacy"
        VOUCHER_SERVICE_URL: "http://voucher-service.conversions.legacy"
        CPS_BASE_URL: "http://customer-plans-service-api.consumer-core.legacy"
        BALANCE_SERVICE_URL: "http://balance-service-api.payments.legacy"
        DB_HOST: "credit-autocancel-service-db000.staging.hellofresh.io"
        DB_PORT: "5432"
        DB_USER: "creditautocancelservice"
        DB_NAME: creditautocancelservicedb
        # Kafka
        KAFKA_DSN: "kafka-staging-hellofresh.aivencloud.com:23419"
        BENTHOS_KAFKA_CONSUMER_GROUP_INGEST: "credit-autocancel-service-benthos-ingest"
        BENTHOS_KAFKA_CONSUMER_GROUP_ORDER_PAID: "credit-autocancel-service-benthos-order-paid"
        BENTHOS_KAFKA_CONSUMER_GROUP_BENEFIT_ATTACHED: "credit-autocancel-service-benthos-benefit-attached"
        BENTHOS_KAFKA_START_FROM_OLDEST: "true"

      image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-benthos
      version: latest
      ports:
      - containerPort: 9090
        port: 80
        protocol: http
      cpu: 0.2
      memory: 200Mi
      probes:
        liveness:
          failureThreshold: 3
          initialDelaySeconds: 15
          path: /alive
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 3
        readiness:
          failureThreshold: 3
          initialDelaySeconds: 15
          path: /health
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 3
    traits:
      - type: secrets
        properties:
          secrets:
            - name: app
      - type: dependencies
        properties:
          meshInternals:
            - name: auth-service-api
              namespace: consumer-core
              partition: legacy
            - name: price-service-api
              namespace: consumer-core
              partition: legacy
            - name: voucher-service
              namespace: conversions
              partition: legacy
            - name: customer-plans-service-api
              namespace: consumer-core
              partition: legacy
            - name: balance-service-api
              namespace: payments
              partition: legacy
          meshExternals: []
      - type: east-west-auth
        properties:
          externalIdentities:
            - partition: legacy       # To allow connection to legacy cluster
              namespace: conversions
              name: credit-autocancel-service-benthos        # Service Account name

  policies:
  - properties: {}
    type: secrets
    name: secrets
  - properties: {}
    type: dependencies
    name: dependencies
