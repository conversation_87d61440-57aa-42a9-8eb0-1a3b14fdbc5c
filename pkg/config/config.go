// Package config holds all the config for job
package config

import (
	"time"

	"github.com/kelseyhightower/envconfig"

	"github.com/hellofresh/credit-autocancel-service/internal/database"
	"github.com/hellofresh/growth-go-kit/observability"
)

type (
	// JobConfig holds all configurations for job
	JobConfig struct {
		Port        int           `envconfig:"PORT" default:"9090"`
		JobInterval time.Duration `envconfig:"JOB_INTERVAL"`
		CPSBaseURL  string        `envconfig:"CPS_BASE_URL"`
		AppEnv      string        `envconfig:"APP_ENV" default:"development"`

		Database database.Config
		View     observability.ViewConfig
		Trace    observability.TraceConfig
	}
)

// LoadConfig returns a JobConfig object with its fields populated by the environment
func LoadConfig() (*JobConfig, error) {
	cfg := &JobConfig{}
	err := envconfig.Process("", cfg)
	if err != nil {
		return cfg, err
	}

	return cfg, nil
}
