package config_test

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/hellofresh/credit-autocancel-service/pkg/config"
)

// TestLoadConfig loads the configurations
func TestLoadConfig(t *testing.T) {
	setGlobalConfigEnv(t)

	cfg, err := config.LoadConfig()
	assert.NoError(t, err)

	assertConfig(t, cfg)
}

// TestDefaultConfig tests the LoadConfig method
func TestDefaultConfig(t *testing.T) {
	cfg, err := config.LoadConfig()
	assert.NoError(t, err)

	assertDefaults(t, cfg)
}

// assertDefaults uses assert to validate that all configs with default values get those values
func assertDefaults(t *testing.T, cfg *config.JobConfig) {
	assert.Equal(t, "postgres", cfg.Database.DBHost)
}

// assertConfig uses assert to validate that all configs in cfg match os env values
func assertConfig(t *testing.T, cfg *config.JobConfig) {
	assert.Equal(t, "http://cpi.staging-k8s.hellofresh.io", cfg.CPSBaseURL)
	assert.Equal(t, "credit_autocancel_db", cfg.Database.DBName)

	assert.Equal(t, true, cfg.View.EnablePrometheus)
	assert.Equal(t, "test-namespace", cfg.View.PrometheusNamespace)

	assert.Equal(t, true, cfg.Trace.EnableOTLPGRPC)
	assert.Equal(t, "otlp-collector", cfg.Trace.OTLPHost)
	assert.Equal(t, "4317", cfg.Trace.OTLPGRPCPort)
	assert.Equal(t, "4318", cfg.Trace.OTLPHTTPPort)
	assert.Equal(t, 0.5, cfg.Trace.SamplerProbability)
}

// setGlobalConfigEnv sets the environment variables to the given strings
// nolint:usetesting
func setGlobalConfigEnv(t *testing.T) {
	t.Helper()

	assert.NoError(t, os.Setenv("DB_HOST", "postgres"))
	assert.NoError(t, os.Setenv("DB_PORT", "5430"))
	assert.NoError(t, os.Setenv("DB_USER", "user"))
	assert.NoError(t, os.Setenv("DB_NAME", "credit_autocancel_db"))
	assert.NoError(t, os.Setenv("DB_DSN", "postgres://cmd:@postgres:5432/cmds?sslmode=disable"))
	assert.NoError(t, os.Setenv("DB_PASSWORD", "fresh4you"))
	assert.NoError(t, os.Setenv("JOB_INTERVAL", "5m"))
	assert.NoError(t, os.Setenv("CPS_BASE_URL", "http://cpi.staging-k8s.hellofresh.io"))

	// viewconfig
	assert.NoError(t, os.Setenv("ENABLE_PROMETHEUS", "true"))
	assert.NoError(t, os.Setenv("PROMETHEUS_NAMESPACE", "test-namespace"))

	// traceconfig
	assert.NoError(t, os.Setenv("ENABLE_OTLP_GRPC", "true"))
	assert.NoError(t, os.Setenv("OTLP_EXPORTER_HOST", "otlp-collector"))
	assert.NoError(t, os.Setenv("OTLP_EXPORTER_GRPC_PORT", "4317"))
	assert.NoError(t, os.Setenv("OTLP_EXPORTER_HTTP_PORT", "4318"))
	assert.NoError(t, os.Setenv("SAMPLE_PROBABILITY", "0.5"))
}
