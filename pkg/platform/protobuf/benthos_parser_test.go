package protobuf

import (
	"context"
	"testing"

	"github.com/hellofresh/credit-autocancel-service/integration_tests/testdata"
	v1 "github.com/hellofresh/schema-registry-go/shared/v1"
	protoCustomerBenefitAttachment "github.com/hellofresh/schema-registry-go/stream/customer/benefit/attachment/v2beta1"
	protoCustomer "github.com/hellofresh/schema-registry-go/stream/customer/v1beta2"
	"github.com/redpanda-data/benthos/v4/public/service"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
)

func TestBenthosParserProcess(t *testing.T) {
	t.Parallel()

	res := service.MockResources()
	benthosParser := NewBenthosParser(res)

	ctx := context.Background()

	invalidBusinessDivision := &v1.BusinessDivision{
		RegionCode: "XX",
		Brand:      v1.Brand_BRAND_FACTOR75,
	}

	missingBrand := &v1.BusinessDivision{
		RegionCode: "DE",
	}

	customerEventValueWithInvalidBU, err := proto.Marshal(&protoCustomer.CustomerValue{
		BusinessDivision: invalidBusinessDivision,
		LegacyId:         123456,
	})
	require.NoError(t, err)

	customerEventValueWithMissingBrand, err := proto.Marshal(&protoCustomer.CustomerValue{
		BusinessDivision: missingBrand,
		LegacyId:         123456,
	})
	require.NoError(t, err)

	customerEventValueWithMissingBusinessDivision, err := proto.Marshal(&protoCustomer.CustomerValue{
		LegacyId: 123456,
	})
	require.NoError(t, err)

	customerBenefitAttachmentEventValueWithInvalidBU, err := proto.Marshal(&protoCustomerBenefitAttachment.BenefitAttachmentValue{
		Distributable: &protoCustomerBenefitAttachment.Distributable{
			BusinessDivision: invalidBusinessDivision,
		},
	})
	require.NoError(t, err)

	customerBenefitAttachmentEventValueWithMissingBrand, err := proto.Marshal(&protoCustomerBenefitAttachment.BenefitAttachmentValue{
		Distributable: &protoCustomerBenefitAttachment.Distributable{
			BusinessDivision: missingBrand,
		},
	})
	require.NoError(t, err)

	customerBenefitAttachmentEventValueWithMissingBusinessDivision, err := proto.Marshal(&protoCustomerBenefitAttachment.BenefitAttachmentValue{
		Distributable: &protoCustomerBenefitAttachment.Distributable{},
	})
	require.NoError(t, err)

	customerBenefitAttachmentEventValueWithMissingDistributable, err := proto.Marshal(&protoCustomerBenefitAttachment.BenefitAttachmentValue{
		Distributable: nil,
	})
	require.NoError(t, err)

	tests := []struct {
		name    string
		message func() *service.Message
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "missing topic",
			message: func() *service.Message {
				return service.NewMessage([]byte("event"))
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "kafka_topic not found")
			},
		},
		{
			name: "missing customer order key",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("event"))
				msg.MetaSet("kafka_topic", testdata.CustomerOrderEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "kafka_key not found")
			},
		},
		{
			name: "missing customer order delivery key",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("event"))
				msg.MetaSet("kafka_topic", testdata.DeliveryEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "kafka_key not found")
			},
		},
		{
			name: "missing customer order payment key",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("event"))
				msg.MetaSet("kafka_topic", testdata.PaymentEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "kafka_key not found")
			},
		},
		{
			name: "missing customer key",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("event"))
				msg.MetaSet("kafka_topic", testdata.CustomerEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "kafka_key not found")
			},
		},
		{
			name: "customer order event success",
			message: func() *service.Message {
				msg := service.NewMessage(testdata.CustomerOrderEvent.Value)
				msg.MetaSet("kafka_key", string(testdata.CustomerOrderEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerOrderEvent.Topic)
				return msg
			},
		},
		{
			name: "customer order event key unmarshal error",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("event"))
				msg.MetaSet("kafka_key", "key")
				msg.MetaSet("kafka_topic", testdata.CustomerOrderEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.ErrorContains(t, err, "failed to unmarshal order protobuf header")
			},
		},
		{
			name: "customer order event value unmarshal error",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("not a customer order event"))
				msg.MetaSet("kafka_key", string(testdata.CustomerOrderEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerOrderEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.ErrorContains(t, err, "failed to unmarshal order protobuf message")
			},
		},
		{
			name: "delivery event success",
			message: func() *service.Message {
				msg := service.NewMessage(testdata.DeliveryEvent.Value)
				msg.MetaSet("kafka_key", string(testdata.DeliveryEvent.Key))
				msg.MetaSet("kafka_topic", testdata.DeliveryEvent.Topic)
				return msg
			},
		},
		{
			name: "delivery event key unmarshal error",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("event"))
				msg.MetaSet("kafka_key", "key")
				msg.MetaSet("kafka_topic", testdata.DeliveryEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.ErrorContains(t, err, "failed to unmarshal order delivery protobuf header")
			},
		},
		{
			name: "delivery event value unmarshal error",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("not a delivery event"))
				msg.MetaSet("kafka_key", string(testdata.DeliveryEvent.Key))
				msg.MetaSet("kafka_topic", testdata.DeliveryEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.ErrorContains(t, err, "failed to unmarshal order delivery protobuf message")
			},
		},
		{
			name: "payment event success",
			message: func() *service.Message {
				msg := service.NewMessage(testdata.PaymentEvent.Value)
				msg.MetaSet("kafka_key", string(testdata.PaymentEvent.Key))
				msg.MetaSet("kafka_topic", testdata.PaymentEvent.Topic)
				return msg
			},
		},
		{
			name: "payment event key unmarshal error",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("event"))
				msg.MetaSet("kafka_key", "key")
				msg.MetaSet("kafka_topic", testdata.PaymentEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.ErrorContains(t, err, "failed to unmarshal order payment protobuf header")
			},
		},
		{
			name: "payment event value unmarshal error",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("not a payment event"))
				msg.MetaSet("kafka_key", string(testdata.PaymentEvent.Key))
				msg.MetaSet("kafka_topic", testdata.PaymentEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.ErrorContains(t, err, "failed to unmarshal order payment protobuf message")
			},
		},
		{
			name: "customer event success",
			message: func() *service.Message {
				msg := service.NewMessage(testdata.CustomerEvent.Value)
				msg.MetaSet("kafka_key", string(testdata.CustomerEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerEvent.Topic)
				return msg
			},
		},
		{
			name: "customer event key unmarshal error",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("event"))
				msg.MetaSet("kafka_key", "key")
				msg.MetaSet("kafka_topic", testdata.CustomerEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "failed to parse customer UUID: invalid UUID length: 3")
			},
		},
		{
			name: "customer event missing business division",
			message: func() *service.Message {
				msg := service.NewMessage(customerEventValueWithMissingBusinessDivision)
				msg.MetaSet("kafka_key", string(testdata.CustomerEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "failed to get business division from customer")
			},
		},
		{
			name: "customer event business division missing brand",
			message: func() *service.Message {
				msg := service.NewMessage(customerEventValueWithMissingBrand)
				msg.MetaSet("kafka_key", string(testdata.CustomerEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "failed to get brand from business division")
			},
		},
		{
			name: "customer event invalid business division",
			message: func() *service.Message {
				msg := service.NewMessage(customerEventValueWithInvalidBU)
				msg.MetaSet("kafka_key", string(testdata.CustomerEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "failed to find business division: unable to find merchant for country \"XX\" for brand 5 (\"BRAND_FACTOR75\"): merchant not found")
			},
		},
		{
			name: "customer event value unmarshal error",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("not a customer event"))
				msg.MetaSet("kafka_key", string(testdata.CustomerEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.ErrorContains(t, err, "failed to unmarshal customer protobuf message")
			},
		},
		{
			name: "customer benefit attachment event success",
			message: func() *service.Message {
				msg := service.NewMessage(testdata.CustomerBenefitAttachmentEvent.Value)
				msg.MetaSet("kafka_key", string(testdata.CustomerBenefitAttachmentEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerBenefitAttachmentEvent.Topic)
				return msg
			},
		},
		{
			name: "customer benefit attachment event business division missing brand",
			message: func() *service.Message {
				msg := service.NewMessage(customerBenefitAttachmentEventValueWithMissingBrand)
				msg.MetaSet("kafka_key", string(testdata.CustomerBenefitAttachmentEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerBenefitAttachmentEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "failed to get brand from business division")
			},
		},
		{
			name: "customer benefit attachment event missing business division",
			message: func() *service.Message {
				msg := service.NewMessage(customerBenefitAttachmentEventValueWithMissingBusinessDivision)
				msg.MetaSet("kafka_key", string(testdata.CustomerBenefitAttachmentEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerBenefitAttachmentEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "failed to get business division from customer")
			},
		},
		{
			name: "customer benefit attachment event invalid business division",
			message: func() *service.Message {
				msg := service.NewMessage(customerBenefitAttachmentEventValueWithInvalidBU)
				msg.MetaSet("kafka_key", string(testdata.CustomerBenefitAttachmentEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerBenefitAttachmentEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "failed to find business division: unable to find merchant for country \"XX\" for brand 5 (\"BRAND_FACTOR75\"): merchant not found")
			},
		},
		{
			name: "customer benefit attachment event value unmarshal error",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("not a customer benefit attachment event"))
				msg.MetaSet("kafka_key", string(testdata.CustomerBenefitAttachmentEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerBenefitAttachmentEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.ErrorContains(t, err, "failed to unmarshal customer benefit attachment protobuf message")
			},
		},
		{
			name: "customer benefit attachment event missing distributable",
			message: func() *service.Message {
				msg := service.NewMessage(customerBenefitAttachmentEventValueWithMissingDistributable)
				msg.MetaSet("kafka_key", string(testdata.CustomerBenefitAttachmentEvent.Key))
				msg.MetaSet("kafka_topic", testdata.CustomerBenefitAttachmentEvent.Topic)
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "failed to get distributable from benefit attachment")
			},
		},
		{
			name: "unsupported kafka_topic",
			message: func() *service.Message {
				msg := service.NewMessage([]byte("event"))
				msg.MetaSet("kafka_key", "key")
				msg.MetaSet("kafka_topic", "topic")
				return msg
			},
			wantErr: func(t assert.TestingT, err error, _ ...interface{}) bool {
				return assert.EqualError(t, err, "unsupported kafka_topic: \"topic\"")
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			msg := tt.message()
			got, err := benthosParser.Process(
				ctx,
				msg,
			)

			if tt.wantErr == nil {
				assert.NoError(t, err)
				assert.Len(t, got, 1)
				assert.Equal(t, msg, got[0])
			} else {
				tt.wantErr(t, err)
			}
		})
	}

	require.NoError(t, benthosParser.Close(ctx))
}
