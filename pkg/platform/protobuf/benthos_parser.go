package protobuf

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/google/uuid"
	"github.com/hellofresh/growth-go-kit/merchant"
	protoCustomerBenefitAttachment "github.com/hellofresh/schema-registry-go/stream/customer/benefit/attachment/v2beta1"
	protoCustomerOrderDelivery "github.com/hellofresh/schema-registry-go/stream/customer/order/delivery/v1"
	protoCustomerOrderPayment "github.com/hellofresh/schema-registry-go/stream/customer/order/payment/v1"
	protoCustomerOrder "github.com/hellofresh/schema-registry-go/stream/customer/order/v1"
	protoCustomer "github.com/hellofresh/schema-registry-go/stream/customer/v1beta2"
	"github.com/redpanda-data/benthos/v4/public/service"
	"google.golang.org/protobuf/proto"
)

// BenthosParser converts certain data from Protobuf messages to Benthos metadata.
type BenthosParser struct {
	Resources *service.Resources
	Merchant  *merchant.Merchants
}

// NewBenthosParser initiates a new BenthosParser
func NewBenthosParser(res *service.Resources) *BenthosParser {
	return &BenthosParser{
		Resources: res,
		Merchant:  merchant.LoadMerchants(),
	}
}

// processCustomerOrderMessage processes a customer order message and sets the metadata.
func (h *BenthosParser) processCustomerOrderMessage(message *service.Message, kafkaMessage []byte) ([]*service.Message, error) {
	kafkaHeader, ok := message.MetaGet("kafka_key")
	if !ok {
		return nil, errors.New("kafka_key not found")
	}

	var protoHeader protoCustomerOrder.CustomerOrderKey
	err := proto.Unmarshal([]byte(kafkaHeader), &protoHeader)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal order protobuf header: %w", err)
	}

	var protoMessage protoCustomerOrder.CustomerOrderValue
	err = proto.Unmarshal(kafkaMessage, &protoMessage)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal order protobuf message: %w", err)
	}

	message.MetaSet("hf_business_unit", strings.ToLower(protoHeader.GetRegionCode()))
	message.MetaSet("hf_customer_plan_id", protoMessage.GetPlanId())
	message.MetaSet("hf_order_voucher_code", protoMessage.Items[0].GetVoucherCode())
	message.MetaSet("hf_is_subscription_initial_order", strconv.FormatBool(protoMessage.GetIsSubscriptionInitialOrder()))
	message.MetaSet("hf_order_uuid", protoHeader.GetId())

	h.Resources.Logger().With("header", &protoHeader, "body", &protoMessage).Debug("received order message")

	return []*service.Message{message}, nil
}

// processCustomerOrderDeliveryMessage processes a customer order delivery message and sets the metadata.
func (h *BenthosParser) processCustomerOrderDeliveryMessage(message *service.Message, kafkaMessage []byte) ([]*service.Message, error) {
	kafkaHeader, ok := message.MetaGet("kafka_key")
	if !ok {
		return nil, errors.New("kafka_key not found")
	}

	var protoHeader protoCustomerOrderDelivery.DeliveryDetailsKey
	err := proto.Unmarshal([]byte(kafkaHeader), &protoHeader)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal order delivery protobuf header: %w", err)
	}

	var protoMessage protoCustomerOrderDelivery.DeliveryDetailsValue
	err = proto.Unmarshal(kafkaMessage, &protoMessage)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal order delivery protobuf message: %w", err)
	}

	message.MetaSet("hf_business_unit", protoHeader.GetRegionCode())
	message.MetaSet("hf_order_uuid", protoHeader.GetId())

	h.Resources.Logger().With("header", &protoHeader, "body", &protoMessage).Debug("received order delivery message")

	return []*service.Message{message}, nil
}

// processCustomerOrderPaymentMessage processes a customer order payment message and sets the metadata.
func (h *BenthosParser) processCustomerOrderPaymentMessage(message *service.Message, kafkaMessage []byte) ([]*service.Message, error) {
	kafkaHeader, ok := message.MetaGet("kafka_key")
	if !ok {
		return nil, errors.New("kafka_key not found")
	}

	var protoHeader protoCustomerOrderPayment.PaymentDetailsKey
	err := proto.Unmarshal([]byte(kafkaHeader), &protoHeader)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal order payment protobuf header: %w", err)
	}

	var protoMessage protoCustomerOrderPayment.PaymentDetailsValue
	err = proto.Unmarshal(kafkaMessage, &protoMessage)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal order payment protobuf message: %w", err)
	}

	message.MetaSet("hf_business_unit", protoHeader.GetRegionCode())
	message.MetaSet("hf_order_uuid", protoHeader.GetId())
	message.MetaSet("hf_payment_state", strconv.Itoa(int(protoMessage.GetState())))

	h.Resources.Logger().With("header", &protoHeader, "body", &protoMessage).Debug("received order payment message")

	return []*service.Message{message}, nil
}

// processCustomerMessage processes a customer message and sets the metadata.
func (h *BenthosParser) processCustomerMessage(message *service.Message, kafkaMessage []byte) ([]*service.Message, error) {
	kafkaHeader, ok := message.MetaGet("kafka_key")
	if !ok {
		return nil, errors.New("kafka_key not found")
	}

	var customerUUID uuid.UUID
	customerUUID, err := uuid.Parse(kafkaHeader)
	if err != nil {
		return nil, fmt.Errorf("failed to parse customer UUID: %w", err)
	}

	var protoMessage protoCustomer.CustomerValue
	err = proto.Unmarshal(kafkaMessage, &protoMessage)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal customer protobuf message: %w", err)
	}

	if protoMessage.GetBusinessDivision() == nil {
		return nil, errors.New("failed to get business division from customer")
	}
	if protoMessage.GetBusinessDivision().GetBrand() == 0 {
		return nil, errors.New("failed to get brand from business division")
	}

	m, err := h.Merchant.FindByBusinessDivision(protoMessage.GetBusinessDivision())
	if err != nil {
		return nil, fmt.Errorf("failed to find business division: %w", err)
	}

	message.MetaSet("hf_business_unit", strings.ToLower(m.BusinessUnit))
	message.MetaSet("hf_customer_id", strconv.FormatInt(protoMessage.GetLegacyId(), 10)) // nolint:staticcheck
	message.MetaSet("hf_customer_uuid", customerUUID.String())

	h.Resources.Logger().With("header", kafkaHeader, "body", &protoMessage).Debug("received customer message")

	return []*service.Message{message}, nil
}

// processCustomerBenefitAttachmentMessage processes a customer benefit attachment message and sets the metadata.
func (h *BenthosParser) processCustomerBenefitAttachmentMessage(message *service.Message, kafkaMessage []byte) ([]*service.Message, error) {
	var protoMessage protoCustomerBenefitAttachment.BenefitAttachmentValue
	err := proto.Unmarshal(kafkaMessage, &protoMessage)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal customer benefit attachment protobuf message: %w", err)
	}

	distributable := protoMessage.GetDistributable()
	if distributable == nil {
		return nil, errors.New("failed to get distributable from benefit attachment")
	}

	if distributable.GetBusinessDivision() == nil {
		return nil, errors.New("failed to get business division from customer")
	}
	if distributable.GetBusinessDivision().GetBrand() == 0 {
		return nil, errors.New("failed to get brand from business division")
	}

	m, err := h.Merchant.FindByBusinessDivision(distributable.GetBusinessDivision())
	if err != nil {
		return nil, fmt.Errorf("failed to find business division: %w", err)
	}

	message.MetaSet("hf_business_unit", strings.ToLower(m.BusinessUnit))
	message.MetaSet("hf_customer_plan_id", protoMessage.GetCustomerPlanId())
	message.MetaSet("hf_voucher_code", distributable.GetReference())
	message.MetaSet("hf_error_code", strconv.Itoa(int(protoMessage.GetErrorCode())))

	h.Resources.Logger().With("message", &protoMessage).Debug("received benefit attachment message")

	return []*service.Message{message}, nil
}

// Process converts certain data from Protobuf messages to Benthos metadata.
func (h *BenthosParser) Process(ctx context.Context, message *service.Message) (service.MessageBatch, error) { // nolint:revive
	kafkaTopic, ok := message.MetaGet("kafka_topic")
	if !ok {
		return nil, errors.New("kafka_topic not found")
	}

	kafkaMessage, err := message.AsBytes()
	if err != nil {
		return nil, fmt.Errorf("failed to get message bytes: %w", err)
	}

	switch kafkaTopic {
	case "public.customer.order.v1":
		return h.processCustomerOrderMessage(message, kafkaMessage)
	case "public.customer.order.delivery.v1":
		return h.processCustomerOrderDeliveryMessage(message, kafkaMessage)
	case "public.customer.order.payment.v1":
		return h.processCustomerOrderPaymentMessage(message, kafkaMessage)
	case "public.customer.v1beta2":
		return h.processCustomerMessage(message, kafkaMessage)
	case "public.customer.benefit.attachment.v2beta1":
		return h.processCustomerBenefitAttachmentMessage(message, kafkaMessage)
	}

	return nil, fmt.Errorf("unsupported kafka_topic: %q", kafkaTopic)
}

// Close is a stub implementation.
func (h *BenthosParser) Close(_ context.Context) error {
	return nil
}
