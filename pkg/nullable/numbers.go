package nullable

import (
	"strconv"
)

// Itoa converts an integer pointer to a string scalar
func Itoa(v *int) string {
	if v == nil {
		return ""
	}

	return strconv.Itoa(*v)
}

// ToZeroInteger converts an integer pointer to a zero integer
func ToZeroInteger(v *int) int {
	if v == nil {
		return 0
	}

	return *v
}

// ToZeroInteger64 converts an integer64 pointer to a zero integer64
func ToZeroInteger64(v *int64) int64 {
	if v == nil {
		return int64(0)
	}

	return *v
}

// ToZeroFloat64 converts a pointer of type float64 to a float64
func ToZeroFloat64(v *float64) float64 {
	if v == nil {
		return float64(0.00)
	}

	return *v
}

// EmptyIntToNil converts empty int to nil
func EmptyIntToNil(value int) *int {
	if value == 0 {
		return nil
	}
	return &value
}
