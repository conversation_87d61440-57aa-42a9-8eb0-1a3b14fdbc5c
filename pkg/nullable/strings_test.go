package nullable_test

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/hellofresh/credit-autocancel-service/pkg/nullable"
)

func TestEmptyStringToNil(t *testing.T) {
	type args struct {
		value string
	}
	tests := []struct {
		name string
		args args
		want *string
	}{
		// TODO: Add test cases.
		{
			name: "testEmptyStringToNil",
			args: args{value: ""},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := nullable.EmptyStringToNil(tt.args.value); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rf("EmptyStringToNil() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNullableToEmptyString(t *testing.T) {
	sample := "5"

	scenarios := []struct {
		description string
		input       *string
		expected    string
	}{
		{
			description: "value is null",
			input:       nil,
			expected:    "",
		},
		{
			description: "value is an string",
			input:       &sample,
			expected:    "5",
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.description, func(t *testing.T) {
			assert.Equal(t, scenario.expected, nullable.ToEmptyString(scenario.input))
		})
	}
}
