package nullable_test

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/hellofresh/credit-autocancel-service/pkg/nullable"
)

func TestNullableItoa(t *testing.T) {
	sample := 5

	scenarios := []struct {
		description string
		input       *int
		expected    string
	}{
		{
			description: "value is null",
			input:       nil,
			expected:    "",
		},
		{
			description: "value is an int",
			input:       &sample,
			expected:    "5",
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.description, func(t *testing.T) {
			assert.Equal(t, scenario.expected, nullable.Itoa(scenario.input))
		})
	}
}

func TestNullableToZeroInteger(t *testing.T) {
	sample := 5

	scenarios := []struct {
		description string
		input       *int
		expected    int
	}{
		{
			description: "value is null",
			input:       nil,
			expected:    0,
		},
		{
			description: "value is a positive int",
			input:       &sample,
			expected:    5,
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.description, func(t *testing.T) {
			assert.Equal(t, scenario.expected, nullable.ToZeroInteger(scenario.input))
		})
	}
}

func TestNullableToZeroInteger64(t *testing.T) {
	sample := int64(5)

	scenarios := []struct {
		description string
		input       *int64
		expected    int64
	}{
		{
			description: "value is null",
			input:       nil,
			expected:    int64(0),
		},
		{
			description: "value is a positive int64",
			input:       &sample,
			expected:    sample,
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.description, func(t *testing.T) {
			assert.Equal(t, scenario.expected, nullable.ToZeroInteger64(scenario.input))
		})
	}
}

func TestNullableToZeroFloat64(t *testing.T) {
	sample := 123.45

	scenarios := []struct {
		description string
		input       *float64
		expected    float64
	}{
		{
			description: "value is null",
			input:       nil,
			expected:    0.00,
		},
		{
			description: "value is a positive int",
			input:       &sample,
			expected:    123.45,
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.description, func(t *testing.T) {
			assert.Equal(t, scenario.expected, nullable.ToZeroFloat64(scenario.input))
		})
	}
}

func TestEmptyIntToNil(t *testing.T) {
	expected := 5

	type args struct {
		value int
	}
	tests := []struct {
		name string
		args args
		want *int
	}{
		{
			name: "converts 0 value to nil",
			args: args{value: 0},
			want: nil,
		},
		{
			name: "converts non-zero value to pointer",
			args: args{value: 5},
			want: &expected,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.want, nullable.EmptyIntToNil(tt.args.value))
		})
	}
}
