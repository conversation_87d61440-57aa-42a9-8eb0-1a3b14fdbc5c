version: '2.2'
services:
  credit_autocancel_service_api:
    build:
      context: ../
      dockerfile: ./testing/test.Dockerfile
      args:
        GITHUB_TOKEN: "${GITHUB_TOKEN}"
    ports:
      - "9090:9090"
    environment:
      - PORT=9090
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=credit-autocancel-service
      - DB_PASSWORD=freshforyou
      - DB_NAME=creditautocanceldb
      - AMQP_DSN=amqp://credit-autocancel-service:credit-autocancel-service@rabbit:5672
      - AUTH_URL=http://wiremock:8080
      - PRICE_SERVICE_URL=http://wiremock:8080
      - VOUCHER_SERVICE_URL=http://wiremock:8080
      - BALANCE_SERVICE_URL=http://wiremock:8080
      - CLIENT_ID=id
      - CLIENT_SECRET=secret
      - COUNTRY_CODES=AO,AT,AU,BE,CA,CG,CH,CK,DE,DK,ER,FR,GB,GN,LU,NL,NZ,SE,US
      - CPS_BASE_URL=http://wiremock:8080
    command: /bin/sh -c "/credit-autocancel-service -migrate && /credit-autocancel-service"
    depends_on:
      postgres:
        condition: service_healthy
      rabbit:
        condition: service_healthy
      outbox-worker:
        condition: service_started
      wiremock:
        condition: service_started

  credit_autocancel_service_job:
    build:
      context: ../
      dockerfile: ./testing/test.Dockerfile
      args:
        GITHUB_TOKEN: "${GITHUB_TOKEN}"
    ports:
      - "9091:9090"
    environment:
      - AMQP_DSN=amqp://credit-autocancel-service:credit-autocancel-service@rabbit:5672
      - AUTH_URL=http://wiremock:8080
      - CLIENT_ID=id
      - CLIENT_SECRET=secret
      - COUNTRY_CODES=AO,AT,AU,BE,CA,CG,CH,CK,DE,DK,ER,FR,GB,GN,LU,NL,NZ,SE,US
      - DB_HOST=postgres
      - DB_NAME=creditautocanceldb
      - DB_PASSWORD=freshforyou
      - DB_PORT=5432
      - DB_USER=credit-autocancel-service
      - JOB_INTERVAL=1s
      - PORT=9090
      - PRICE_SERVICE_URL=http://wiremock:8080
      - VOUCHER_SERVICE_URL=http://wiremock:8080
      - BALANCE_SERVICE_URL=http://wiremock:8080
      - CPS_BASE_URL=http://wiremock:8080
    command: /bin/sh -c "sleep 10 && /credit-autocancel-service-job"
    depends_on:
      postgres:
        condition: service_healthy
      rabbit:
        condition: service_healthy
      outbox-worker:
        condition: service_started
      wiremock:
        condition: service_started

  postgres:
    image: postgres:12
    ports:
      - "5432:5432"
    environment:
      LC_ALL: C.UTF-8
      POSTGRES_USER: credit-autocancel-service
      POSTGRES_PASSWORD: freshforyou
      POSTGRES_DB: creditautocanceldb
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U credit-autocancel-service -d creditautocanceldb" ]
      interval: 1s
      timeout: 3s
      retries: 60

  rabbit:
    image: rabbitmq:3.7-management
    ports:
      - "15672:15672"
      - "5672:5672"
    environment:
      - RABBITMQ_DEFAULT_USER=credit-autocancel-service
      - RABBITMQ_DEFAULT_PASS=credit-autocancel-service
    healthcheck:
      test: [ "CMD-SHELL", "rabbitmq-diagnostics -q check_running" ]
      interval: 5s
      timeout: 5s
      retries: 60

  outbox-worker:
    image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/outbox-worker:stable
    depends_on:
      postgres:
        condition: service_healthy
      rabbit:
        condition: service_healthy
    environment:
      - AMQP_DSN=amqp://credit-autocancel-service:@rabbit:5672
      - AMQP_PASSWORD=credit-autocancel-service
      - AMQP_EXCHANGES=[{"name":"customers","type":"topic","durable":true,"arguments":{}}, {"name":"subscriptions_changes_exchange","type":"topic","durable":true,"arguments":{}}]
      - CIRCUIT_BREAKER_NAME=outbox-credit-autocancel-service
      - DB_DSN=postgres://credit-autocancel-service:@postgres:5432/creditautocanceldb?sslmode=disable
      - DB_PASSWORD=freshforyou
      - TRIGGER_INTERVAL=50ms

  wiremock:
    build:
      context: ../testing/mock/wiremock
      dockerfile: test.Dockerfile
    ports:
      - "8080:8080"
