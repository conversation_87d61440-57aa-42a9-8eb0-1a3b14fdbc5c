# Credit Autocancel Service

`credit-autocancel-service` is a service maintained by the RTE Platform Squad to
cancel subscriptions if voucher or gift card does not cover the full price of
the order.

**Contact:**
- Slack Channel: [#squad-rte-platform][squad-slack]

## Quick Links

- Docs:
  - [Contributing Guide][contributing-guide]
  - [Runbook][runbook]
  - [Confluence][confluence]
  - [OpenAPI][docs-openapi]
  - [Database Schema][database-schema]
  - User Flows:
    - [Customer Order Paid][userflow-order-paid]
    - [Customer Journey][userflow-customer-journey]
    - [Auto Cancel Job Flow][userflow-auto-cancel-job]
- Deployment:
  - CI Pipeline:
    - [FTCP][ci-pipeline-ftcp]
    - [Staging][ci-pipeline-staging]
    - [Promote to Live][ci-pipeline-promote]
    - [Live][ci-pipeline-live]
- Metrics:
  - Service Dashboard:
    - [Live][dashboard-live]
    - [Staging][dashboard-staging]
  - Istio Workload:
    - Live:
      - [API][dashboard-istio-live-api]
      - [Job][dashboard-istio-live-job]
      - [Benthos][dashboard-istio-live-benthos]
    - Staging:
      - [API][dashboard-istio-staging-api]
      - [Job][dashboard-istio-staging-job]
      - [Benthos][dashboard-istio-staging-benthos]
  - RED:
    - Live:
      - [API][dashboard-red-live-api]
      - [Job][dashboard-red-live-job]
      - [Benthos][dashboard-red-live-benthos]
    - Staging:
      - [API][dashboard-red-staging-api]
      - [Job][dashboard-red-staging-job]
      - [Benthos][dashboard-red-staging-benthos]
  - SLO:
    - [Live][dashboard-slo-live]
    - [Staging][dashboard-slo-staging]
- Logs:
  - Live:
    - [API][logs-api-live]
    - [Job][logs-job-live]
    - [Benthos][logs-benthos-live]
  - Staging:
    - [API][logs-api-staging]
    - [Job][logs-job-staging]
    - [Benthos][logs-benthos-staging]

## Architecture

The service consists of three main components:
- **Credit Autocancel Service API**: Used to create and retrieve campaigns
- **Credit Autocancel Service Job**: Used to scan database for orders to cancel
- **Credit Autocancel Service Benthos**: Used to read messages from Kafka, write
  to DB and trigger private topic messages for subscription cancellation

```mermaid
graph LR
    Kafka[Kafka Topics] -->|Events| Benthos[Benthos Service]

    subgraph CreditAutocancelService["Credit Autocancel Service"]
        Benthos[cacs-benthos]
        API[cacs-api]
        Job[cacs-job]
    end

    Benthos -->|Store Events| Postgres[PostgreSQL]
    API -->|Create/Manage Campaigns| Postgres
    Job -->|Scan for Cancellations| Postgres
    Job -->|Cancel Subscriptions| CustomerPlans[Customer Plans Service]
    Benthos -->|Query Vouchers| Voucher[Voucher Service]
    Benthos -->|Calculate Prices| Price[Price Service]
    Benthos -->|Get Token| Auth[Auth Service]
```

## Development

See [`CONTRIBUTING.md`][contributing-guide] for detailed setup instructions.

[ci-pipeline-ftcp]: https://github.com/hellofresh/credit-autocancel-service/actions/workflows/deployment.yaml
[ci-pipeline-live]: https://github.com/hellofresh/credit-autocancel-service/actions/workflows/deploy-live.yml
[ci-pipeline-promote]: https://github.com/hellofresh/credit-autocancel-service/actions/workflows/promote-live.yml
[ci-pipeline-staging]: https://github.com/hellofresh/credit-autocancel-service/actions/workflows/deploy-staging.yml
[confluence]: https://hellofresh.atlassian.net/wiki/spaces/TTH/pages/4171334975/Credit+Auto+Cancel+Service
[contributing-guide]: ./CONTRIBUTING.md
[dashboard-istio-live-api]: https://prdhellofresh.grafana.net/d/dxDH55RgFKZnFSgRUeueGZ9hwGqEnlcoLISqEhmE/istio-workload-dashboard?orgId=1&refresh=5m&var-metrics_ds=bcde3167-579a-4ff2-a459-d2748e37f2a0&var-namespace=conversions&var-workload=credit-autocancel-service-api&var-qrep=destination&var-srcns=$__all&var-srcwl=$__all&var-dstsvc=$__all&from=now-24h&to=now&timezone=browser
[dashboard-istio-live-benthos]: https://prdhellofresh.grafana.net/d/dxDH55RgFKZnFSgRUeueGZ9hwGqEnlcoLISqEhmE/istio-workload-dashboard?orgId=1&refresh=5m&var-metrics_ds=bcde3167-579a-4ff2-a459-d2748e37f2a0&var-namespace=conversions&var-workload=credit-autocancel-service-benthos&var-qrep=destination&var-srcns=$__all&var-srcwl=$__all&var-dstsvc=$__all&from=now-24h&to=now&timezone=browser
[dashboard-istio-live-job]: https://prdhellofresh.grafana.net/d/dxDH55RgFKZnFSgRUeueGZ9hwGqEnlcoLISqEhmE/istio-workload-dashboard?orgId=1&refresh=5m&var-metrics_ds=bcde3167-579a-4ff2-a459-d2748e37f2a0&var-namespace=conversions&var-workload=credit-autocancel-service-job&var-qrep=destination&var-srcns=$__all&var-srcwl=$__all&var-dstsvc=$__all&from=now-24h&to=now&timezone=browser
[dashboard-istio-staging-api]: https://stghellofresh.grafana.net/d/fS7gtOmoZMrnbUJceCh8YDXHJWGHtmvSUwqS9dfi/istio-workload-dashboard?orgId=1&refresh=5m&var-metrics_ds=af332bf6-da26-494d-8eb8-b09e897974ad&var-namespace=conversions&var-workload=credit-autocancel-service-api&var-qrep=destination&var-srcns=$__all&var-srcwl=$__all&var-dstsvc=$__all&from=now-24h&to=now&timezone=browser
[dashboard-istio-staging-benthos]: https://stghellofresh.grafana.net/d/fS7gtOmoZMrnbUJceCh8YDXHJWGHtmvSUwqS9dfi/istio-workload-dashboard?orgId=1&refresh=5m&var-metrics_ds=af332bf6-da26-494d-8eb8-b09e897974ad&var-namespace=conversions&var-workload=credit-autocancel-service-benthos&var-qrep=destination&var-srcns=$__all&var-srcwl=$__all&var-dstsvc=$__all&from=now-24h&to=now&timezone=browser
[dashboard-istio-staging-job]: https://stghellofresh.grafana.net/d/fS7gtOmoZMrnbUJceCh8YDXHJWGHtmvSUwqS9dfi/istio-workload-dashboard?orgId=1&refresh=5m&var-metrics_ds=af332bf6-da26-494d-8eb8-b09e897974ad&var-namespace=conversions&var-workload=credit-autocancel-service-job&var-qrep=destination&var-srcns=$__all&var-srcwl=$__all&var-dstsvc=$__all&from=now-24h&to=now&timezone=browser
[dashboard-live]: https://prdhellofresh.grafana.net/goto/A4vlChoNR?orgId=1
[dashboard-red-live-api]: https://prdhellofresh.grafana.net/d/llOkCT07ohy8iTAXRVe6LfLc41n9frvVhqwMFP37/red-dashboard-extended?orgId=1&refresh=5m&var-metrics_ds=bcde3167-579a-4ff2-a459-d2748e37f2a0&var-namespace=conversions&var-podhash=$__all&var-rolloutpodhash=545f9ff66&var-k8s_svc_name=credit-autocancel-service-api&var-period=5m&var-excluded_containers=%28nginx%7Cistio-proxy%7C.%2A-exporter%7Cprometheus-gateway%29&from=now-1h&to=now&timezone=browser&var-revision=$__all
[dashboard-red-live-benthos]: https://prdhellofresh.grafana.net/d/llOkCT07ohy8iTAXRVe6LfLc41n9frvVhqwMFP37/red-dashboard-extended?orgId=1&refresh=5m&var-metrics_ds=bcde3167-579a-4ff2-a459-d2748e37f2a0&var-namespace=conversions&var-podhash=$__all&var-rolloutpodhash=545f9ff66&var-k8s_svc_name=credit-autocancel-service-benthos&var-period=5m&var-excluded_containers=%28nginx%7Cistio-proxy%7C.%2A-exporter%7Cprometheus-gateway%29&from=now-1h&to=now&timezone=browser&var-revision=$__all
[dashboard-red-live-job]: https://prdhellofresh.grafana.net/d/llOkCT07ohy8iTAXRVe6LfLc41n9frvVhqwMFP37/red-dashboard-extended?orgId=1&refresh=5m&var-metrics_ds=bcde3167-579a-4ff2-a459-d2748e37f2a0&var-namespace=conversions&var-podhash=$__all&var-rolloutpodhash=545f9ff66&var-k8s_svc_name=credit-autocancel-service-job&var-period=5m&var-excluded_containers=%28nginx%7Cistio-proxy%7C.%2A-exporter%7Cprometheus-gateway%29&from=now-1h&to=now&timezone=browser&var-revision=$__all
[dashboard-red-staging-api]: https://stghellofresh.grafana.net/d/6kE8pkx5qZi9NNhVXTe64viYGzgERM6Ep4AEF2ia/red-dashboard-extended?orgId=1&refresh=5m&var-metrics_ds=af332bf6-da26-494d-8eb8-b09e897974ad&var-namespace=conversions&var-podhash=$__all&var-rolloutpodhash=5f8dcfdbf5&var-k8s_svc_name=credit-autocancel-service-api&var-period=5m&var-excluded_containers=%28nginx%7Cistio-proxy%7C.%2A-exporter%7Cprometheus-gateway%29&from=now-1h&to=now&timezone=browser&var-revision=$__all
[dashboard-red-staging-benthos]: https://stghellofresh.grafana.net/d/6kE8pkx5qZi9NNhVXTe64viYGzgERM6Ep4AEF2ia/red-dashboard-extended?orgId=1&refresh=5m&var-metrics_ds=af332bf6-da26-494d-8eb8-b09e897974ad&var-namespace=conversions&var-podhash=$__all&var-rolloutpodhash=5f8dcfdbf5&var-k8s_svc_name=credit-autocancel-service-benthos&var-period=5m&var-excluded_containers=%28nginx%7Cistio-proxy%7C.%2A-exporter%7Cprometheus-gateway%29&from=now-1h&to=now&timezone=browser&var-revision=$__all
[dashboard-red-staging-job]: https://stghellofresh.grafana.net/d/6kE8pkx5qZi9NNhVXTe64viYGzgERM6Ep4AEF2ia/red-dashboard-extended?orgId=1&refresh=5m&var-metrics_ds=af332bf6-da26-494d-8eb8-b09e897974ad&var-namespace=conversions&var-podhash=$__all&var-rolloutpodhash=5f8dcfdbf5&var-k8s_svc_name=credit-autocancel-service-job&var-period=5m&var-excluded_containers=%28nginx%7Cistio-proxy%7C.%2A-exporter%7Cprometheus-gateway%29&from=now-1h&to=now&timezone=browser&var-revision=$__all
[dashboard-slo-live]: https://prdhellofresh.grafana.net/d/Atzzwg33XRkEQCXQ9zXyp6vnwXiosBxPxETdk0lk/slo-per-service?orgId=1&var-metrics_ds=bcde3167-579a-4ff2-a459-d2748e37f2a0&var-tribe=rte-expansion&var-service=credit-autocancel-service&var-slo=$__all&var-sli_window=5m&var-min_burning_rate=0&from=now-24h&to=now&timezone=browser
[dashboard-slo-staging]: https://stghellofresh.grafana.net/d/kT1Ce86CyGOVnuRKVjc1FlJfeveB6q3lFhZPjZrg/slo-per-service?orgId=1&var-metrics_ds=af332bf6-da26-494d-8eb8-b09e897974ad&var-tribe=rte-expansion&var-service=credit-autocancel-service&var-slo=$__all&var-sli_window=5m&var-min_burning_rate=0&from=now-24h&to=now&timezone=browser
[dashboard-staging]: https://stghellofresh.grafana.net/goto/U-krjhTNg?orgId=1
[database-schema]: ./docs/database-schema-diagram.png
[docs-openapi]: ./docs/openapi.json
[logs-api-live]: https://prdhellofresh.grafana.net/a/grafana-lokiexplore-app/explore/service/credit-autocancel-service-api/logs?from=now-3h&to=now&var-ds=DbAZrmOVz&var-filters=service_name%7C%3D%7Ccredit-autocancel-service-api&timezone=browser&visualizationType=%22logs%22
[logs-api-staging]: https://stghellofresh.grafana.net/a/grafana-lokiexplore-app/explore/service/credit-autocancel-service-api/logs?from=now-3h&to=now&var-ds=fpy-agdVk&var-filters=service_name%7C%3D%7Ccredit-autocancel-service-api&timezone=browser&visualizationType=%22logs%22
[logs-benthos-live]: https://prdhellofresh.grafana.net/a/grafana-lokiexplore-app/explore/service/credit-autocancel-service-benthos/logs?from=now-3h&to=now&var-ds=DbAZrmOVz&var-filters=service_name%7C%3D%7Ccredit-autocancel-service-benthos&timezone=browser&visualizationType=%22logs%22
[logs-benthos-staging]: https://stghellofresh.grafana.net/a/grafana-lokiexplore-app/explore/service/credit-autocancel-service-benthos/logs?from=now-3h&to=now&var-ds=fpy-agdVk&var-filters=service_name%7C%3D%7Ccredit-autocancel-service-benthos&timezone=browser&visualizationType=%22logs%22
[logs-job-live]: https://prdhellofresh.grafana.net/a/grafana-lokiexplore-app/explore/service/credit-autocancel-service-job/logs?from=now-3h&to=now&var-ds=DbAZrmOVz&var-filters=service_name%7C%3D%7Ccredit-autocancel-service-job&timezone=browser&visualizationType=%22logs%22
[logs-job-staging]: https://stghellofresh.grafana.net/a/grafana-lokiexplore-app/explore/service/credit-autocancel-service-job/logs?from=now-3h&to=now&var-ds=fpy-agdVk&var-filters=service_name%7C%3D%7Ccredit-autocancel-service-job&timezone=browser&visualizationType=%22logs%22
[runbook]: https://github.com/hellofresh/runbooks/tree/master/troubleshooting/rte-expansion/rte-systems/credit-autocancel-service
[squad-slack]: https://hellofresh.slack.com/archives/C08A4BCMD39
[userflow-auto-cancel-job]: ./docs/userflows/auto_cancel_job_flow.md
[userflow-customer-journey]: ./docs/userflows/customer_journey.md
[userflow-order-paid]: ./docs/userflows/customer_order_paid.md
