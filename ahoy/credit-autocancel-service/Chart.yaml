apiVersion: v2
description: A Helm chart for credit-autocancel-service, that handles automatic subscription cancel hen credit runs out
name: credit-autocancel-service
version: 0.0.1
appVersion: 0.0.1
dependencies:
  - name: outbox-worker-amqp
    version: "*"
    repository: https://artifactory.tools-k8s.hellofresh.io/artifactory/helm
    condition: outbox-worker-rabbit.enabled
  - name: hf-prometheus-rds-exporter
    condition: hf-prometheus-rds-exporter.enabled
    version: ~2.x
    repository: "https://artifactory.tools-k8s.hellofresh.io/artifactory/helm"
  - name: hf-prometheus-rabbitmq-exporter
    version: ~2.x
    repository: https://artifactory.tools-k8s.hellofresh.io/artifactory/helm
