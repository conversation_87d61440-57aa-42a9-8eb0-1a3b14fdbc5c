environment: &environment live
tribe: &tribe rte-expansion
squad: &squad rte-platform
slack: &slack squad-rte-systems-alerts
service: &service credit-autocancel-service
vaultNamespace: &vaultNamespace services/credit-autocancel-service

jobs:
  migrate:
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-api
    pullPolicy: Always
    restartPolicy: Never
    command: ["/bin/sh"]
    args:
      - "-c"
      - |
        /migrate -database="$DATABASE_DSN" -path=migrations up
    env:
      DATABASE_DSN: "postgres://creditautocancelservice:${vault:live/key-value/data/db#password}@credit-autocancel-service-db000.live.hellofresh.io:5432/creditautocancelservicedb?sslmode=disable"
    annotations:
      "helm.sh/hook": post-install, post-upgrade
      "helm.sh/hook-delete-policy": hook-succeeded, hook-failed

deployment-config: &deployment-config
  containerPorts:
    http: 9090
  pullPolicy: IfNotPresent
  podAnnotations:
    sidecar.istio.io/inject: "true" # enable if istio sidecar is enabled
  resources:
    requests:
      cpu: 250m
      memory: 100Mi
  nodeSelector: {}
  tolerations: []
  affinity: {}
  hpa:
    enabled: false

deployments:
  api:
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-api
    replicaCount: 1
    <<: *deployment-config
    livenessProbe:
      httpGet:
        path: /alive
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /health
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      runAsNonRoot: true

  job:
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-job
    replicaCount: 1
    <<: *deployment-config
    livenessProbe:
      httpGet:
        path: /alive
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /health
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      runAsNonRoot: true

  benthos:
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-benthos
    replicaCount: 2
    minAvailable: 1
    <<: *deployment-config
    deploymentStrategy:
      type: Recreate
    livenessProbe:
      httpGet:
        path: /alive
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /health
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3

services:
  api:
    enabled: true
    enablePrometheus: true
    type: ClusterIP
    ports:
      http: 80
  job:
    enabled: true
    enablePrometheus: true
    type: ClusterIP
    ports:
      http: 80
  benthos:
    enabled: true
    enablePrometheus: true
    type: ClusterIP
    ports:
      http: 80

ingresses:
  api:
    enabled: true
    path: /
    hosts:
      http: credit-autocancel-service.live-k8s.hellofresh.io
    class: edge-stack-private
  job:
    enabled: false
    class: edge-stack-private
  benthos:
    enabled: false
    class: edge-stack-private

configMap:
  PORT: "9090"
  APP_ENV: "production"
  AUTH_URL: "http://auth-service-api-k8s.consumer-core.svc.cluster.local"
  PRICE_SERVICE_URL: "http://price-service-k8s.consumer-core.svc.cluster.local"
  VOUCHER_SERVICE_URL: "http://voucher-service-k8s.conversions.svc.cluster.local"
  BALANCE_SERVICE_URL: "http://balance-service-api-k8s.payments.svc.cluster.local"
  JOB_INTERVAL: "12h"
  COUNTRY_CODES: "AO,AT,AU,BE,CA,CF,CG,CH,CK,DE,DK,ER,ES,FJ,FR,GB,GN,GQ,IE,IT,KN,LU,MR,NL,NO,NZ,SE,TK,TV,TZ,US,YE"
  CPS_BASE_URL: "http://customer-plans-service-api-k8s.consumer-core.svc.cluster.local/"
  DB_HOST: "credit-autocancel-service-db000.live.hellofresh.io"
  DB_PORT: "5432"
  DB_USER: "creditautocancelservice"
  DB_PASSWORD: "${vault:live/key-value/data/db#password}"
  DB_NAME: creditautocancelservicedb
  CLIENT_SECRET: "${vault:live/key-value/data/auth_service#client_secret}"
  CLIENT_ID: "${vault:live/key-value/data/auth_service#client_id}"
  AMQP_DSN: "${vault:live/key-value/data/amqp#dsn}"
  AMQP_PASSWORD: "${vault:live/key-value/data/amqp#password}"
  # Kafka
  KAFKA_DSN: "kafka-live-hellofresh-live.aivencloud.com:23419"
  BENTHOS_KAFKA_CONSUMER_GROUP_INGEST: "credit-autocancel-service-benthos-ingest"
  BENTHOS_KAFKA_CONSUMER_GROUP_ORDER_PAID: "credit-autocancel-service-benthos-order-paid"
  BENTHOS_KAFKA_CONSUMER_GROUP_BENEFIT_ATTACHED: "credit-autocancel-service-benthos-benefit-attached"
  BENTHOS_KAFKA_START_FROM_OLDEST: "true"
  KAFKA_CA_CERT: "${vault:live/key-value/data/kafka/credit-autocancel-service#ca}"
  KAFKA_USERNAME: "${vault:live/key-value/data/kafka/credit-autocancel-service#username}"
  KAFKA_PASSWORD: "${vault:live/key-value/data/kafka/credit-autocancel-service#password}"

prometheusRules:
  - name: credit-autocancel-service.rules
    rules:
      - alert: CACSIsDown
        annotations:
          description: "Service Down"
          summary: Service is down
        expr: absent(up{job="credit-autocancel-service-k8s"} == 1)
        for: 5m
        labels:
          slack: kube-alerts-live
          severity: critical

provisionDashboards:
  enabled: true
  dashboardLabel: grafana_dashboard

istio-destination-rule: &istio-destination-rule
  enabled: true
  connectionPool:
    http:
      http1MaxPendingRequests: 1024
      maxRequestsPerConnection: 1024
    tcp:
      maxConnections: 1024
  outlierDetection:
    consecutive5xxErrors: 5
    interval: 10s
    baseEjectionTime: 30s
    maxEjectionPercent: 20

istio:
  api:
    enabled: true
    gateway:
      enabled: true
      hosts:
        - credit-autocancel-service.live-k8s.hellofresh.io
    virtualService:
      enabled: true
      timeout: 10s
    destinationRule:
      <<: *istio-destination-rule
    sidecar:
      egress:
        hosts:
          - price-service-k8s.consumer-core.svc.cluster.local
          - auth-service-api-k8s.consumer-core.svc.cluster.local
          - voucher-service-k8s.conversions.svc.cluster.local
          - customer-plans-service-k8s.consumer-core.svc.cluster.local
          - "*/rabbitmq.live.hellofresh.io"

  benthos:
    enabled: true
    gateway:
      enabled: false
      hosts:
        - credit-autocancel-service-benthos.live-k8s.hellofresh.io
    virtualService:
      enabled: false
    destinationRule:
      <<: *istio-destination-rule
    sidecar:
      egress:
        hosts:
          - voucher-service-k8s.conversions.svc.cluster.local
          - "*/rabbitmq.live.hellofresh.io"
          - "*/kafka-live-hellofresh-live.aivencloud.com"

  job:
    enabled: true
    gateway:
      enabled: false
      hosts:
        - credit-autocancel-service-job.live-k8s.hellofresh.io
    virtualService:
      enabled: false
    destinationRule:
      <<: *istio-destination-rule
    sidecar:
      egress:
        hosts:
          - customer-plans-service-k8s.consumer-core.svc.cluster.local
          - "*/rabbitmq.live.hellofresh.io"

outbox-worker-amqp:
  enabled: true
  vaultNamespace: *vaultNamespace
  env:
    DB_PASSWORD: "${vault:live/key-value/data/db#password}"
    AMQP_PASSWORD: "${vault:live/key-value/data/amqp#password}"
  config:
    AMQP_CONNECTION_NAME: "credit-autocancel-service"
    AMQP_DSN: amqp://credit-autocancel-service:@rabbitmq.live.hellofresh.io:5672/
    DB_DSN: postgres://creditautocancelservice:@credit-autocancel-service-db000.live.hellofresh.io/creditautocancelservicedb?sslmode=disable
    TRIGGER_INTERVAL: 10ms
  environment: *environment
  tribe: *tribe
  squad: *squad

hf-prometheus-rds-exporter:
  enabled: true
  environment: *environment
  tribe: *tribe
  squad: *squad

  alerts_config:
    slack: *slack
    severity: P4

  config:
    - instance: credit-autocancel-service-db000-live
      alerts:
        RDSDiskWillFillInXHours: {}
        RDSDiskIsXPercentFull: {}
        RDSCPUUsageHigh: {}
        RDSConnectionNumberHigh: {}
        RDSMemoryUsageHigh: {}
        RDSReadLatencyIncrease: {}
        RDSBurstBalanceLow: {}
        RDSCreditWillDepleteInXHours: {}

alerts:
  systemRules:
    - name: credit-autocancel-service-api.rules
      rules:
        - alert: AbnormalAPIMemoryUsage
          annotations:
            description: "Credit AutoCancel Abnormal Memory Usage"
            summary: |
              The Credit AutoCancel api may have a memory leak. Their combined average
              consumption is greater than the historical average recorded in Grafana.
              Historically we have noticed spikes of more than 250Mib per pod for a
              period of 6 hours and then the pods stabilize. Please investigate.
              **Dashboard:** https://grafana.live-k8s.hellofresh.io/d/credit-autocancel-service/credit-auto-cancel?orgId=1
          expr: >
            ((sum(container_memory_usage_bytes{container=~"credit-autocancel-service-.+"}) / count(container_memory_usage_bytes{container=~"credit-autocancel-service-.+"}) ) / (1024 * 1024)) >= 250
          for: 6h
          labels:
            severity: P4
            slack: *slack
            service: *service
            tribe: *tribe

        - alert: CreditAutoCancelServiceKafkaTooManyPendingMessages
          annotations:
            summary: "Credit AutoCancel Service: too many pending Kafka messages"
            description: |
              Credit AutoCancel Service deployment `{{ $labels.name }}` is lagging behind on reading topic `{{ $labels.topic }}`.
              The service could be too slow at reading messages or stuck.
            runbook_url: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/rte-expansion/rte-systems/credit-autocancel-service/kakfa-too-many-pending-messages.md
          expr: >
            sum(kafka_consumer_group_rep_lag{name=~"credit-autocancel-service-benthos.*",name!~".*development.*",name!="credit-autocancel-service-benthos-ingest-v2"}) by (name,topic) > 50000
          for: 12h
          labels:
            severity: P4
            service: *service
            slack: *slack

        - alert: CreditAutoCancelServiceNoSubscriptionsCancelled
          annotations:
            summary: "Credit AutoCancel Service: no subscription cancellations"
            description: |
              Credit AutoCancel Service has not cancelled any subscriptions from the previous job run.
              Grafana dashboard: https://prdhellofresh.grafana.net/d/FnUgF1NyV7NckKTUQ6eARYQgfZ6ySLYGkwR7NgHM/credit-autocancel-service?orgId=1&from=now-24h&to=now&viewPanel=46
            runbook_url: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/rte-expansion/rte-systems/credit-autocancel-service/no-subscriptions-cancelled.md
          expr: >
            sum(credit_autocancel_service_subscription_cancelled_total_count{result=~"ok"}) == 0
          for: 12h
          labels:
            severity: P4
            service: *service
            slack: *slack

  additionalRules:
    - name: credit-autocancel-service-api.rules
      rules:
        - alert: CreditAutoCancelServiceAPIIsDown
          annotations:
            summary: 'Instance {{ $labels.pod }} is down'
            runbook_url: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/rte-expansion/rte-systems/credit-autocancel-service/api-down.md
            description: 'Less than half of pods up for more than 1 minute'
          expr: |
            avg(avg_over_time(up{pod=~"credit-autocancel-service-api.*"}[5m])) < 0.5
          for: 1m
          labels:
            severity: P4
            slack: *slack
            service: *service
            tribe: *tribe

        - alert: CreditAutoCancelServiceJobIsDown
          annotations:
            summary: 'Instance {{ $labels.pod }} is down'
            runbook_url: https://github.com/hellofresh/runbooks/blob/master/troubleshooting/rte-expansion/rte-systems/credit-autocancel-service/job-down.md
            description: 'Less than half of pods are up'
          expr: |
            avg(avg_over_time(up{pod=~"credit-autocancel-service-job.*"}[5m])) < 0.5
          for: 10m
          labels:
            severity: P4
            slack: *slack
            service: *service
            tribe: *tribe
