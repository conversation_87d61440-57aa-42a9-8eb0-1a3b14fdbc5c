environment: &environment ahoy
tribe: &tribe rte-expansion
squad: &squad rte-platform
vaultNamespace: &vaultNamespace services/credit-autocancel-service

jobs:
  migrate:
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-api
    pullPolicy: Always
    restartPolicy: OnFailure
    command: ["/bin/sh"]
    args:
      - "-c"
      - |
        /migrate -database="$DATABASE_DSN" -path=migrations up
    env:
      DATABASE_DSN: "postgres://creditautocancelservice:${vault:staging/key-value/data/db#password}@credit-autocancel-service-db000.staging.hellofresh.io:5432/creditautocancelservicedb?sslmode=disable"
    annotations:
      "helm.sh/hook": post-install, post-upgrade

deployment-config: &deployment-config
  replicaCount: 1
  containerPorts:
    http: 9090
  pullPolicy: Always
  podAnnotations:
    sidecar.istio.io/inject: "true"
  resources: {}
  nodeSelector: {}
  tolerations: []
  affinity: {}
  hpa:
    enabled: false
  securityContext:
    allowPrivilegeEscalation: false
    readOnlyRootFilesystem: false
    runAsNonRoot: false

deployments:
  api:
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-api
    <<: *deployment-config
    livenessProbe:
      httpGet:
        path: /alive
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /health
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3

  job:
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-job
    <<: *deployment-config
    livenessProbe:
      httpGet:
        path: /alive
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /health
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3

  benthos:
    repository: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/credit-autocancel-service-benthos
    <<: *deployment-config
    deploymentStrategy:
      type: Recreate
    livenessProbe:
      httpGet:
        path: /alive
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    readinessProbe:
      httpGet:
        path: /health
        port: http
      initialDelaySeconds: 15
      periodSeconds: 10
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3

services:
  api:
    enabled: true
    enablePrometheus: true
    type: ClusterIP
    ports:
      http: 80
  job:
    enabled: true
    enablePrometheus: true
    type: ClusterIP
    ports:
      http: 80
  benthos:
    enabled: true
    enablePrometheus: true
    type: ClusterIP
    ports:
      http: 80

ingresses:
  api:
    enabled: true
    annotations:
      disable-external-dns: "true"
      kubernetes.io/ingress.class: "nginx"
    path: /
    hosts:
      http: "credit-autocancel-service-{{ .Values.entryDnsName }}.{{ .Values.TLD }}"
  job:
    enabled: false
  benthos:
    enabled: false

configMap:
  PORT: "9090"
  APP_ENV: "development"
  AUTH_URL: "http://auth-service-k8s.consumer-core.svc.cluster.local"
  PRICE_SERVICE_URL: "http://price-service-k8s.consumer-core.svc.cluster.local"
  VOUCHER_SERVICE_URL: "http://voucher-service.staging-k8s.hellofresh.io"
  BALANCE_SERVICE_URL: "http://balance-service.staging-k8s.hellofresh.io"
  JOB_INTERVAL: "5m"
  COUNTRY_CODES: "ao,at,au,be,ca,cg,ch,ck,de,dk,er,es,fj,fr,gb,gn,gq,ie,lu,mr,nl,nz,se,us"
  CPS_BASE_URL: "http://customer-plans-service.staging-k8s.hellofresh.io"
  DB_HOST: "credit-autocancel-service-db000.staging.hellofresh.io"
  DB_PORT: "5432"
  DB_USER: "creditautocancelservice"
  DB_PASSWORD: "${vault:ahoy/key-value/data/db#password}"
  DB_NAME: creditautocancelservicedb
  CLIENT_SECRET: "${vault:ahoy/key-value/data/auth_service#client_secret}"
  CLIENT_ID: "${vault:ahoy/key-value/data/auth_service#client_id}"
  AMQP_DSN: "${vault:ahoy/key-value/data/amqp#dsn}"
  AMQP_PASSWORD: "${vault:ahoy/key-value/data/amqp#password}"
  # Kakfa
  KAFKA_DSN: "kafka-staging-hellofresh.aivencloud.com:23419"
  KAFKA_CA_CERT: "${vault:ahoy/key-value/data/kafka/credit-autocancel-service#ca}"
  KAFKA_USERNAME: "${vault:ahoy/key-value/data/kafka/credit-autocancel-service#username}"
  KAFKA_PASSWORD: "${vault:ahoy/key-value/data/kafka/credit-autocancel-service#password}"

provisionDashboards:
  enabled: false
  dashboardLabel: grafana_dashboard

istio-destination-rule: &istio-destination-rule
  enabled: true
  connectionPool:
    http:
      http1MaxPendingRequests: 1024
      maxRequestsPerConnection: 1024
    tcp:
      maxConnections: 1024
  outlierDetection:
    consecutive5xxErrors: 5
    interval: 10s
    baseEjectionTime: 30s
    maxEjectionPercent: 20

istio:
  api:
    enabled: true
    gateway:
      enabled: true
      hosts:
        - credit-autocancel-service.ahoy-k8s.hellofresh.io
    virtualService:
      enabled: true
      timeout: 10s
    destinationRule:
      <<: *istio-destination-rule

  benthos:
    enabled: true
    gateway:
      enabled: false
    virtualService:
      enabled: false
    destinationRule:
      <<: *istio-destination-rule

  job:
    enabled: true
    gateway:
      enabled: false
    virtualService:
      enabled: false
    destinationRule:
      <<: *istio-destination-rule

hf-prometheus-rds-exporter:
  enabled: false

outbox-worker-amqp:
  enabled: true
  fullnameOverride: "outbox-amqp-pbs"
  vaultNamespace: *vaultNamespace
  env:
    DB_PASSWORD: "${vault:ahoy/key-value/data/db#password}"
    AMQP_PASSWORD: "${vault:ahoy/key-value/data/amqp#password}"
  config:
    AMQP_CONNECTION_NAME: "credit-autocancel-service"
    AMQP_DSN: amqp://credit-autocancel-service:@rabbitmq.staging.hellofresh.io:5672/
    DB_DSN: postgres://creditautocancelservice:@credit-autocancel-service-db000.staging.hellofresh.io/creditautocancelservicedb?sslmode=disable
    TRIGGER_INTERVAL: 10ms
  environment: *environment
  tribe: *tribe
  squad: *squad
