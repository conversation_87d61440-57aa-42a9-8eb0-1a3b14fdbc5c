{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "graphTooltip": 0, "links": [], "panels": [{"id": 1, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "type": "row", "title": "<PERSON><PERSON><PERSON>", "fieldConfig": {"defaults": {"color": null, "custom": null, "mappings": null, "thresholds": null, "unit": ""}, "overrides": null}}, {"id": 2, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "type": "timeseries", "title": "Input Connection Status", "description": "Connection status for input components (1=up, 0=down). Monitors connectivity to upstream sources.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  benthos_input_connection_up{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 3, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "type": "timeseries", "title": "Output Connection Status", "description": "Connection status for output components (1=up, 0=down). Monitors connectivity to downstream systems.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  benthos_output_connection_up{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 4, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "type": "timeseries", "title": "Output Connection Failures", "description": "Rate of output connection failures. Indicates problems establishing connections to downstream systems.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  rate(benthos_output_connection_failed{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 5, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "type": "timeseries", "title": "Output Connection Lost", "description": "Rate of established connections that were lost. Shows connection stability issues with downstream systems.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  rate(benthos_output_connection_lost{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 6, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "type": "timeseries", "title": "Input Message Rate", "description": "Rate of individual messages received by Benthos inputs per second. Shows incoming data volume.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, path, label) (\n  rate(benthos_input_received{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{path}} - {{label}}", "range": true, "refId": "A"}]}, {"id": 7, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "type": "timeseries", "title": "Output Message Rate", "description": "Rate of individual messages sent by Benthos outputs per second. Shows outgoing data volume.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  rate(benthos_output_sent{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 8, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "type": "timeseries", "title": "Output Errors", "description": "Total count of errors in Benthos output components. Shows message delivery failures to destinations.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  benthos_output_error{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 9, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 25}, "type": "timeseries", "title": "Input Processing Latency (p99)", "description": "99th percentile of input message processing time in milliseconds. Shows input performance bottlenecks.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.99,\n  sum by (le, stream, path) (\n    rate(benthos_input_latency_ns_bucket{\n      job=\"credit-autocancel-service-benthos-k8s\",\n      stream=~\"${benthos_stream}\",\n      label=~\"${benthos_label}\",\n      path=~\"${benthos_path}\",\n    }[2m])\n  )\n)\n", "legendFormat": "{{stream}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 10, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "type": "timeseries", "title": "Output Processing Latency (p99)", "description": "99th percentile of output message processing time in milliseconds. Shows output performance bottlenecks.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.99,\n  sum by (le, stream, label, path) (\n    rate(benthos_output_latency_ns_bucket{\n      job=\"credit-autocancel-service-benthos-k8s\",\n      stream=~\"${benthos_stream}\",\n      label=~\"${benthos_label}\",\n      path=~\"${benthos_path}\",\n    }[2m])\n  )\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 11, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "type": "timeseries", "title": "Processor Latency (p99)", "description": "99th percentile of message processing time in milliseconds by processors. Shows processing bottlenecks.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.99,\n  sum by (le, stream, label, path) (\n    rate(benthos_processor_latency_ns_bucket{\n      job=\"credit-autocancel-service-benthos-k8s\",\n      stream=~\"${benthos_stream}\",\n      label=~\"${benthos_label}\",\n      path=~\"${benthos_path}\",\n    }[2m])\n  )\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 12, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 41}, "type": "timeseries", "title": "Input Operation Rate", "description": "Rate of input operations per second. Differs from message rate for batched operations.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, path) (\n  rate(benthos_input_latency_ns_count{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 13, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 41}, "type": "timeseries", "title": "Output Operation Rate", "description": "Rate of output operations per second. Differs from message rate for batched operations.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  rate(benthos_output_latency_ns_count{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 14, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 49}, "type": "timeseries", "title": "<PERSON><PERSON>", "description": "Rate of cache operation errors by type of operation (add, get, set, delete). Shows issues with cache interactions.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path, operation) (\n  rate(benthos_cache_error{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}} - {{operation}}", "range": true, "refId": "A"}]}, {"id": 15, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 49}, "type": "timeseries", "title": "<PERSON><PERSON> Duplicate Rate", "description": "Rate of duplicate key insertions in cache. Shows attempts to add keys that already exist.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path, operation) (\n  rate(benthos_cache_duplicate{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n    operation=\"add\"\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}} - {{operation}}", "range": true, "refId": "A"}]}, {"id": 16, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 57}, "type": "timeseries", "title": "Processor <PERSON> Received Rate", "description": "Rate of batches received by processors per second. Shows batch processing load.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  rate(benthos_processor_batch_received{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 17, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 57}, "type": "timeseries", "title": "Processor <PERSON>ch <PERSON>", "description": "Rate of batches sent by processors per second. Shows batch output throughput.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  rate(benthos_processor_batch_sent{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 18, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 65}, "type": "timeseries", "title": "Processor Message Received Rate", "description": "Rate of individual messages received by processors per second. Shows message processing volume.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  rate(benthos_processor_received{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 19, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 65}, "type": "timeseries", "title": "Processor Message Sent Rate", "description": "Rate of individual messages sent by processors per second. Shows message output volume.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  rate(benthos_processor_sent{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }[2m])\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 20, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 73}, "type": "timeseries", "title": "Processor <PERSON>", "description": "Total count of errors in Benthos processors. Shows processing failure points.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (stream, label, path) (\n  benthos_processor_error{\n    job=\"credit-autocancel-service-benthos-k8s\",\n    stream=~\"${benthos_stream}\",\n    label=~\"${benthos_label}\",\n    path=~\"${benthos_path}\",\n  }\n)\n", "legendFormat": "{{stream}} - {{label}} - {{path}}", "range": true, "refId": "A"}]}, {"id": 21, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 73}, "type": "timeseries", "title": "AutoCancel Consumer Results", "description": "Rate at which subscriptions are being set for cancellation, grouped by result status.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (result) (\n  rate(benthos_credit_autocancel_service_subscription_set_for_cancellation_count{}[2m])\n)\n", "legendFormat": "{{result}}", "range": true, "refId": "A"}]}, {"id": 22, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 81}, "type": "row", "title": "Istio - Inbound", "fieldConfig": {"defaults": {"color": null, "custom": null, "mappings": null, "thresholds": null, "unit": ""}, "overrides": null}}, {"id": 23, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 82}, "type": "timeseries", "title": "Inbound Request Rate by Source", "description": "Rate of incoming requests per second to credit-autocancel-service components from other services by source workload.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (source_workload) (\n  rate(istio_requests_total{\n    job=\"monitoring/hf-istio\",\n    reporter=\"destination\", \n    destination_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    destination_workload_namespace=\"conversions\"\n  }[2m])\n)\n", "legendFormat": "{{source_workload}}", "range": true, "refId": "A"}]}, {"id": 24, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 82}, "type": "timeseries", "title": "Inbound Error Rate by Source (Destination Reporter)", "description": "Rate of error responses (4xx and 5xx) to incoming requests by source, as reported by the destination (credit-autocancel-service).", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (source_workload, response_code) (\n  rate(istio_requests_total{\n    job=\"monitoring/hf-istio\",\n    reporter=\"destination\", \n    destination_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    destination_workload_namespace=\"conversions\", \n    response_code!~\"2..|3..\"\n  }[2m])\n)\n", "legendFormat": "{{source_workload}} - {{response_code}}", "range": true, "refId": "A"}]}, {"id": 25, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 90}, "type": "timeseries", "title": "Inbound Error Rate by Source (Source Reporter)", "description": "Rate of error responses (4xx and 5xx) to incoming requests by source, as reported by the source services making the requests.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (source_workload, response_code) (\n  rate(istio_requests_total{\n    job=\"monitoring/hf-istio\",\n    reporter=\"source\", \n    destination_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    destination_workload_namespace=\"conversions\", \n    response_code!~\"2..|3..\"\n  }[2m])\n)\n", "legendFormat": "{{source_workload}} - {{response_code}}", "range": true, "refId": "A"}]}, {"id": 26, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 90}, "type": "timeseries", "title": "Inbound Error Percentage", "description": "Percentage of incoming requests resulting in errors (4xx/5xx responses).", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(rate(istio_requests_total{\n  job=\"monitoring/hf-istio\",\n  reporter=\"destination\", \n  destination_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n  destination_workload_namespace=\"conversions\", \n  response_code!~\"2..|3..\"\n}[2m])) / sum(rate(istio_requests_total{\n  job=\"monitoring/hf-istio\",\n  reporter=\"destination\", \n  destination_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n  destination_workload_namespace=\"conversions\"\n}[2m]))\n", "legendFormat": "Error percentage", "range": true, "refId": "A"}]}, {"id": 27, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 98}, "type": "timeseries", "title": "Request Failures by Response Flags", "description": "Rate of requests with specific error indicators in response flags.  Common flags:  - 'UH' (upstream host not found) - 'UO' (upstream overflow) - 'NR' (no route) - 'RL' (rate limited) - 'DC' (downstream connection termination) - 'UC' (upstream connection termination)\n", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (response_flags) (\n  rate(istio_requests_total{\n    job=\"monitoring/hf-istio\",\n    reporter=\"destination\", \n    destination_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    destination_workload_namespace=\"conversions\", \n    response_flags!=\"-\"\n  }[2m])\n)\n", "legendFormat": "{{response_flags}}", "range": true, "refId": "A"}]}, {"id": 28, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 98}, "type": "timeseries", "title": "Inbound Request Duration by Source", "description": "99th percentile of request processing time in milliseconds grouped by source workload.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.99, sum by (source_workload, le) (\n  rate(istio_request_duration_milliseconds_bucket{\n    job=\"monitoring/hf-istio\",\n    reporter=\"destination\", \n    destination_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    destination_workload_namespace=\"conversions\"\n  }[2m])\n))\n", "legendFormat": "{{source_workload}}", "range": true, "refId": "A"}]}, {"id": 29, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 106}, "type": "timeseries", "title": "Inbound Request Payload Size by Source", "description": "99th percentile of incoming request payload size in bytes by source workload.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.99, sum by (source_workload, le) (\n  rate(istio_request_bytes_bucket{\n    job=\"monitoring/hf-istio\",\n    reporter=\"destination\",\n    destination_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    destination_workload_namespace=\"conversions\"\n  }[2m])\n))\n", "legendFormat": "{{source_workload}}", "range": true, "refId": "A"}]}, {"id": 30, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 114}, "type": "row", "title": "Istio - Outbound", "fieldConfig": {"defaults": {"color": null, "custom": null, "mappings": null, "thresholds": null, "unit": ""}, "overrides": null}}, {"id": 31, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 115}, "type": "timeseries", "title": "Outbound Request Rate by Destination", "description": "Rate of outgoing requests per second from credit-autocancel-service to other services.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (source_workload, destination_workload) (\n  rate(istio_requests_total{\n    job=\"monitoring/hf-istio\",\n    reporter=\"source\", \n    source_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    source_workload_namespace=\"conversions\"\n  }[2m])\n)\n", "legendFormat": "{{source_workload}} - {{destination_workload}}", "range": true, "refId": "A"}]}, {"id": 32, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 115}, "type": "timeseries", "title": "Outbound Error Rate by Destination", "description": "Rate of error responses (4xx and 5xx) from downstream services.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (source_workload, destination_workload, response_code) (\n  rate(istio_requests_total{\n    job=\"monitoring/hf-istio\",\n    reporter=\"source\", \n    source_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    source_workload_namespace=\"conversions\", \n    response_code!~\"2..|3..\"\n  }[2m])\n)\n", "legendFormat": "{{source_workload}} - {{destination_workload}} - {{response_code}}", "range": true, "refId": "A"}]}, {"id": 33, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 123}, "type": "timeseries", "title": "Outbound Error Percentage by Destination", "description": "Percentage of outgoing requests resulting in errors (4xx/5xx) by destination.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (source_workload, destination_workload) (\n  rate(istio_requests_total{\n    job=\"monitoring/hf-istio\",\n    reporter=\"source\", \n    source_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    source_workload_namespace=\"conversions\", \n    response_code!~\"2..|3..\"\n  }[2m])\n) / sum by (source_workload, destination_workload) (\n  rate(istio_requests_total{\n    job=\"monitoring/hf-istio\",\n    reporter=\"source\", \n    source_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    source_workload_namespace=\"conversions\"\n  }[2m])\n)\n", "legendFormat": "{{source_workload}} - {{destination_workload}}", "range": true, "refId": "A"}]}, {"id": 34, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 123}, "type": "timeseries", "title": "Outbound Request Duration by Destination", "description": "99th percentile of request processing time for outgoing requests by destination.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.99, sum by (source_workload, destination_workload, le) (\n  rate(istio_request_duration_milliseconds_bucket{\n    job=\"monitoring/hf-istio\",\n    reporter=\"source\", \n    source_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    source_workload_namespace=\"conversions\"\n  }[2m])\n))\n", "legendFormat": "{{source_workload}} - {{destination_workload}}", "range": true, "refId": "A"}]}, {"id": 35, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 131}, "type": "timeseries", "title": "Outbound Request Payload Size by Destination", "description": "99th percentile of outgoing request payload size in bytes by destination.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.99, sum by (source_workload, destination_workload, le) (\n  rate(istio_request_bytes_bucket{\n    job=\"monitoring/hf-istio\",\n    reporter=\"source\",\n    source_workload=~\"credit-autocancel-service-(api|benthos|job)\", \n    source_workload_namespace=\"conversions\"\n  }[2m])\n))\n", "legendFormat": "{{source_workload}} - {{destination_workload}}", "range": true, "refId": "A"}]}, {"id": 36, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 139}, "type": "row", "title": "Kafka", "fieldConfig": {"defaults": {"color": null, "custom": null, "mappings": null, "thresholds": null, "unit": ""}, "overrides": null}}, {"id": 37, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 140}, "type": "timeseries", "title": "Topic Message Production Rate", "description": "Rate of messages produced to credit-autocancel-service related Kafka topics. Shows message volume patterns.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (topic) (\n  rate(kafka_server_BrokerTopicMetrics_MessagesInPerSec_Count{\n    job=\"aiven-kafka\",\n    service=\"$kafka_service\",\n    service_type=\"kafka\",\n    topic=~\"${topics}\"\n  }[2m])\n)\n", "legendFormat": "{{topic}}", "range": true, "refId": "A"}]}, {"id": 38, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 140}, "type": "timeseries", "title": "Kafka Consumer Lag", "description": "Number of messages waiting to be processed by our service's Benthos consumers. Shows backlog by topic.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (topic, group) (\n  kafka_consumergroup_group_lag{\n    job=\"kafka-lag-exporter\",\n    cluster_name=\"live\",\n    group=~\"credit-autocancel-service-.*\",\n    group!=\"credit-autocancel-service-benthos-ingest-v2\",\n    topic=~\"${topics}\"\n  }\n)\n", "legendFormat": "{{topic}} - {{group}}", "range": true, "refId": "A"}]}, {"id": 39, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 148}, "type": "row", "title": "Credit Auto-Cancel Service - HTTP", "fieldConfig": {"defaults": {"color": null, "custom": null, "mappings": null, "thresholds": null, "unit": ""}, "overrides": null}}, {"id": 40, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 149}, "type": "timeseries", "title": "HTTP Client Error Responses", "description": "Count of non-success (non-200) HTTP responses received by our service, grouped by status, method, and host.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (http_client_status, http_client_method, http_client_host) (\n  increase(kit_http_client_completed_count{\n    job=~\"credit-autocancel-service.*\", \n    http_client_status!~\"2..\"\n  }[5m])\n)\n", "legendFormat": "{{http_client_status}} - {{http_client_method}} - {{http_client_host}}", "range": true, "refId": "A"}]}, {"id": 41, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 157}, "type": "row", "title": "Credit Auto-Cancel Service - SQL", "fieldConfig": {"defaults": {"color": null, "custom": null, "mappings": null, "thresholds": null, "unit": ""}, "overrides": null}}, {"id": 42, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 158}, "type": "timeseries", "title": "Database Query Rate", "description": "Rate of database queries per second by operation. Shows database access patterns and usage.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (db_operation) (\n  rate(db_sql_client_calls{\n    job=~\"credit-autocancel-service-(api|benthos|job)-k8s\"\n  }[2m])\n)\n", "legendFormat": "{{db_operation}}", "range": true, "refId": "A"}]}, {"id": 43, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 158}, "type": "timeseries", "title": "Database Query Error Rate", "description": "Rate of database query errors per second. Identifies database connectivity and query issues.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (db_operation, db_sql_status) (\n  rate(db_sql_client_calls{\n    job=~\"credit-autocancel-service-(api|benthos|job)-k8s\",\n    db_sql_status!=\"OK\"\n  }[2m])\n)\n", "legendFormat": "{{db_operation}} - {{db_sql_status}}", "range": true, "refId": "A"}]}, {"id": 44, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 166}, "type": "timeseries", "title": "Database Query Latency", "description": "99th percentile of database query latency by operation. Shows database operation performance.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "histogram_quantile(0.99, sum by (db_operation, le) (\n  rate(db_sql_client_latency_bucket{\n    job=~\"credit-autocancel-service-(api|benthos|job)-k8s\"\n  }[2m])\n))\n", "legendFormat": "{{db_operation}}", "range": true, "refId": "A"}]}, {"id": 45, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 166}, "type": "timeseries", "title": "Open Database Connections", "description": "Total number of open database connections. Monitors connection usage and potential leaks.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (job) (\n  db_sql_connections_open{\n    job=~\"credit-autocancel-service-(api|benthos|job)-k8s\"\n  }\n)\n", "legendFormat": "{{job}}", "range": true, "refId": "A"}]}, {"id": 46, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 174}, "type": "timeseries", "title": "Database Connection Wait Duration", "description": "Time spent waiting for database connections. Non-zero values indicate connection pool saturation.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (job) (\n  rate(db_sql_connections_wait_duration{\n    job=~\"credit-autocancel-service-(api|benthos|job)-k8s\"\n  }[2m])\n)\n", "legendFormat": "{{job}}", "range": true, "refId": "A"}]}, {"id": 47, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 182}, "type": "row", "title": "Credit Auto-Cancel Service Metrics", "fieldConfig": {"defaults": {"color": null, "custom": null, "mappings": null, "thresholds": null, "unit": ""}, "overrides": null}}, {"id": 48, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 183}, "type": "timeseries", "title": "Published Message Totals", "description": "Count of successfully published messages from our service during the selected interval, by source.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (service, source) (\n  increase(credit_autocancel_service_publisher_message_process_total_count{\n    result=~\"ok\"\n  }[5m])\n) > 0\n", "legendFormat": "{{service}} - {{source}}", "range": true, "refId": "A"}]}, {"id": 49, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 183}, "type": "timeseries", "title": "Subscription Cancellations by Country", "description": "Rate of successful subscription cancellation marking grouped by country.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (country) (\n  rate(benthos_credit_autocancel_service_subscription_set_for_cancellation_count{\n    result=~\"success\"\n  }[2m])\n)\n", "legendFormat": "{{country}}", "range": true, "refId": "A"}]}, {"id": 50, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 191}, "type": "timeseries", "title": "Job Subscription Cancelled Totals", "description": "Count of subscriptions cancelled during the selected interval, grouped by result status.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (result) (\n  credit_autocancel_service_subscription_cancelled_total_count{}\n)\n", "legendFormat": "{{result}}", "range": true, "refId": "A"}]}, {"id": 51, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 199}, "type": "row", "title": "Kubernetes State Metrics", "fieldConfig": {"defaults": {"color": null, "custom": null, "mappings": null, "thresholds": null, "unit": ""}, "overrides": null}}, {"id": 52, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 200}, "type": "timeseries", "title": "Pods by phase and component", "description": "Number of pods per phase (Running, Pending, Failed, etc.) grouped by credit-autocancel-service component.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (phase, component) (\n  label_replace(\n    kube_pod_status_phase{\n      job=\"kube-state-metrics\",\n      namespace=\"conversions\",\n      pod=~\"credit-autocancel-service-(api|benthos|job)-.*\",\n    },\n    \"component\", \"credit-autocancel-service-$1\", \"pod\", \"credit-autocancel-service-(api|benthos|job)-.*\"\n  )\n)\n", "legendFormat": "{{component}} - {{phase}}", "range": true, "refId": "A"}]}, {"id": 53, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 200}, "type": "timeseries", "title": "Pod uptime", "description": "Duration that each pod has been running since its start time. Shows pod stability and restart patterns.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "time() - kube_pod_start_time{\n  job=\"kube-state-metrics\",\n  namespace=\"conversions\",\n  pod=~\"credit-autocancel-service-(api|benthos|job)-.+-.+\",\n}\n", "legendFormat": "{{pod}}", "range": true, "refId": "A"}]}, {"id": 54, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 208}, "type": "timeseries", "title": "Container Restarts", "description": "Number of container restarts in the last 2 minutes. Indicates stability issues.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by(pod) (delta(kube_pod_container_status_restarts_total{\n    job=\"kube-state-metrics\",\n    namespace=\"conversions\",\n    container=~\"credit-autocancel-service-(api|benthos|job)\"\n}[2m]))\n", "legendFormat": "{{pod}}", "range": true, "refId": "A"}]}, {"id": 55, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 208}, "type": "timeseries", "title": "Deployment Replicas", "description": "Number of requested vs available replicas for each component of the service.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "kube_deployment_status_replicas{\n  job=\"kube-state-metrics\",\n  namespace=\"conversions\",\n  deployment=~\"credit-autocancel-service-(api|benthos|job)\"\n}\n", "legendFormat": "{{deployment}}", "range": true, "refId": "A"}]}, {"id": 56, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 216}, "type": "row", "title": "Kubelet Metrics", "fieldConfig": {"defaults": {"color": null, "custom": null, "mappings": null, "thresholds": null, "unit": ""}, "overrides": null}}, {"id": 57, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 217}, "type": "timeseries", "title": "Pod Memory Usage", "description": "Working set memory usage by pod in bytes. Represents actual memory in use by the container.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "container_memory_working_set_bytes{\n    job=\"kubelet\",\n    namespace=\"conversions\",\n    container=~\"credit-autocancel-service-(api|benthos|job)\",\n}\n", "legendFormat": "{{pod}}", "range": true, "refId": "A"}]}, {"id": 58, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 217}, "type": "timeseries", "title": "Pod CPU Usage", "description": "CPU usage by pod as a ratio of CPU cores. 1.0 means using one full CPU core.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "irate(container_cpu_usage_seconds_total{\n    job=\"kubelet\",\n    namespace=\"conversions\",\n    container=~\"credit-autocancel-service-(api|benthos|job)\",\n    cpu=\"total\",\n}[2m])\n", "legendFormat": "{{pod}}", "range": true, "refId": "A"}]}, {"id": 59, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 225}, "type": "timeseries", "title": "Probe Duration", "description": "Average duration of health probe checks in seconds. Indicates service responsiveness.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum(irate(prober_probe_duration_seconds_sum{\n    job=\"kubelet\",\n    container=~\"credit-autocancel-service-(api|benthos|job)\"\n}[2m])) by (pod) / sum(irate(prober_probe_duration_seconds_count{\n    job=\"kubelet\",\n    container=~\"credit-autocancel-service-(api|benthos|job)\"\n}[2m])) by (pod)\n", "legendFormat": "{{pod}}", "range": true, "refId": "A"}]}, {"id": 60, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 225}, "type": "timeseries", "title": "CPU Throttling", "description": "CPU throttling ratio. Shows fraction of CPU periods throttled due to CPU limits.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "container_cpu_cfs_throttled_periods_total{\n    job=\"kubelet\",\n    namespace=\"conversions\",\n    container=~\"credit-autocancel-service-(api|benthos|job)\",\n} / container_cpu_cfs_periods_total\n", "legendFormat": "{{pod}}", "range": true, "refId": "A"}]}, {"id": 61, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 233}, "type": "timeseries", "title": "Network I/O", "description": "Network I/O rates (receive/transmit) for each pod. Shows network traffic patterns.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "Bps"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (pod, interface) (\n  irate(container_network_receive_bytes_total{\n    job=\"kubelet\",\n    namespace=\"conversions\",\n    pod=~\"credit-autocancel-service-(api|benthos|job)-.*\"\n  }[2m])\n) or  sum by (pod, interface) (\n  irate(container_network_transmit_bytes_total{\n    job=\"kubelet\",\n    namespace=\"conversions\",\n    pod=~\"credit-autocancel-service-(api|benthos|job)-.*\"\n  }[2m])\n)\n", "legendFormat": "{{pod}} - {{interface}}", "range": true, "refId": "A"}]}, {"id": 62, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 241}, "type": "row", "title": "RDS Exporter", "fieldConfig": {"defaults": {"color": null, "custom": null, "mappings": null, "thresholds": null, "unit": ""}, "overrides": null}}, {"id": 63, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 242}, "type": "timeseries", "title": "Database Connections", "description": "Number of database connections in use. Monitors connection load and pool utilization.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (instance) (aws_rds_database_connections_average{\n  job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"\n})\n", "legendFormat": "{{instance}}", "range": true, "refId": "A"}]}, {"id": 64, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 242}, "type": "timeseries", "title": "Read IOPS", "description": "Average number of disk read I/O operations per second. Indicates database read load.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (instance) (aws_rds_read_iops_average{\n  job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"\n})\n", "legendFormat": "{{instance}}", "range": true, "refId": "A"}]}, {"id": 65, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 250}, "type": "timeseries", "title": "Write IOPS", "description": "Average number of disk write I/O operations per second. Indicates database write load.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (instance) (aws_rds_write_iops_average{\n  job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"\n})\n", "legendFormat": "{{instance}}", "range": true, "refId": "A"}]}, {"id": 66, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 250}, "type": "timeseries", "title": "Read Latency", "description": "Average time taken per disk read operation. High values may indicate disk performance issues.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (instance) (aws_rds_read_latency_average{\n  job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"\n})\n", "legendFormat": "{{instance}}", "range": true, "refId": "A"}]}, {"id": 67, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 258}, "type": "timeseries", "title": "Write Latency", "description": "Average time taken per disk write operation. High values may indicate disk performance issues.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (instance) (aws_rds_write_latency_average{\n  job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"\n})\n", "legendFormat": "{{instance}}", "range": true, "refId": "A"}]}, {"id": 68, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 258}, "type": "timeseries", "title": "CPU Utilization", "description": "Percentage of CPU utilization on the RDS instance. Shows database server load.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "avg by (instance) (avg_over_time(\n  node_cpu_average{\n    job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\", \n    mode='total', \n  }[2m])\n)\n", "legendFormat": "{{instance}}", "range": true, "refId": "A"}]}, {"id": 69, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 266}, "type": "timeseries", "title": "Memory Utilization", "description": "Percentage of memory used on the RDS instance. Indicates memory pressure affecting performance.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (instance) (\n  (node_memory_MemTotal_bytes{job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"} - \n  node_memory_Cached_bytes{job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"}) / \n  node_memory_MemTotal_bytes{job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"}\n)\n", "legendFormat": "{{instance}}", "range": true, "refId": "A"}]}, {"id": 70, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 266}, "type": "timeseries", "title": "Filesystem Usage", "description": "Percentage of storage space used on RDS instance. Critical for monitoring storage capacity. Note: '/' is OS storage (logs, temp files) while '/rdsdbdata' contains actual database files.", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "1 - (\n  node_filesystem_free_bytes{job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"} / \n  node_filesystem_size_bytes{job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"}\n)\n", "legendFormat": "{{instance}} - {{mountpoint}}", "range": true, "refId": "A"}]}, {"id": 71, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 274}, "type": "timeseries", "title": "Free Storage Space", "description": "Available storage space in bytes. Helps predict when storage expansion is needed. Watch '/' (OS storage, doesn't auto-scale) separately from '/rdsdbdata' (database files, may auto-scale).", "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "targets": [{"editorMode": "code", "expr": "sum by (instance, mountpoint) (\n  node_filesystem_free_bytes{job=\"credit-autocancel-service-hf-prometheus-rds-exporter-k8s\"}\n)\n", "legendFormat": "{{instance}} - {{mountpoint}}", "range": true, "refId": "A"}]}], "schemaVersion": 41, "tags": [], "templating": {"list": [{"current": {"text": "metrics_live", "value": "metrics_live"}, "label": "Metrics", "name": "metrics_ds", "query": "prometheus", "refresh": 1, "regex": "/^metrics_/", "type": "datasource"}, {"current": {"text": "logs_all_internal", "value": "logs_all_internal"}, "label": "Logs", "name": "logs_ds", "query": "loki", "refresh": 1, "regex": "/^logs_/", "type": "datasource"}, {"current": {"text": ".*", "value": ".*"}, "description": "Benthos stream to filter metrics for", "name": "benthos_stream", "options": [{"selected": true, "text": ".*", "value": ".*"}], "query": ".*", "type": "custom"}, {"current": {"text": ".*", "value": ".*"}, "description": "Benth<PERSON> label to filter metrics for", "name": "benthos_label", "options": [{"selected": true, "text": ".*", "value": ".*"}], "query": ".*", "type": "custom"}, {"current": {"text": ".*", "value": ".*"}, "description": "<PERSON><PERSON><PERSON> path to filter metrics for", "name": "benthos_path", "options": [{"selected": true, "text": ".*", "value": ".*"}], "query": ".*", "type": "custom"}, {"current": {"text": "public\\.customer\\.order\\.v1|public\\.customer\\.order\\.delivery\\.v1|public\\.customer\\.order\\.payment\\.v1|credit-autocancel-service\\.benthos\\.trigger-order-paid|public\\.customer\\.v1beta2", "value": "public\\.customer\\.order\\.v1|public\\.customer\\.order\\.delivery\\.v1|public\\.customer\\.order\\.payment\\.v1|credit-autocancel-service\\.benthos\\.trigger-order-paid|public\\.customer\\.v1beta2"}, "description": "Credit Auto-Cancel Service related Kafka topics", "name": "topics", "options": [{"selected": true, "text": "public\\.customer\\.order\\.v1|public\\.customer\\.order\\.delivery\\.v1|public\\.customer\\.order\\.payment\\.v1|credit-autocancel-service\\.benthos\\.trigger-order-paid|public\\.customer\\.v1beta2", "value": "public\\.customer\\.order\\.v1|public\\.customer\\.order\\.delivery\\.v1|public\\.customer\\.order\\.payment\\.v1|credit-autocancel-service\\.benthos\\.trigger-order-paid|public\\.customer\\.v1beta2"}], "query": "public\\.customer\\.order\\.v1|public\\.customer\\.order\\.delivery\\.v1|public\\.customer\\.order\\.payment\\.v1|credit-autocancel-service\\.benthos\\.trigger-order-paid|public\\.customer\\.v1beta2", "type": "custom"}, {"current": {"text": "kafka-live,kafka-staging", "value": "kafka-live,kafka-staging"}, "description": "Kafka service to gather metrics about", "name": "kafka_service", "options": [{"selected": true, "text": "kafka-live,kafka-staging", "value": "kafka-live,kafka-staging"}], "query": "kafka-live,kafka-staging", "type": "custom"}]}, "time": {"from": "now-6h", "to": "now"}, "timezone": "browser", "title": "Credit Auto-Cancel Service", "uid": "f166914a", "version": 1, "weekStart": ""}