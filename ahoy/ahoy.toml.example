# Docs on this config file are available @ https://github.com/hellofresh/ahoy/blob/master/ahoy.sample.toml

[[Charts]]
  Name = "credit-autocancel-service"
  Repo = "hellofresh/credit-autocancel-service"
  Provider = "github"
  [Charts.Git]
    Branch = "master" # TODO: update with branch
  [Charts.ValueOverrides]
    tag = "PR-?" # TODO: update with PR number

[[Charts]]
  Name = "postgresql"
  Repo = "bitnami/postgresql"
  Provider = "helm_registry"
  Version = "12.0.1"
  [Charts.ValueOverrides.image]
    repository = "postgres"
    tag = "12.4"
  [Charts.ValueOverrides]
    fullnameOverride = "postgresql"
    postgresqlDataDir="/tmp/pgdata"
  [Charts.ValueOverrides.securityContext]
    enabled = false
  [Charts.ValueOverrides.persistence]
    enabled = false
    mountPath = "/blackhole"

[[Charts]]
  Name = "rabbitmq"
  Repo = "bitnami/rabbitmq"
  Provider = "helm_registry"
  Version = "12.1.7"
  AppVersion = "3.12.6"
