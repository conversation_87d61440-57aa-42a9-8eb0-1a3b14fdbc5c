// Package integrationtests contains the integration tests
package integrationtests

import (
	"flag"
	"fmt"
	"os"
	"testing"

	"github.com/hellofresh/credit-autocancel-service/integration_tests/containers"
)

var testInstance *containers.DockerTestInstance

func TestMain(m *testing.M) {
	if !flag.Parsed() {
		flag.Parse()
	}

	if testing.Short() {
		fmt.Fprintln(os.Stdout, "received -short flag, skipping integration tests")
		return
	}

	var err error
	testInstance, err = containers.NewDockerTest()
	if err != nil {
		panic(err)
	}

	code := m.Run()

	testInstance.Close()

	os.Exit(code)
}
