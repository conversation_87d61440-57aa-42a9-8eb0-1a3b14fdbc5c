// Package testdata contains stub responses for testing
package testdata

import (
	_ "embed"
	"encoding/json"
	"math"
	"math/rand"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/voucherservice"
	v1 "github.com/hellofresh/schema-registry-go/shared/v1"
	protoCustomerBenefitAttachment "github.com/hellofresh/schema-registry-go/stream/customer/benefit/attachment/v2beta1"
	protoCustomerOrderDelivery "github.com/hellofresh/schema-registry-go/stream/customer/order/delivery/v1"
	protoCustomerOrderPayment "github.com/hellofresh/schema-registry-go/stream/customer/order/payment/v1"
	protoCustomerOrder "github.com/hellofresh/schema-registry-go/stream/customer/order/v1"
	protoCustomer "github.com/hellofresh/schema-registry-go/stream/customer/v1beta2"
	"github.com/stretchr/testify/require"
	"github.com/twmb/franz-go/pkg/kgo"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/proto"
)

var (
	//go:embed wiremock/__files/voucher_service_response.json
	VoucherServiceResponse []byte

	//go:embed wiremock/__files/auth_service_response.json
	AuthServiceResponse []byte

	//go:embed wiremock/__files/price_service_response.json
	PriceServiceResponse []byte

	Voucher *voucherservice.VoucherDetails

	subscriptionID     = randNDigitInt64(6)
	customerID         = randNDigitInt64(6)
	customerUUID       = uuid.New().String()
	planID             = uuid.New().String()
	VoucherCode        = "FBO-CYU8YTFKR"
	invalidVoucherCode = "wrongwrongwrong"
	sku                = "FR-CBT1-4-2-1"
	currency           = "EUR"
	CampaignType       = "CANCEL_ANY"
	price              = &money.Money{
		CurrencyCode: currency,
		Units:        59,
		Nanos:        *********,
	}
	tax = &money.Money{
		CurrencyCode: currency,
		Units:        5,
		Nanos:        *********,
	}

	// CustomerEvent provides a *kgo.Record with topic "public.customer.1beta2" based on fixtures/customer.json
	CustomerEvent *kgo.Record

	// CustomerEventValue provides a *protoCustomer.Customer based on fixtures/customer.json
	CustomerEventValue = &protoCustomer.CustomerValue{
		BusinessDivision: &v1.BusinessDivision{
			RegionCode: strings.ToUpper(businessUnit),
			Brand:      v1.Brand_BRAND_HELLOFRESH,
		},
		LegacyId: customerID,
	}

	// CustomerBenefitAttachmentEvent provides a *kgo.Record with topic "public.customer.benefit.attachment.v2beta1"
	CustomerBenefitAttachmentEvent *kgo.Record

	// CustomerBenefitAttachmentEventValue provides a *protoCustomerBenefitAttachment.BenefitAttachmentValue
	CustomerBenefitAttachmentEventValue = &protoCustomerBenefitAttachment.BenefitAttachmentValue{
		CustomerPlanId: planID,
		CustomerId:     customerUUID,
		ErrorCode:      0,
		Distributable: &protoCustomerBenefitAttachment.Distributable{
			Reference: VoucherCode,
			BusinessDivision: &v1.BusinessDivision{
				RegionCode: strings.ToUpper(businessUnit),
				Brand:      v1.Brand_BRAND_HELLOFRESH,
			},
		},
	}

	// CustomerOrderEvent provides a *kgo.Record with topic "public.customer.order.v1" based on fixtures/order.json
	CustomerOrderEvent *kgo.Record

	// CustomerOrderEventKey provides a *protoCustomerOrder.CustomerOrderKey based on fixtures/order.json
	CustomerOrderEventKey *protoCustomerOrder.CustomerOrderKey

	// CustomerOrderEventValue provides a *protoCustomerOrder.CustomerOrderEvent based on fixtures/order.json
	CustomerOrderEventValue = &protoCustomerOrder.CustomerOrderValue{
		OrderNumber:    *********,
		CustomerId:     customerUUID,
		RegionCode:     businessUnit,
		SubscriptionId: 123456,
		CreateDate: &date.Date{
			Year:  2023,
			Month: 2,
			Day:   12,
		},
		GrandTotal: price,
		ShippingAmount: &money.Money{
			CurrencyCode: currency,
			Units:        9,
			Nanos:        *********,
		},
		TaxAmount: tax,
		Items: []*protoCustomerOrder.CustomerOrderValue_Item{
			{
				BoxId:         "FR706465",
				ProductHandle: sku,
				UnitPrice:     price,
				TaxAmount:     tax,
				PaidPrice: &money.Money{
					CurrencyCode: currency,
					Units:        price.Units + tax.Units,
					Nanos:        price.Nanos + tax.Nanos,
				},
				VoucherCode: VoucherCode,
			},
		},
		PlanId: planID,
	}

	// DeliveryEventValue provides a *protoCustomerOrderDelivery.DeliveryDetailsValue based on wiremock/__files/order-delivery.json
	DeliveryEventValue = &protoCustomerOrderDelivery.DeliveryDetailsValue{
		Name:                 "SpongeBob SquarePants",
		PhoneNumber:          "5555555555",
		DeliveryOptionHandle: "FR-TEST-HANDLE",
		ShippingAddress: &postaladdress.PostalAddress{
			RegionCode:         strings.ToUpper(businessUnit),
			PostalCode:         "60000",
			SortingCode:        "",
			AdministrativeArea: "HI",
			Locality:           "Bikini Bottom",
			AddressLines:       []string{"124 Conch St"},
		},
		State:            protoCustomerOrderDelivery.DeliveryDetailsValue_STATE_PENDING,
		ShippingComments: []string{"Leave at front door"},
	}

	// DeliveryEventKey provides a *protoCustomerOrderDelivery.DeliveryDetailsKey based on wiremock/__files/order-delivery.json
	DeliveryEventKey *protoCustomerOrderDelivery.DeliveryDetailsKey

	// DeliveryEvent provides a *kgo.Record with topic "public.customer.order.delivery.v1" based on wiremock/__files/order-delivery.json
	DeliveryEvent *kgo.Record

	// PaymentEventValue provides a *protoCustomerOrderPayment.PaymentDetailsValue based on wiremock/__files/order-delivery.json
	PaymentEventValue = &protoCustomerOrderPayment.PaymentDetailsValue{
		Type:           protoCustomerOrderPayment.PaymentDetailsValue_METHOD_RECURRING,
		Provider:       protoCustomerOrderPayment.PaymentDetailsValue_PROVIDER_BRAINTREE,
		TokenId:        randNDigitInt64(6),
		State:          protoCustomerOrderPayment.PaymentDetailsValue_STATE_PAID,
		CustomerPlanId: &planID,
	}

	// PaymentEventKey provides a *protoCustomerOrderPayment.PaymentDetailsKey based on wiremock/__files/order-delivery.json
	PaymentEventKey *protoCustomerOrderPayment.PaymentDetailsKey

	// PaymentEvent provides a *kgo.Record with topic "public.customer.order.payment.v1" based on wiremock/__files/order-delivery.json
	PaymentEvent *kgo.Record

	orderUUID uuid.UUID
)

const businessUnit = "fr"

func init() {
	Voucher = voucher()

	CustomerEvent = customerEvent()
	CustomerBenefitAttachmentEvent = customerBenefitAttachmentEvent()

	var err error
	if orderUUID, err = uuid.NewRandom(); err == nil {
		CustomerOrderEventKey = customerOrderEventKey()
		CustomerOrderEvent = customerOrderEvent()

		DeliveryEventKey = deliveryEventKey()
		PaymentEventKey = paymentEventKey()

		DeliveryEvent = deliveryEvent()
		PaymentEvent = paymentEvent()
	}

}

func voucher() *voucherservice.VoucherDetails {
	var msg voucherservice.VoucherDetails
	err := json.Unmarshal(VoucherServiceResponse, &msg)
	if err != nil {
		return nil
	}

	return &msg
}

func customerOrderEventKey() *protoCustomerOrder.CustomerOrderKey {
	return &protoCustomerOrder.CustomerOrderKey{
		RegionCode: businessUnit,
		Id:         orderUUID.String(),
	}
}

func deliveryEventKey() *protoCustomerOrderDelivery.DeliveryDetailsKey {
	return &protoCustomerOrderDelivery.DeliveryDetailsKey{
		RegionCode: businessUnit,
		Id:         orderUUID.String(),
	}
}

func paymentEventKey() *protoCustomerOrderPayment.PaymentDetailsKey {
	return &protoCustomerOrderPayment.PaymentDetailsKey{
		RegionCode: businessUnit,
		Id:         orderUUID.String(),
	}
}

func customerEvent() *kgo.Record {
	value, err := proto.Marshal(CustomerEventValue)
	if err != nil {
		return nil
	}

	return &kgo.Record{
		Topic: "public.customer.v1beta2",
		Key:   []byte(customerUUID),
		Value: value,
	}
}

func customerBenefitAttachmentEvent() *kgo.Record {
	value, err := proto.Marshal(CustomerBenefitAttachmentEventValue)
	if err != nil {
		return nil
	}

	return &kgo.Record{
		Topic: "public.customer.benefit.attachment.v2beta1",
		Key:   []byte(customerUUID),
		Value: value,
	}
}

func customerOrderEvent() *kgo.Record {
	value, err := proto.Marshal(CustomerOrderEventValue)
	if err != nil {
		return nil
	}

	key, err := proto.Marshal(CustomerOrderEventKey)
	if err != nil {
		return nil
	}

	return &kgo.Record{
		Topic: "public.customer.order.v1",
		Value: value,
		Key:   key,
	}
}

func deliveryEvent() *kgo.Record {
	value, err := proto.Marshal(DeliveryEventValue)
	if err != nil {
		return nil
	}

	key, err := proto.Marshal(DeliveryEventKey)
	if err != nil {
		return nil
	}

	return &kgo.Record{
		Topic: "public.customer.order.delivery.v1",
		Value: value,
		Key:   key,
	}
}

func paymentEvent() *kgo.Record {
	value, err := proto.Marshal(PaymentEventValue)
	if err != nil {
		return nil
	}

	key, err := proto.Marshal(PaymentEventKey)
	if err != nil {
		return nil
	}

	return &kgo.Record{
		Topic: "public.customer.order.payment.v1",
		Value: value,
		Key:   key,
	}
}

func randNDigitInt64(digits float64) int64 {
	max := math.Pow(10, digits) - 1
	return rand.Int63n(int64(max)-1) + 1
}

// RequireTestDataReady is a testing helper that will require all test data is not nil
func RequireTestDataReady(t *testing.T) {
	t.Helper()

	require.NotNil(t, CustomerEvent)
	require.NotNil(t, CustomerBenefitAttachmentEvent)

	require.NotNil(t, CustomerOrderEvent)
	require.NotNil(t, CustomerOrderEventKey)
	require.NotNil(t, CustomerOrderEventValue)
}
