package containers

import (
	"github.com/kelseyhightower/envconfig"
)

// Config holds the configuration for integration tests
type Config struct {
	SkipBuild bool `envconfig:"SKIP_BUILD" default:"false"`
}

// getConfig returns config, filled from environment variables
func (dt *DockerTestInstance) getConfig() error {
	var cfg Config

	var err error
	if err = envconfig.Process("", &cfg); err != nil {
		return err
	}

	dt.Config = cfg

	return nil
}
