package containers

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/url"
	"path/filepath"
	"runtime"
	"strings"
	"testing"
	"time"

	"github.com/gofrs/uuid"
	"github.com/golang-migrate/migrate/v4"
	"github.com/hellofresh/credit-autocancel-service/integration_tests/testdata"
	"github.com/hellofresh/credit-autocancel-service/internal/campaign"
	"github.com/hellofresh/credit-autocancel-service/internal/payload"
	"github.com/hellofresh/credit-autocancel-service/internal/subscription"

	// Import the postgres driver
	_ "github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/hellofresh/growth-go-kit/database"
	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/hellofresh/growth-go-kit/reliability"
	"github.com/jackc/pgx/v4"
	"github.com/jmoiron/sqlx"
	"github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
	"github.com/sethvargo/go-retry"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
)

const (
	// databaseName is the name of the template database to clone.
	databaseName = "creditautocanceldb"

	databasePort = "5432"

	// databaseUser and databasePassword are the username and password for
	// connecting to the database. These values are only used for testing.
	databaseUser     = "credit-autocancel-service"
	databasePassword = "freshforyou" //gitleaks:allow
)

type databaseContainer struct {
	pool          *dockertest.Pool
	container     *dockertest.Resource
	network       *dockertest.Network
	ConnectionURL *url.URL

	conn *pgx.Conn
	db   *sqlx.DB
}

// newDatabaseContainer creates a new Docker-based database instance. It also creates
// an initial database, runs the migrations, and sets that database as a
// template to be cloned by future tests.
//
// This should not be used outside of testing, but it is exposed in the package
// so it can be shared with other packages. It should be called and instantiated
// in TestMain.
func (dt *DockerTestInstance) newDatabaseContainer(name string) (*databaseContainer, error) {
	ctx := context.Background()

	// Start the container.
	resource, err := dt.pool.RunWithOptions(&dockertest.RunOptions{
		Name:       name + "_db",
		Repository: "postgres",
		Tag:        "13",
		Env: []string{
			"POSTGRES_DB=" + databaseName,
			"POSTGRES_USER=" + databaseUser,
			"POSTGRES_PASSWORD=" + databasePassword,
		},
		Networks: []*dockertest.Network{dt.network},
		CapAdd:   []string{"NET_ADMIN"},
	}, func(c *docker.HostConfig) {
		c.AutoRemove = true
		c.RestartPolicy = docker.RestartPolicy{Name: "no"}
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start database container: %w", err)
	}

	// Build the connection URL.
	connectionURL := &url.URL{
		Scheme:   "postgres",
		User:     url.UserPassword(databaseUser, databasePassword),
		Host:     getResourceHostPort(resource, databasePort+"/tcp"),
		Path:     databaseName,
		RawQuery: "sslmode=disable",
	}

	var conn *pgx.Conn
	c := &databaseContainer{
		pool:          dt.pool,
		container:     resource,
		network:       dt.network,
		ConnectionURL: connectionURL,

		conn: conn,
	}

	// Stop the container after it's been running for too long since tests should not take super long.
	if err := resource.Expire(uint((5 * time.Minute).Seconds())); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to expire database container: %w", err)
	}

	// Create retryable.
	b := retry.WithMaxRetries(30, retry.NewConstant(1*time.Second))

	// Try to establish a connection to the database, with retries.
	if err := retry.Do(ctx, b, func(ctx context.Context) error {
		var err error
		conn, err = pgx.Connect(ctx, connectionURL.String())
		if err != nil {
			return retry.RetryableError(err)
		}
		if err := conn.Ping(ctx); err != nil {
			return retry.RetryableError(err)
		}
		return nil
	}); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed waiting for database container to be ready: %w", err)
	}

	// Run the migrations.
	if err := dbMigrate(connectionURL.String()); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to migrate database: %w", err)
	}

	c.db, err = observability.NewDBX(
		"postgres",
		database.Config{
			DSN:          connectionURL.String(),
			MaxLifetime:  4 * time.Hour,
			MaxIdleConns: 50,
			MaxOpenConns: 100,
		})
	if err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to init database: %w", err)
	}
	err = c.db.Ping()
	if err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	err = c.createCampaign()
	if err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to create campaign: %w", err)
	}

	log.Debug(
		"database ready",
		zap.String("resource_name", resource.Container.Name[1:]),
		zap.String("url", connectionURL.String()),
	)

	// Return the instance.
	return c, nil
}

// hostInNetwork is the host of the instance within a docker network
func (c *databaseContainer) hostInNetwork() string {
	return c.container.GetIPInNetwork(c.network)
}

// urlInNetwork is the url of the database instance within a docker network
func (c *databaseContainer) urlInNetwork() *url.URL {
	connectionURL := c.ConnectionURL

	return &url.URL{
		Scheme:   connectionURL.Scheme,
		User:     connectionURL.User,
		Host:     c.hostInNetwork() + ":" + databasePort,
		Path:     connectionURL.Path,
		RawQuery: connectionURL.RawQuery,
	}
}

type dbEventRow struct {
	Raw       []byte    `db:"raw"`
	CreatedAt time.Time `json:"-" db:"created_at"`
}

var (
	protoTypeToDBTable = map[string]string{
		"CustomerEvent":          "events_customer_v1beta2",
		"CustomerOrderValue":     "events_customer_order_v1",
		"DeliveryDetailsValue":   "events_customer_order_delivery_v1",
		"PaymentDetailsValue":    "events_customer_order_payment_v1",
		"BenefitAttachmentValue": "events_benefit_attachment_v2beta1",
	}
	retrier = reliability.Retrier{
		AttemptCount: 50,
		InitialWait:  1 * time.Second,
		MaxWait:      5 * time.Second,
	}
)

// createCampaign creates a campaign for the voucher in testdata.VoucherServiceResponse
func (c *databaseContainer) createCampaign() error {
	return campaign.NewRepo(c.db).CreateCampaigns(context.Background(), payload.CampaignsPayload{
		CampaignIDs: []uuid.UUID{
			uuid.FromStringOrNil(testdata.Voucher.Campaign.CampaignID),
		},
		Active:       true,
		Country:      strings.ToUpper(testdata.CustomerOrderEventValue.RegionCode),
		CampaignType: testdata.CampaignType,
	})
}

// VerifyDBAutoCancelBenefitAttached verifies that the running autocancel in database matches expected *subscription.AutoCancel
func (c *databaseContainer) VerifyDBAutoCancelBenefitAttached(t *testing.T, want *subscription.AutoCancel) {
	t.Helper()

	var got *subscription.AutoCancel
	fn := func(ctx context.Context) error {
		var err error
		got, err = subscription.NewRepo(c.db, nil).FindRunning(ctx, want.CustomerPlanID)
		if errors.Is(err, subscription.ErrRunningSubscriptionNotFound) {
			log.Error("db autocancel record not yet available", zap.Error(err))
			return err
		}
		if err != nil {
			log.Error("failed to find autocancel record", zap.Error(err))
		}
		return nil
	}

	err := retrier.Do(
		t.Context(),
		fn,
	)
	require.NoError(t, err)

	// sanitize created_at
	want.CreatedAt = got.CreatedAt

	require.Equal(t, want, got)
}

// VerifyDBAutoCancelToBeCanceled verifies that the to-be-canceled autocancel in database matches expected *subscription.AutoCancel
func (c *databaseContainer) VerifyDBAutoCancelToBeCanceled(t *testing.T, want *subscription.AutoCancel) {
	t.Helper()

	var got *subscription.AutoCancel
	fn := func(ctx context.Context) error {
		var err error
		got, err = subscription.NewRepo(c.db, nil).FindToBeCanceled(ctx, want.CustomerPlanID)
		if errors.Is(err, sql.ErrNoRows) {
			log.Error("db autocancel record not yet available", zap.Error(err))
			return err
		}
		if err != nil {
			log.Error("failed to find autocancel record", zap.Error(err))
		}
		return nil
	}

	err := retrier.Do(
		t.Context(),
		fn,
	)
	require.NoError(t, err)

	// sanitize created_at
	want.CreatedAt = got.CreatedAt

	// validates that the CancellationDate is at least 71h59m after time.Now().
	require.WithinDuration(t, time.Now().Add(72*time.Hour), *got.CancellationDate, time.Minute)
	// sanitize got CancellationDate since we've already validated it
	got.CancellationDate = want.CancellationDate

	require.Equal(t, want, got)
}

// VerifyDBEvent verifies that event in database matches expected *proto.Message
func (c *databaseContainer) VerifyDBEvent(ctx context.Context, t *testing.T, want proto.Message, wantCount int) {
	t.Helper()

	protoType := string(proto.MessageName(want).Name())
	table, ok := protoTypeToDBTable[protoType]
	require.Truef(t, ok, "failed to find db table name for %s", protoType)

	var rows []*dbEventRow
	fn := c.retryDBQueryFunc(
		&rows,
		fmt.Sprintf(`
SELECT raw, created_at
FROM %s
ORDER BY created_at DESC
;
`,
			table,
		),
		c.db.SelectContext,
		wantCount,
	)

	err := retrier.Do(
		ctx,
		fn,
	)
	require.NoError(t, err)
	require.Equal(t, wantCount, len(rows))

	got := proto.Clone(want)
	err = proto.Unmarshal(rows[0].Raw, got)
	require.NoError(t, err)

	assert.Truef(t, proto.Equal(want, got), "wanted %v but got %v", want, got)
}

func (c *databaseContainer) retryDBQueryFunc(rows *[]*dbEventRow, query string, fn func(ctx context.Context, dest interface{}, query string, args ...interface{}) error, wantCount int) func(context.Context) error {
	return func(ctx context.Context) error {
		err := fn(ctx, rows, query)
		if err != nil {
			log.Error(
				"failed to query db",
				zap.String("query", query),
				zap.Error(err),
			)
		}
		if wantCount > 0 && len(*rows) < wantCount {
			msg := fmt.Sprintf("%d db records found, waiting for %d", len(*rows), wantCount)
			log.Error(
				msg,
				zap.String("query", query),
			)
			return errors.New(msg)
		}
		return nil
	}
}

func (c *databaseContainer) outboxReady() error {
	q := "SELECT relname FROM pg_catalog.pg_class where relname = 'outbox_message'"

	row := struct {
		RelName string `db:"relname"`
	}{}

	return c.db.GetContext(context.Background(), &row, q)
}

// Close the test database connection and purge container.
func (c *databaseContainer) close() (retErr error) {
	defer func() {
		if err := c.pool.Purge(c.container); err != nil {
			retErr = fmt.Errorf("failed to purge database container: %w", err)
			return
		}
	}()

	if c.conn == nil {
		return
	}

	ctx := context.Background()
	if err := c.conn.Close(ctx); err != nil {
		retErr = fmt.Errorf("failed to close connection: %w", err)
	}

	return
}

// dbMigrate runs the migrations. u is the connection URL string (e.g.
// postgres://...).
func dbMigrate(u string) error {
	// Run the migrations
	migrationsDir := fmt.Sprintf("file://%s", dbMigrationsDir())
	m, err := migrate.New(migrationsDir, u)
	if err != nil {
		return fmt.Errorf("failed create migrate: %w", err)
	}
	if err := m.Up(); err != nil && !errors.Is(err, migrate.ErrNoChange) {
		return fmt.Errorf("failed run migrate: %w", err)
	}
	srcErr, dbErr := m.Close()
	if srcErr != nil {
		return fmt.Errorf("migrate source error: %w", srcErr)
	}
	if dbErr != nil {
		return fmt.Errorf("migrate database error: %w", dbErr)
	}
	return nil
}

// dbMigrationsDir returns the path on disk to the migrations. It uses
// runtime.Caller() to get the path to the caller, since this package is
// imported by multiple others at different levels.
func dbMigrationsDir() string {
	_, filename, _, ok := runtime.Caller(1)
	if !ok {
		return ""
	}
	return filepath.Join(filepath.Dir(filename), "../../migrations")
}
