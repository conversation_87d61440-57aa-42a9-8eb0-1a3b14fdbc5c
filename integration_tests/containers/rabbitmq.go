package containers

import (
	"errors"
	"fmt"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
	"go.uber.org/zap"
)

const (
	rabbitMQInternalPort = 5672
	user                 = "credit-autocancel-service"
)

// rabbitMQContainer represents a new Docker-based rabbitmq instance.
type rabbitMQContainer struct {
	pool      *dockertest.Pool
	container *dockertest.Resource
	network   *dockertest.Network
}

// newRabbitMQContainer creates a new Docker-based rabbitmq instance.
func (dt *DockerTestInstance) newRabbitMQContainer(name string) (*rabbitMQContainer, error) {
	// Start the container.
	resource, err := dt.pool.RunWithOptions(&dockertest.RunOptions{
		Name:       name + "_rabbitmq",
		Repository: "rabbitmq",
		Tag:        "3.7-management",
		Networks:   []*dockertest.Network{dt.network},
		Env: []string{
			"RABBITMQ_DEFAULT_USER=" + user,
			"RABBITMQ_DEFAULT_PASS=" + user,
		},
	}, func(c *docker.HostConfig) {
		c.AutoRemove = true
		c.RestartPolicy = docker.RestartPolicy{Name: "no"}
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start rabbitmq container: %w", err)
	}

	c := &rabbitMQContainer{
		pool:      dt.pool,
		container: resource,
		network:   dt.network,
	}

	// Stop the container after it's been running for too long since tests should not take super long.
	if err := resource.Expire(uint((5 * time.Minute).Seconds())); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to expire rabbitmq container: %w", err)
	}

	if err = dt.pool.Retry(func() error {
		_, readyErr := c.container.Exec([]string{"rabbitmq-diagnostics", "check_port_connectivity"}, dockertest.ExecOptions{})
		if readyErr != nil {
			updatedContainer, err := dt.pool.Client.InspectContainer(resource.Container.ID)
			if err != nil {
				return backoff.Permanent(fmt.Errorf("failed to inspect container for integration tests: %w", err))
			}
			if !updatedContainer.State.Running {
				return backoff.Permanent(errors.New("rabbitmq container crashed"))
			}

			log.Error(
				"resource not yet ready",
				zap.String("status", updatedContainer.State.Status),
				zap.Error(readyErr),
			)

			return readyErr
		}

		return nil
	}); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed waiting for rabbitmq container to be ready: %w", err)
	}

	log.Debug(
		"rabbitmq ready",
		zap.String("resource_name", resource.Container.Name[1:]),
	)

	// Return the instance.
	return c, nil
}

// connectionURL is the url of the rabbitmq instance within a docker
func (c *rabbitMQContainer) connectionURL() string {
	return fmt.Sprintf("amqp://%s:%s@%s:%d", user, user, c.container.Container.Name, rabbitMQInternalPort)
}

// close the test rabbitmq container.
func (c *rabbitMQContainer) close() error {
	if err := c.pool.Purge(c.container); err != nil {
		return fmt.Errorf("failed to purge rabbitmq container: %w", err)
	}
	return nil
}
