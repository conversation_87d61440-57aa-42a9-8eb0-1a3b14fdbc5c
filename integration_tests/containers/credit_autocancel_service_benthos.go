package containers

import (
	"errors"
	"fmt"
	"net/url"
	"os"
	"path"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/docker/docker/pkg/namesgenerator"
	"github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
)

const (
	benthosPort = "9090"
)

var errGroup = errgroup.Group{}

// creditAutoCancelServiceBenthosContainer is a wrapper around the Docker-based benthos instance.
type creditAutoCancelServiceBenthosContainer struct {
	pool        *dockertest.Pool
	container   *dockertest.Resource
	network     *dockertest.Network
	closeWaiter docker.CloseWaiter
}

func (dt *DockerTestInstance) buildCreditAutoCancelServiceBenthosImage() {
	dockerFile := "benthos.integration.Dockerfile"
	if !dt.Config.SkipBuild {
		log.Debug("rebuilding benthos binary")
		dockerFile = "benthos.Dockerfile"
	}

	file := path.Join(os.Getenv("PWD"), "..", "dist", "credit-autocancel-service-benthos")
	if _, err := os.Stat(file); err != nil && dt.Config.SkipBuild {
		log.Info("could not find file, skipping build dockertest image", zap.String("file", file))
		return
	}

	errGroup.Go(func() error {
		if err := dt.pool.Client.BuildImage(docker.BuildImageOptions{
			Name:         "credit-autocancel-service-benthos",
			Dockerfile:   dockerFile,
			OutputStream: os.Stdout,
			ErrorStream:  os.Stderr,
			ContextDir:   "../",
			BuildArgs: []docker.BuildArg{
				{
					Name:  "GITHUB_TOKEN",
					Value: os.Getenv("GITHUB_TOKEN"),
				},
			},
		}); err != nil {
			return fmt.Errorf("could not build benthos docker image: %w", err)
		}
		return nil
	})
}

// NewBenthosContainerWithWiremockProvider returns a *DockerTestContainers with provider pointed to a wiremock container
func (dt *DockerTestInstance) NewBenthosContainerWithWiremockProvider(kafkaBatchingCountOrderPaid int) (*DockerTestContainers, error) {
	name := namesgenerator.GetRandomName(0)

	dc := DockerTestContainers{
		Name: name,
	}

	errGroup.Go(func() error {
		var err error
		dc.Database, err = dt.newDatabaseContainer(name)
		return err
	})

	errGroup.Go(func() error {
		var err error
		dc.Wiremock, err = dt.newWiremockContainer(name)
		return err
	})

	errGroup.Go(func() error {
		var err error
		dc.Kafka, err = dt.newKafkaContainer(name)
		return err
	})

	errGroup.Go(func() error {
		var err error
		dc.RabbitMQ, err = dt.newRabbitMQContainer(name)
		return err
	})

	err := errGroup.Wait()
	if err != nil {
		dt.Close()
		return nil, err
	}

	go func() {
		var err error
		dc.OutboxWorker, err = dt.newOutboxWorkerContainer(name, dc)
		if err != nil {
			log.Fatal("failure creating credit autocancel service benthos outbox worker", zap.Error(err))
		}
	}()

	env := dc.benthosEnv(kafkaBatchingCountOrderPaid)

	dc.CreditAutoCancelServiceBenthos, err = dt.runBenthosContainer(name, env)
	if err != nil {
		dt.Close()
		return nil, err
	}

	dt.Containers[name] = &dc

	return &dc, err
}

func (dt *DockerTestInstance) runBenthosContainer(name string, env []string) (*creditAutoCancelServiceBenthosContainer, error) {
	resource, err := dt.pool.RunWithOptions(&dockertest.RunOptions{
		Name:       name + "_benthos",
		Repository: "credit-autocancel-service-benthos",
		Tag:        "latest",
		Networks:   []*dockertest.Network{dt.network},
		Env:        env,
	}, func(c *docker.HostConfig) {
		c.AutoRemove = true
		c.RestartPolicy = docker.RestartPolicy{Name: "no"}
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start benthos container: %w", err)
	}

	resourceName := resource.Container.Name[1:]

	log.Debug(
		"finished building benthos",
		zap.String("resource_name", resourceName),
	)

	// Build the connection URL.
	connectionURL := &url.URL{
		Scheme: "http",
		Host:   getResourceHostPort(resource, benthosPort+"/tcp"),
	}

	c := &creditAutoCancelServiceBenthosContainer{
		pool:      dt.pool,
		container: resource,
		network:   dt.network,
	}

	c.closeWaiter, err = dt.pool.Client.AttachToContainerNonBlocking(docker.AttachToContainerOptions{
		Container:    resourceName,
		OutputStream: os.Stdout,
		ErrorStream:  os.Stderr,
		RawTerminal:  true,
		Logs:         true,
		Stream:       true,
		Stdout:       true,
		Stderr:       true,
	})
	if err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		fmt.Println("unable to get logs for benthos container: ", err)
	}

	// Stop the container after it's been running for too long since tests should not take super long.
	if err := resource.Expire(uint((5 * time.Minute).Seconds())); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to expire container for integration tests: %w", err)
	}

	// exponential backoff-retry, because the application in the container might not be ready to accept connections yet
	if err = dt.pool.Retry(func() error {
		readyErr := retryReadinessCheck(connectionURL, "health")
		if readyErr != nil {
			updatedContainer, err := dt.pool.Client.InspectContainer(resource.Container.ID)
			if err != nil {
				return backoff.Permanent(fmt.Errorf("failed to inspect container for integration tests: %w", err))
			}
			if !updatedContainer.State.Running {
				return backoff.Permanent(errors.New("benthos container crashed"))
			}

			log.Error(
				"resource not yet ready",
				zap.String("status", updatedContainer.State.Status),
				zap.String("url", connectionURL.String()),
				zap.Error(readyErr),
			)
		}
		return readyErr
	}); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed waiting for integration test container to be ready: %w", err)
	}

	log.Debug(
		"benthos ready",
		zap.String("resource_name", resourceName),
		zap.String("url", connectionURL.String()),
	)

	return c, nil
}

func (dc *DockerTestContainers) benthosEnv(kafkaBatchingCountOrderPaid int) []string {
	env := []string{
		"DB_HOST=" + dc.Database.hostInNetwork(),
		"DB_PORT=" + databasePort,
		"DB_NAME=" + databaseName,
		"DB_USER=" + databaseUser,
		"DB_PASSWORD=" + databasePassword,
		"KAFKA_DSN=" + dc.Kafka.urlInNetwork(),
		"PORT=" + benthosPort,
		"TLS_ENABLED=false",
		"KAFKA_SASL_MECHANISM=none",
		"BENTHOS_KAFKA_START_FROM_OLDEST=true",
		"BENTHOS_LOG_LEVEL=DEBUG",
		fmt.Sprintf("BENTHOS_KAFKA_BATCHING_COUNT_ORDER=%d", kafkaBatchingCountOrderPaid),
		"BENTHOS_KAFKA_INGEST_BATCHING_COUNT=1",
		"KAFKA_USERNAME=",
		"KAFKA_PASSWORD=",
		"KAFKA_CA_CERT_FILE=",
		"AMQP_DSN=" + dc.RabbitMQ.connectionURL(),
		"VOUCHER_SERVICE_URL=" + dc.Wiremock.urlInNetwork().String(),
		"AUTH_URL=" + dc.Wiremock.urlInNetwork().String(),
		"CLIENT_ID=" + "test_client_id",
		"CLIENT_SECRET=" + "test_client_secret",
		"PRICE_SERVICE_URL=" + dc.Wiremock.urlInNetwork().String(),
	}

	return env
}

// close terminates the test benthos instance, cleaning up any resources,
func (c *creditAutoCancelServiceBenthosContainer) close() error {
	if err := c.closeWaiter.Close(); err != nil {
		log.Error("failed to close benthos container logger", zap.Error(err))
	}
	if err := c.pool.Purge(c.container); err != nil {
		return fmt.Errorf("failed to purge benthos container: %w", err)
	}
	return nil
}
