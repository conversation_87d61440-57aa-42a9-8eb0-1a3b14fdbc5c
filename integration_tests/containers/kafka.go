package containers

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
	"github.com/twmb/franz-go/pkg/kadm"
	"github.com/twmb/franz-go/pkg/kgo"
	"github.com/twmb/franz-go/pkg/kversion"
	"go.uber.org/zap"
)

const (
	kafkaPort           = "9092"
	kafkaInternalPort   = "29092"
	kafkaControllerPort = "29093"
)

type kafkaContainer struct {
	pool          *dockertest.Pool
	container     *dockertest.Resource
	network       *dockertest.Network
	connectionURL string
	*kgo.Client
	adminClient *kadm.Client
}

// newKafkaContainer creates a new Docker-based kafka instance.
func (dt *DockerTestInstance) newKafkaContainer(name string) (*kafkaContainer, error) {
	resource, err := dt.pool.RunWithOptions(&dockertest.RunOptions{
		Name:       fmt.Sprintf("%s_%s", name, "kafka"),
		Repository: "confluentinc/cp-kafka",
		Tag:        "7.3.1",
		Networks:   []*dockertest.Network{dt.network},
		Hostname:   "kafka",
		Env: []string{
			"KAFKA_NODE_ID=1",
			"KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT",
			fmt.Sprintf("KAFKA_LISTENERS=PLAINTEXT://:%s,CONTROLLER://kafka:%s,PLAINTEXT_HOST://0.0.0.0:%s", kafkaInternalPort, kafkaControllerPort, kafkaPort),
			fmt.Sprintf("KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:%s,PLAINTEXT_HOST://localhost:%s", kafkaInternalPort, kafkaPort),
			fmt.Sprintf("KAFKA_CONTROLLER_QUORUM_VOTERS=1@kafka:%s", kafkaControllerPort),
			"KAFKA_PROCESS_ROLES=broker,controller",
			"KAFKA_INTER_BROKER_LISTENER_NAME=PLAINTEXT",
			"KAFKA_CONTROLLER_LISTENER_NAMES=CONTROLLER",
			"KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1",
			"KAFKA_SOCKET_REQUEST_MAX_BYTES=1048576000",
			"KAFKA_HEAP_OPTS=-Xmx8g -Xms8g -XX:MetaspaceSize=96m -XX:+UseG1GC -XX:MaxGCPauseMillis=20 -XX:InitiatingHeapOccupancyPercent=35 -XX:G1HeapRegionSize=16M -XX:MinMetaspaceFreeRatio=50 -XX:MaxMetaspaceFreeRatio=80",
		},
		PortBindings: map[docker.Port][]docker.PortBinding{
			kafkaPort + "/tcp": {{HostIP: "localhost", HostPort: kafkaPort + "/tcp"}},
		},
		ExposedPorts: []string{kafkaPort + "/tcp"},
		Cmd: []string{
			"bash",
			"-c",
			"sed -i '/KAFKA_ZOOKEEPER_CONNECT/d' /etc/confluent/docker/configure && sed -i 's/cub zk-ready/echo ignore zk-ready/' /etc/confluent/docker/ensure && echo \"kafka-storage format --ignore-formatted -t $(kafka-storage random-uuid) -c /etc/kafka/kafka.properties\" >> /etc/confluent/docker/ensure && /etc/confluent/docker/run",
		},
	}, func(c *docker.HostConfig) {
		c.AutoRemove = true
		c.RestartPolicy = docker.RestartPolicy{Name: "no"}
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start kafka container: %w", err)
	}

	c := &kafkaContainer{
		pool:      dt.pool,
		container: resource,
		network:   dt.network,
	}

	// Build the connection URL.
	connectionURL := &url.URL{
		Host: getResourceHostPort(resource, kafkaPort+"/tcp"),
	}

	// kafka client Addr doesn't support a url scheme so we need to strip "//" from the front
	if len(connectionURL.String()) < 2 {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to build kafka container connection url: %w", err)
	}
	c.connectionURL = connectionURL.String()[2:]

	// Stop the container after it's been running for too long since tests should not take super long.
	if err = resource.Expire(uint((5 * time.Minute).Seconds())); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to expire kafka container: %w", err)
	}

	c.Client, err = kgo.NewClient(
		kgo.SeedBrokers(c.connectionURL),
		kgo.RequestRetries(60),
		kgo.RetryTimeout(1*time.Minute),
		// Do not try to send requests newer than 2.4.0 to avoid breaking changes in the request struct.
		// Sometimes there are breaking changes for newer versions where more properties are required to set.
		kgo.MaxVersions(kversion.V2_4_0()),
	)
	if err != nil {
		return nil, fmt.Errorf("failed creating kafka admin client: %w", err)
	}

	c.adminClient = kadm.NewClient(c.Client)

	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Minute)
	defer cancel()

	retryFn := func() error {
		_, err = c.adminClient.ListTopics(ctx)
		if err != nil {
			return fmt.Errorf("failed to list topics: %w", err)
		}

		return nil
	}

	// Try to establish a connection to the kafka, with retries.
	if err = dt.pool.Retry(func() error {
		readyErr := retryFn()
		if readyErr != nil {
			updatedContainer, err := dt.pool.Client.InspectContainer(resource.Container.ID)
			if err != nil {
				return backoff.Permanent(fmt.Errorf("failed to inspect kafka container: %w", err))
			}
			if !updatedContainer.State.Running {
				return backoff.Permanent(errors.New("kafka container crashed"))
			}

			log.Error(
				"resource not yet ready",
				zap.String("status", updatedContainer.State.Status),
				zap.String("url", c.connectionURL),
				zap.Error(readyErr),
			)
		}
		return readyErr
	}); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed waiting for kafka container to be ready: %w", err)
	}

	log.Debug(
		"kafka ready",
		zap.String("resource_name", resource.Container.Name[1:]),
		zap.String("url", c.connectionURL),
	)

	err = c.createTopics(ctx)
	if err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, err
	}

	return c, err
}

func (c *kafkaContainer) createTopics(ctx context.Context) error {
	topics := []string{
		"public.customer.v1beta2",
		"public.customer.order.v1",
		"public.customer.order.delivery.v1",
		"public.customer.order.payment.v1",
		"credit-autocancel-service.benthos.trigger-order-paid",
		"public.customer.benefit.attachment.v2beta1",
		"credit-autocancel-service.benthos.trigger-benefit-attachment",
	}

	res, err := c.adminClient.CreateTopics(ctx, 1, 1, nil, topics...)
	if err != nil {
		return fmt.Errorf("failed creating kafka topics: %w", err)
	}

	for _, topic := range res {
		if topic.Err != nil {
			log.Error("failed to create topic", zap.String("topic", topic.Topic), zap.Error(topic.Err))
		} else {
			log.Info("created topic", zap.String("topic", topic.Topic))
		}
	}

	return nil
}

// urlInNetwork is the url of the kafka instance within a docker network
func (c *kafkaContainer) urlInNetwork() string {
	return c.container.GetIPInNetwork(c.network) + ":" + kafkaInternalPort
}

// Close the test kafka container.
func (c *kafkaContainer) close() error {
	if c.adminClient != nil {
		c.adminClient.Close()
	}

	if err := c.pool.Purge(c.container); err != nil {
		return fmt.Errorf("failed to purge %s container: %w", c.container.Container.Config.Hostname, err)
	}

	return nil
}
