package containers

import (
	"strings"

	"github.com/ory/dockertest/v3"
)

// getResourceHostPort returns a resource's published port with an address.
// Should be used over `dockertest.Resource.GetHostPort` as this function will avoid IPv6 configuration issues by
// replacing `localhost` with `127.0.0.1`.
func getResourceHostPort(resource *dockertest.Resource, portID string) string {
	return strings.ReplaceAll(resource.GetHostPort(portID), "localhost", "127.0.0.1")
}
