package containers

import (
	"errors"
	"fmt"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
	"go.uber.org/zap"
)

// outboxWorkerContainer represents a new Docker-based outbox worker instance.
type outboxWorkerContainer struct {
	pool      *dockertest.Pool
	container *dockertest.Resource
	network   *dockertest.Network
}

// newOutboxWorkerContainer creates a new Docker-based outbox worker instance.
func (dt *DockerTestInstance) newOutboxWorkerContainer(name string, dc DockerTestContainers) (*outboxWorkerContainer, error) {
	if dc.Database == nil {
		return nil, errors.New("db is required for outbox worker")
	}
	if dc.RabbitMQ == nil {
		return nil, errors.New("rabbitmq is required for outbox worker")
	}

	// Start the container.
	resource, err := dt.pool.RunWithOptions(&dockertest.RunOptions{
		Name:       name + "_outbox",
		Repository: "489198589229.dkr.ecr.eu-west-1.amazonaws.com/outbox-worker",
		Tag:        "stable",
		Networks:   []*dockertest.Network{dt.network},
		Env: []string{
			"DB_DSN=" + dc.Database.urlInNetwork().String(),
			"DB_PASSWORD=" + databasePassword,
			"AMQP_DSN=" + dc.RabbitMQ.connectionURL(),
			"AMQP_PASSWORD=credit-autocancel-service",
			"TRIGGER_INTERVAL=50ms",
		},
		Auth: *dt.auth,
	}, func(c *docker.HostConfig) {
		c.AutoRemove = true
		c.RestartPolicy = docker.RestartPolicy{Name: "no"}
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start outbox worker container: %w", err)
	}

	c := &outboxWorkerContainer{
		pool:      dt.pool,
		container: resource,
		network:   dt.network,
	}

	// Stop the container after it's been running for too long since tests should not take super long.
	if err := resource.Expire(uint((5 * time.Minute).Seconds())); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to expire outbox worker container: %w", err)
	}

	if err = dt.pool.Retry(func() error {
		readyErr := dc.Database.outboxReady()
		if readyErr != nil {
			updatedContainer, err := dt.pool.Client.InspectContainer(resource.Container.ID)
			if err != nil {
				return backoff.Permanent(fmt.Errorf("failed to inspect container for integration tests: %w", err))
			}
			if !updatedContainer.State.Running {
				return backoff.Permanent(errors.New("outbox worker container crashed"))
			}

			log.Error(
				"resource not yet ready",
				zap.String("status", updatedContainer.State.Status),
				zap.Error(readyErr),
			)

			return readyErr
		}

		return nil
	}); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed waiting for outbox worker container to be ready: %w", err)
	}

	log.Debug(
		"outbox worker ready",
		zap.String("resource_name", resource.Container.Name[1:]),
	)

	// Return the instance.
	return c, nil
}

// close the test outbox worker container.
func (c *outboxWorkerContainer) close() error {
	if err := c.pool.Purge(c.container); err != nil {
		return fmt.Errorf("failed to purge outbox worker container: %w", err)
	}
	return nil
}
