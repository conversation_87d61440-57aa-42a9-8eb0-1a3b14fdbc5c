package containers

import (
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path"
	"strings"
	"time"

	"github.com/cenkalti/backoff/v4"
	"github.com/hellofresh/credit-autocancel-service/integration_tests/testdata"
	"github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
	"github.com/walkerus/go-wiremock"
	"go.uber.org/zap"
)

const (
	wiremockPort = "8080"
)

type wiremockContainer struct {
	pool                 *dockertest.Pool
	container            *dockertest.Resource
	network              *dockertest.Network
	connectionURL        *url.URL
	client               *wiremock.Client
	voucherStubRule      *wiremock.StubRule
	authServiceStubRule  *wiremock.StubRule
	priceServiceStubRule *wiremock.StubRule
}

// NewWiremockContainer creates a new Docker-based wiremock instance.
//
// This should not be used outside of testing, but it is exposed in the package
// so it can be shared with other packages. It should be called and instantiated
// in TestMain.
func (dt *DockerTestInstance) newWiremockContainer(name string) (*wiremockContainer, error) {
	// Start the container.
	resource, err := dt.pool.RunWithOptions(&dockertest.RunOptions{
		Name:       name + "_wiremock",
		Repository: "wiremock/wiremock",
		Tag:        "2.32.0-alpine",
		Mounts:     []string{path.Join(os.Getenv("PWD"), "..", "testdata", "wiremock") + ":/home/<USER>"},
		Networks:   []*dockertest.Network{dt.network},
	}, func(c *docker.HostConfig) {
		c.AutoRemove = true
		c.RestartPolicy = docker.RestartPolicy{Name: "no"}
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start wiremock container: %w", err)
	}

	// Build the connection URL.
	connectionURL := &url.URL{
		Scheme: "http",
		Host:   getResourceHostPort(resource, wiremockPort+"/tcp"),
	}

	c := &wiremockContainer{
		pool:          dt.pool,
		container:     resource,
		network:       dt.network,
		connectionURL: connectionURL,
		client:        wiremock.NewClient(connectionURL.String()),
	}

	// Stop the container after it's been running for too long since tests should not take super long.
	if err = resource.Expire(uint((5 * time.Minute).Seconds())); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed to expire database container: %w", err)
	}

	// Try to establish a connection to the wiremock, with retries.
	if err = dt.pool.Retry(func() error {
		readyErr := retryReadinessCheck(connectionURL, "__admin")
		if readyErr != nil {
			updatedContainer, err := dt.pool.Client.InspectContainer(resource.Container.ID)
			if err != nil {
				return backoff.Permanent(fmt.Errorf("failed to inspect wiremock container: %w", err))
			}
			if !updatedContainer.State.Running {
				return backoff.Permanent(errors.New("wiremock container crashed"))
			}

			log.Error(
				"resource not yet ready",
				zap.String("status", updatedContainer.State.Status),
				zap.String("url", connectionURL.String()),
				zap.Error(readyErr),
			)
		}
		return readyErr
	}); err != nil {
		if closeErr := c.close(); closeErr != nil {
			return nil, fmt.Errorf("%w: %w", closeErr, err)
		}
		return nil, fmt.Errorf("failed waiting for wiremock container to be ready: %w", err)
	}

	log.Debug(
		"wiremock ready",
		zap.String("resource_name", resource.Container.Name[1:]),
		zap.String("url", connectionURL.String()),
	)

	// EnableVoucherAPISuccessStubRule by default
	if err = c.EnableVoucherAPISuccessStubRule(); err != nil {
		return nil, err
	}

	// EnableAuthServiceSuccessStubRule by default
	if err = c.EnableAuthServiceSuccessStubRule(); err != nil {
		return nil, err
	}

	// EnablePriceServiceSuccessStubRule by default
	if err = c.EnablePriceServiceSuccessStubRule(); err != nil {
		return nil, err
	}

	return c, nil
}

// urlInNetwork is the url of the instance within a docker network
func (c *wiremockContainer) urlInNetwork() *url.URL {
	return &url.URL{
		Scheme: c.connectionURL.Scheme,
		Host:   c.container.GetIPInNetwork(c.network) + ":" + wiremockPort,
	}
}

// Verify checks if the number of POST /vouchers/code requests is equal to numberOfRequests
func (c *wiremockContainer) verify(expectedCount int64, stubRule *wiremock.StubRule, service string) error {
	actualCount, err := c.client.GetCountRequests(stubRule.Request())
	if err != nil {
		return err
	}

	if expectedCount != actualCount {
		return fmt.Errorf("wiremock container received %d requests but expected %d for %s", actualCount, expectedCount, service)
	}

	return nil
}

// VoucherServiceVerify checks if the number of POST /vouchers/code requests is equal to numberOfRequests
func (c *wiremockContainer) VoucherServiceVerify(expectedCount int64) error {
	return c.verify(expectedCount, c.voucherStubRule, "voucherService")
}

// GetVoucherServiceCountRequests returns the number of POST /vouchers/code requests
func (c *wiremockContainer) GetVoucherServiceCountRequests() (int64, error) {
	return c.client.GetCountRequests(c.voucherStubRule.Request())
}

// AuthServiceVerify checks if the number of POST /token requests is equal to numberOfRequests
func (c *wiremockContainer) AuthServiceVerify(expectedCount int64) error {
	return c.verify(expectedCount, c.authServiceStubRule, "authService")
}

// PriceServiceVerify checks if the number of POST /calculate requests is equal to numberOfRequests
func (c *wiremockContainer) PriceServiceVerify(expectedCount int64) error {
	return c.verify(expectedCount, c.priceServiceStubRule, "priceService")
}

// EnableVoucherAPISuccessStubRule enables the stubRule to respond to GET /vouchers/code requests with 200 and voucher_service_response.json in body
// Any already existing stub rule will be deleted. VoucherAPISuccessStubRule is enabled by default by newWiremockContainer().
func (c *wiremockContainer) EnableVoucherAPISuccessStubRule() error {
	c.voucherStubRule = wiremock.Get(wiremock.URLPathEqualTo(fmt.Sprintf("/vouchers/code/%s", testdata.VoucherCode))).
		WithQueryParam("country", wiremock.EqualTo(strings.ToUpper(testdata.CustomerOrderEventValue.GetRegionCode()))).
		WillReturnResponse(wiremock.NewResponse().
			WithBinaryBody(testdata.VoucherServiceResponse).
			WithHeaders(map[string]string{"Content-Type": "application/json"}).
			WithStatus(http.StatusOK))

	err := c.client.StubFor(c.voucherStubRule)
	if err != nil {
		return fmt.Errorf("failed to create voucherStubRule wiremock mapping: %w", err)
	}

	return nil
}

// EnableAuthServiceSuccessStubRule enables the stubRule to respond to POST /token requests with 200 and an auth_service_response.json in body
// Any already existing stub rule will be deleted. AuthServiceSuccessStubRule is enabled by default by newWiremockContainer().
func (c *wiremockContainer) EnableAuthServiceSuccessStubRule() error {
	c.authServiceStubRule = wiremock.Post(wiremock.URLPathEqualTo("/token")).
		WithBodyPattern(wiremock.MatchingJsonPath("$[?(@.client_id == 'test_client_id')]")).
		WithBodyPattern(wiremock.MatchingJsonPath("$[?(@.client_secret == 'test_client_secret')]")).
		WithBodyPattern(wiremock.MatchingJsonPath("$[?(@.grant_type == 'client_credentials')]")).
		WillReturnResponse(wiremock.NewResponse().
			WithBinaryBody(testdata.AuthServiceResponse).
			WithHeaders(map[string]string{"Content-Type": "application/json"}).
			WithStatus(http.StatusOK))

	err := c.client.StubFor(c.authServiceStubRule)
	if err != nil {
		return fmt.Errorf("failed to create authServiceStubRule wiremock mapping: %w", err)
	}

	return nil
}

// EnablePriceServiceSuccessStubRule enables the stubRule to respond to POST /calculate requests with 200 and a price_service_response.json in body
// Any already existing stub rule will be deleted. PriceServiceSuccessStubRule is enabled by default by newWiremockContainer().
func (c *wiremockContainer) EnablePriceServiceSuccessStubRule() error {
	c.priceServiceStubRule = wiremock.Post(wiremock.URLPathEqualTo("/calculate")).
		WithBodyPattern(wiremock.EqualToJson(
			fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":true,"shippingAddress":{"postcode":"60000"}}`,
				testdata.CustomerOrderEventValue.GetItems()[0].GetProductHandle(),
				testdata.VoucherCode,
				testdata.CustomerEventValue.GetLegacyId(), // nolint:staticcheck
				testdata.CustomerOrderEventValue.GetCustomerId(),
				testdata.CustomerOrderEventValue.GetPlanId(),
				testdata.CustomerOrderEventValue.GetSubscriptionId(), // nolint:staticcheck
				strings.ToUpper(testdata.CustomerOrderEventValue.GetRegionCode())))).
		WithHeader("Authorization", wiremock.EqualTo("Bearer test_token")).
		WillReturnResponse(wiremock.NewResponse().
			WithBinaryBody(testdata.PriceServiceResponse).
			WithHeaders(map[string]string{"Content-Type": "application/json"}).
			WithStatus(http.StatusOK))

	err := c.client.StubFor(c.priceServiceStubRule)
	if err != nil {
		return fmt.Errorf("failed to create priceServiceStubRule wiremock mapping: %w", err)
	}

	return nil
}

// ClearStubRules deletes any existing stubRule and activates the new one.
func (c *wiremockContainer) ClearStubRules() error {
	return c.client.Clear()
}

// Close the test redis container.
func (c *wiremockContainer) close() error {
	if err := c.pool.Purge(c.container); err != nil {
		return fmt.Errorf("failed to purge wiremock container: %w", err)
	}
	return nil
}
