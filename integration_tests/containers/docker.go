package containers

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/docker/docker/pkg/namesgenerator"
	"github.com/hellofresh/growth-go-kit/logger"
	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
	"go.uber.org/zap"
)

// DockerTestInstance contains a dockertest pool and network, a docker image of credit-autocancel-service, and a map of clean DockerTestContainers
type DockerTestInstance struct {
	pool       *dockertest.Pool
	network    *dockertest.Network
	Containers map[string]*DockerTestContainers
	Config     Config
	auth       *docker.AuthConfiguration
}

// DockerTestContainers is a wrapper around the Docker-based test resource with dependency containers.
type DockerTestContainers struct {
	Database                       *databaseContainer
	Wiremock                       *wiremockContainer
	Kafka                          *kafkaContainer
	RabbitMQ                       *rabbitMQContainer
	OutboxWorker                   *outboxWorkerContainer
	CreditAutoCancelServiceBenthos *creditAutoCancelServiceBenthosContainer
	Name                           string
}

var log *zap.Logger

func init() {
	var err error
	log, err = logger.InitLogger(logger.WithLevel(logger.LevelDebug))
	if err != nil {
		panic(fmt.Errorf("error initializing logger: %w", err))
	}

	readinessClient = httputil.NewClient(httputil.WithHTTPClient(http.DefaultClient))
}

// NewDockerTest returns a *DockerTestInstance and builds the credit-autocancel-service docker image
func NewDockerTest() (*DockerTestInstance, error) {
	dt := DockerTestInstance{
		Containers: make(map[string]*DockerTestContainers),
	}

	var err error
	dt.pool, err = dockertest.NewPool("")
	if err != nil {
		return nil, fmt.Errorf("failed to create docker pool: %w", err)
	}
	dt.pool.MaxWait = 2 * time.Minute // increase max wait time for docker containers to come up

	if err = dt.getConfig(); err != nil {
		return nil, fmt.Errorf("failed to process config: %w", err)
	}

	if dt.auth, err = getECRCredentials(); err != nil {
		return nil, fmt.Errorf("failed to get ecr credentials: %w", err)
	}

	dt.buildCreditAutoCancelServiceBenthosImage()

	dt.network, err = dt.pool.CreateNetwork(namesgenerator.GetRandomName(0) + "_network")
	if err != nil {
		return nil, fmt.Errorf("could not create docker network: %w", err)
	}

	return &dt, nil
}

// CloseContainers terminates the test instance containers, cleaning up any resources.
func (dt *DockerTestInstance) CloseContainers(name string) {
	dc, found := dt.Containers[name]
	if !found {
		log.Error("failed to find containers", zap.String("container_name", name))
		return
	}

	if dc.CreditAutoCancelServiceBenthos != nil {
		if err := dc.CreditAutoCancelServiceBenthos.close(); err != nil {
			log.Error("failed to close benthos container", zap.Error(err))
		}
	}
	if dc.Database != nil {
		if err := dc.Database.close(); err != nil {
			log.Error("failed to close database container", zap.Error(err))
		}
	}
	if dc.Wiremock != nil {
		if err := dc.Wiremock.close(); err != nil {
			log.Error("failed to close wiremock container", zap.Error(err))
		}
	}
	if dc.OutboxWorker != nil {
		if err := dc.OutboxWorker.close(); err != nil {
			log.Error("failed to close outbox worker container", zap.Error(err))
		}
	}
	if dc.Kafka != nil {
		if err := dc.Kafka.close(); err != nil {
			log.Error("failed to close kafka container", zap.Error(err))
		}
	}
	if dc.RabbitMQ != nil {
		if err := dc.RabbitMQ.close(); err != nil {
			log.Error("failed to close rabbitmq container", zap.Error(err))
		}
	}

	delete(dt.Containers, name)
}

// Close terminates the test instance containers, cleaning up any resources.
func (dt *DockerTestInstance) Close() {
	for _, container := range dt.Containers {
		dt.CloseContainers(container.Name)
	}

	if err := dt.removeNetwork(); err != nil {
		log.Error("failed to remove network", zap.Error(err))
	}

	if err := dt.removeDanglingImages(); err != nil {
		log.Error("failed to remove dangling images", zap.Error(err))
	}
}

func (dt *DockerTestInstance) removeNetwork() error {
	if err := dt.pool.RemoveNetwork(dt.network); err != nil {
		return fmt.Errorf("failed to remove network: %w", err)
	}

	prunedNetworks, err := dt.pool.Client.PruneNetworks(docker.PruneNetworksOptions{Context: context.Background()})
	if err != nil {
		return fmt.Errorf("failed to prune docker networks: %w", err)
	}
	if len(prunedNetworks.NetworksDeleted) > 0 {
		log.Info("pruned docker networks", zap.Any("networks", prunedNetworks))
	}
	return nil
}

func (dt *DockerTestInstance) removeDanglingImages() error {
	images, err := dt.pool.Client.ListImages(docker.ListImagesOptions{
		Filters: map[string][]string{
			"dangling": {"true"},
		},
		Context: context.Background(),
	})
	if err != nil {
		return fmt.Errorf("failed to list dangling images: %w", err)
	}

	for _, image := range images {
		rmiErr := dt.pool.Client.RemoveImage(image.ID)
		if rmiErr != nil {
			// ignore errors caused by dangling images in use by other running containers on dev's machine
			var dockerError *docker.Error
			if !errors.As(rmiErr, &dockerError) || dockerError.Status != http.StatusConflict {
				err = fmt.Errorf("failed to remove dangling image %s: %w", image.ID, rmiErr)
			}
		}
	}
	return err
}
