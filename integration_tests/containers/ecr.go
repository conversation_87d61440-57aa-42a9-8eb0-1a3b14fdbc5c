package containers

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/ecr"
	"github.com/ory/dockertest/v3/docker"
	"go.uber.org/zap"
)

func getECRCredentials() (*docker.AuthConfiguration, error) {
	ctx := context.Background()
	cfg, err := config.LoadDefaultConfig(
		ctx,
		config.WithDefaultRegion("eu-west-1"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load default ecr config: %w", err)
	}

	svc := ecr.NewFromConfig(cfg)
	token, err := svc.GetAuthorizationToken(ctx, &ecr.GetAuthorizationTokenInput{})
	if err != nil {
		if loginErr := loginToAWS(); loginErr != nil {
			return nil, err
		}
		if token, err = svc.GetAuthorizationToken(ctx, &ecr.GetAuthorizationTokenInput{}); err != nil {
			return nil, fmt.Errorf("failed to get ecr authorization token: %w\n\ntry running `aws sso login`", err)
		}
	}

	authData := token.AuthorizationData[0].AuthorizationToken
	data, err := base64.StdEncoding.DecodeString(*authData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode ecr authorization data: %w", err)
	}

	creds := strings.SplitN(string(data), ":", 2)
	if len(creds) < 2 {
		return nil, fmt.Errorf("failed to parse ecr authorization data: %w", err)
	}

	return &docker.AuthConfiguration{
		Username: "AWS",
		Password: creds[1],
	}, nil
}

func loginToAWS() error {
	cmd := exec.Command("aws", "sso", "login")
	cmd.Stdout = os.Stdout
	cmd.Stdin = os.Stdout
	cmd.Stderr = os.Stdout
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to run aws sso login command: %w", err)
	}

	timer := time.AfterFunc(time.Second*30, func() {
		if err := cmd.Process.Kill(); err != nil {
			log.Error("failed to kill aws sso login command", zap.Error(err))
		}
	})
	defer timer.Stop()

	if err := cmd.Wait(); err != nil {
		return fmt.Errorf("failed to wait for aws sso login command: %w", err)
	}
	return nil
}
