package integrationtests

import (
	"context"
	"testing"
	"time"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/credit-autocancel-service/integration_tests/containers"
	"github.com/hellofresh/credit-autocancel-service/integration_tests/testdata"
	"github.com/hellofresh/credit-autocancel-service/internal/subscription"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestBenthos(t *testing.T) {
	t.<PERSON>l()

	// we will publish an order event, delivery event, and payment event, each of which trigger the order-paid private event topic
	// so batch count of 3 will verify that deduplicate_order_paid is working if we only have 1 call to voucher-service
	benthosBatchingCountOrderPaid := 3

	testContainers, err := testInstance.NewBenthosContainerWithWiremockProvider(benthosBatchingCountOrderPaid)
	require.NoError(t, err)

	t.Cleanup(func() {
		testInstance.CloseContainers(testContainers.Name)
	})

	testdata.RequireTestDataReady(t)

	ctx := context.Background()

	err = testContainers.Kafka.ProduceSync(ctx, testdata.CustomerEvent).FirstErr()
	require.NoError(t, err)

	// publish benefit attached event
	statusRunning := "running"
	err = testContainers.Kafka.ProduceSync(ctx, testdata.CustomerBenefitAttachmentEvent).FirstErr()
	require.NoError(t, err)
	testContainers.Database.VerifyDBEvent(ctx, t, testdata.CustomerBenefitAttachmentEventValue, 1)
	testContainers.Database.VerifyDBAutoCancelBenefitAttached(
		t,
		&subscription.AutoCancel{
			Status: &statusRunning,
			CustomerPlanID: uuid.NullUUID{
				UUID:  uuid.FromStringOrNil(testdata.CustomerBenefitAttachmentEventValue.GetCustomerPlanId()),
				Valid: true,
			},
			Voucher: &testdata.VoucherCode,
		},
	)
	voucherServiceRequestCount, err := testContainers.Wiremock.GetVoucherServiceCountRequests()
	require.NoError(t, err)
	assert.GreaterOrEqual(t, voucherServiceRequestCount, int64(1))
	assert.NoError(t, testContainers.Wiremock.AuthServiceVerify(0))
	assert.NoError(t, testContainers.Wiremock.PriceServiceVerify(0))

	publishBatch(ctx, t, testContainers, 1)

	statusToBeCanceled := "tobecanceled"
	time := time.Now().Add(72 * time.Hour)

	testContainers.Database.VerifyDBAutoCancelToBeCanceled(
		t,
		&subscription.AutoCancel{
			Status: &statusToBeCanceled,
			CustomerPlanID: uuid.NullUUID{
				UUID:  uuid.FromStringOrNil(testdata.CustomerOrderEventValue.GetPlanId()),
				Valid: true,
			},
			CancellationDate: &time,
			Voucher:          &testdata.VoucherCode,
		},
	)

	assert.NoError(t, testContainers.Wiremock.AuthServiceVerify(1))
	assert.NoError(t, testContainers.Wiremock.PriceServiceVerify(1))

	voucherServiceRequestCount, err = testContainers.Wiremock.GetVoucherServiceCountRequests()
	require.NoError(t, err)
	assert.GreaterOrEqual(t, voucherServiceRequestCount, int64(1))

	// publish another batch with the same customer_plan_id, which will be dropped because it is already autocanceled,
	// so voucher-service, auth-service, and price-service will see no additional calls
	publishBatch(ctx, t, testContainers, 2)

	assert.NoError(t, testContainers.Wiremock.VoucherServiceVerify(voucherServiceRequestCount))
	assert.NoError(t, testContainers.Wiremock.AuthServiceVerify(1))
	assert.NoError(t, testContainers.Wiremock.PriceServiceVerify(1))
}

func publishBatch(ctx context.Context, t *testing.T, testContainers *containers.DockerTestContainers, nthBatch int) {
	t.Helper()

	// publish 3 events that will trigger order paid
	err := testContainers.Kafka.ProduceSync(ctx, testdata.DeliveryEvent).FirstErr()
	require.NoError(t, err)
	testContainers.Database.VerifyDBEvent(ctx, t, testdata.DeliveryEventValue, nthBatch)

	err = testContainers.Kafka.ProduceSync(ctx, testdata.PaymentEvent).FirstErr()
	require.NoError(t, err)
	testContainers.Database.VerifyDBEvent(ctx, t, testdata.PaymentEventValue, nthBatch)

	err = testContainers.Kafka.ProduceSync(ctx, testdata.CustomerOrderEvent).FirstErr()
	require.NoError(t, err)
	testContainers.Database.VerifyDBEvent(ctx, t, testdata.CustomerOrderEventValue, nthBatch)

}
