FROM golang:1.24.1 AS builder
WORKDIR /tmp/builder

COPY . ./

RUN make build

# ---

FROM alpine
EXPOSE 9090

COPY --from=builder /tmp/builder/dist /
COPY --from=builder /tmp/builder/cmd/credit-autocancel-service-benthos/*.yaml cmd/credit-autocancel-service-benthos/
COPY --from=builder /tmp/builder/cmd/credit-autocancel-service-benthos/streams/*.yaml cmd/credit-autocancel-service-benthos/streams/

RUN adduser -D -u 1000 hellofresh
USER 1000:1000

ENTRYPOINT ["/credit-autocancel-service-benthos"]
