#!/usr/bin/env bash

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[33m'
NC='\033[0m' # No Color

export FORMAT_OK=${GREEN}
export FORMAT_ERROR=${RED}
export FORMAT_WARNING=${YELLOW}
export FORMAT_END=${NC}

export PROJECT_NAME='credit-autocancel-service'
PROJECT_PATH="$( cd "$(dirname "$(dirname "${BASH_SOURCE[0]}" )")" >/dev/null 2>&1 && pwd )"
export PROJECT_PATH
export GOPRIVATE=github.com/hellofresh

# Config for the binaries you want to build
export  REPO="github.com/hellofresh/${PROJECT_NAME}"
export BINARY_NAME=${PROJECT_NAME}
export BINARY_SRC="${REPO}"
export DIR_OUT="${PROJECT_PATH}/out"

# Docs config
export DOCS_DIR="${PROJECT_PATH}/docs"
export VENDOR_DIR="${PROJECT_PATH}/vendor"

export SRC_DIRS=('pkg' 'internal')
export DOCKER_WORK_DIR="/${PROJECT_NAME}"

#env
export ENV_FILE="${PROJECT_PATH}/.env"
export ENV_DIST="${PROJECT_PATH}/.env.example"
