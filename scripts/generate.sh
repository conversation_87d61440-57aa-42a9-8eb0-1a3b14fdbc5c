#!/usr/bin/env bash

# shellcheck disable=SC1091

cd "$( dirname "$( dirname "${BASH_SOURCE[0]}" )")" || exit 1
source "scripts/common.sh"

MOCKGEN_COMMAND="mockgen"
MOCKGEN_PATH="${GOPATH}/bin/${MOCKGEN_COMMAND}"

# Exit early if the command is already installed
if [ -x "${MOCKGEN_PATH}" ]; then
  echo -e "${FORMAT_OK}==> mockgen is already installed on ${MOCKGEN_PATH}${FORMAT_END}"
else
  echo -e "${FORMAT_OK}==> Installing mockgen binary${FORMAT_END}"
  go get github.com/golang/mock/mockgen
fi

echo -e "${FORMAT_OK}==> Cleaning mocks before generation${FORMAT_END}"
find .  -type f -name 'mock_*.go' -delete

echo -e "${FORMAT_OK}==> Generating files${FORMAT_END}"
go generate -x ./...
