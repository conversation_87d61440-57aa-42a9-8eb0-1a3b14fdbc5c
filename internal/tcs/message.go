package tcs

import (
	"time"

	"github.com/gofrs/uuid"
)

const eventType = "tcs.event.send"
const eventName = "business-to-business-sales"
const producer = "credit-autocancel-service"

type (
	// Message is the base holder of TCS messages
	Message struct {
		Type      string    `json:"type"`
		Producer  string    `json:"producer"`
		Payload   Payload   `json:"payload"`
		Timestamp time.Time `json:"timestamp"`
	}
	// Payload holds the message payload
	Payload struct {
		AutoCancellationDate   string  `json:"auto_cancellation_date"`
		Country                string  `json:"country"`
		CustomerUUID           string  `json:"customer_uuid"`
		Event                  string  `json:"event"`
		RemainingVoucherAmount float64 `json:"remaining_voucher_amount"`
		SubscriptionID         int64   `json:"subscription_id"`
		VoucherCode            string  `json:"voucher_code"`
	}
)

// NewTCSMessage returns an encoded TCS message that can be published to TCS
func NewTCSMessage(autoCancellationDate string, country string, customerUUID uuid.UUID, remainingVoucherAmount float64, subscriptionID int64, voucherCode string) *Message {
	TCSMessage := Message{
		Type:     eventType,
		Producer: producer,
		Payload: Payload{
			AutoCancellationDate:   autoCancellationDate,
			Country:                country,
			CustomerUUID:           customerUUID.String(),
			Event:                  eventName,
			RemainingVoucherAmount: remainingVoucherAmount,
			SubscriptionID:         subscriptionID,
			VoucherCode:            voucherCode,
		},
		Timestamp: time.Now(),
	}
	return &TCSMessage
}
