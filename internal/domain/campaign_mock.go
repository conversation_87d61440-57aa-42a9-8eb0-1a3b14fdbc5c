// Code generated by MockGen. DO NOT EDIT.
// Source: campaign.go

// Package domain is a generated GoMock package.
package domain

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	campaign "github.com/hellofresh/credit-autocancel-service/internal/campaign"
	payload "github.com/hellofresh/credit-autocancel-service/internal/payload"
)

// MockCampaignsService is a mock of CampaignsService interface.
type MockCampaignsService struct {
	ctrl     *gomock.Controller
	recorder *MockCampaignsServiceMockRecorder
}

// MockCampaignsServiceMockRecorder is the mock recorder for MockCampaignsService.
type MockCampaignsServiceMockRecorder struct {
	mock *MockCampaignsService
}

// NewMockCampaignsService creates a new mock instance.
func NewMockCampaignsService(ctrl *gomock.Controller) *MockCampaignsService {
	mock := &MockCampaignsService{ctrl: ctrl}
	mock.recorder = &MockCampaignsServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCampaignsService) EXPECT() *MockCampaignsServiceMockRecorder {
	return m.recorder
}

// CreateCampaigns mocks base method.
func (m *MockCampaignsService) CreateCampaigns(ctx context.Context, createChallengesPayload payload.CampaignsPayload) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCampaigns", ctx, createChallengesPayload)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateCampaigns indicates an expected call of CreateCampaigns.
func (mr *MockCampaignsServiceMockRecorder) CreateCampaigns(ctx, createChallengesPayload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCampaigns", reflect.TypeOf((*MockCampaignsService)(nil).CreateCampaigns), ctx, createChallengesPayload)
}

// GetCampaigns mocks base method.
func (m *MockCampaignsService) GetCampaigns(ctx context.Context, country string) ([]campaign.Campaign, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCampaigns", ctx, country)
	ret0, _ := ret[0].([]campaign.Campaign)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCampaigns indicates an expected call of GetCampaigns.
func (mr *MockCampaignsServiceMockRecorder) GetCampaigns(ctx, country interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCampaigns", reflect.TypeOf((*MockCampaignsService)(nil).GetCampaigns), ctx, country)
}

// UpdateCampaign mocks base method.
func (m *MockCampaignsService) UpdateCampaign(ctx context.Context, campaignID, country string, active *bool, campaignType *string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateCampaign", ctx, campaignID, country, active, campaignType)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateCampaign indicates an expected call of UpdateCampaign.
func (mr *MockCampaignsServiceMockRecorder) UpdateCampaign(ctx, campaignID, country, active, campaignType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCampaign", reflect.TypeOf((*MockCampaignsService)(nil).UpdateCampaign), ctx, campaignID, country, active, campaignType)
}
