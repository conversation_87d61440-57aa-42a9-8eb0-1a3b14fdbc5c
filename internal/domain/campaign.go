//go:generate mockgen -source campaign.go -destination campaign_mock.go -package domain

package domain

import (
	"context"

	"github.com/hellofresh/credit-autocancel-service/internal/campaign"
	"github.com/hellofresh/credit-autocancel-service/internal/payload"
)

type (
	// CampaignsService handles requests to the campaigns endpoint
	CampaignsService interface {
		CreateCampaigns(ctx context.Context, createChallengesPayload payload.CampaignsPayload) error
		GetCampaigns(ctx context.Context, country string) ([]campaign.Campaign, error)
		UpdateCampaign(ctx context.Context, campaignID, country string, active *bool, campaignType *string) error
	}
)
