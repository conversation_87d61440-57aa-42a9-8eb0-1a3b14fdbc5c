package publisher

import (
	"context"
)

type (
	// JSONStructPublisher is a Publisher for structs that can be json marshalled.
	JSONStructPublisher struct {
		publisher *OutboxPublisher
	}

	// JSONPublisher interface to publish JSON messages.
	JSONPublisher interface {
		Publish(ctx context.Context, exchange string, routingKey string, payload interface{}) error
	}
)

// NewJSONStructPublisher creates and returns a new JSONStructPublisher
func NewJSONStructPublisher(publisher *OutboxPublisher) *JSONStructPublisher {
	return &JSONStructPublisher{publisher: publisher}
}

// Publish publishes a message to an exchange
func (j JSONStructPublisher) Publish(ctx context.Context, exchange, routingKey string, payload interface{}) error {
	// TODO: REFACTOR
	return j.publisher.Publish(ctx, nil, exchange, routingKey, payload)
}
