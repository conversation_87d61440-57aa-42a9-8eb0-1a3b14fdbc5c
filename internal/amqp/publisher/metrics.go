package publisher

import (
	"github.com/hellofresh/growth-go-kit/observability"
)

var (
	sourceTag = observability.NewTagKey("source")
	resultTag = observability.NewTagKey("result")

	messageProcessed = observability.NewInt64Metric(
		"credit_autocancel_service/publisher_message_process_total/count",
		"MessageProcessed counts the total number of messages processed by the publisher",
		observability.UnitDimensionless,
		observability.AggregationCount(),
	)
)
