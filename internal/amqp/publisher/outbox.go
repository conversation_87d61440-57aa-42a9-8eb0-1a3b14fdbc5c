package publisher

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"time"

	"github.com/jmoiron/sqlx"

	outboxClient "github.com/hellofresh/outbox-client-go"
	"github.com/hellofresh/outbox-client-go/pkg/outbox"
	sqlStorage "github.com/hellofresh/outbox-client-go/pkg/sql"
)

// OutboxPublisher wraps the emit functions from a rabbus emitasync/ok/err to satisfy the Publisher interface
type OutboxPublisher struct {
	db     *sqlx.DB
	outbox *outboxClient.Client

	kind string

	timeout time.Duration
}

// NewOutboxEmitter builds an Publisher from the publisher
func NewOutboxEmitter(db *sqlx.DB, kind string, timeout time.Duration) *OutboxPublisher {
	repo := sqlStorage.NewRepo(db.DB)
	client := outboxClient.New(repo)
	return &OutboxPublisher{
		db:      db,
		outbox:  client,
		kind:    kind,
		timeout: timeout,
	}
}

// Publish wraps the outbox client publish function
func (e *OutboxPublisher) Publish(ctx context.Context, tx *sql.Tx, exchange string, routingKey string, payload interface{}) error {
	p, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal object into the payload %w", err)
	}

	outboxMsg := outbox.Message{
		Exchange:   exchange,
		RoutingKey: routingKey,
		Body:       p,
		Properties: &outbox.Properties{Type: e.kind},
	}

	err = e.outbox.Publish(
		ctx,
		tx,
		&outboxMsg,
	)
	if err != nil {
		messageProcessed.M(ctx, 1, sourceTag.V(routingKey), resultTag.V("error"))
		return fmt.Errorf("failed to publish message %w", err)
	}

	messageProcessed.M(ctx, 1, sourceTag.V(routingKey), resultTag.V("ok"))

	return nil
}
