package app_test

import (
	"os"
	"testing"

	"github.com/kelseyhightower/envconfig"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/hellofresh/credit-autocancel-service/internal/app"
)

// TestObservabilityConfiguration tests the View and Trace config parsing.
// nolint: paralleltest // this test is not parallel because it sets env vars
func TestObservabilityConfiguration(t *testing.T) {
	setObservabilityEnv(t)

	var cfg app.Configuration
	err := envconfig.Process("", &cfg)
	require.NoError(t, err)

	assertObservabilityConfig(t, &cfg)
}

// setObservabilityEnv sets environment variables for observability testing.
// nolint:usetesting
func setObservabilityEnv(t *testing.T) {
	t.Helper()

	assert.NoError(t, os.Setenv("AMQP_DSN", "amqp://guest:guest@localhost:5672/"))
	assert.NoError(t, os.Setenv("AUTH_URL", "http://auth-service.test"))
	assert.NoError(t, os.Setenv("CLIENT_ID", "test-client-id"))
	assert.NoError(t, os.Setenv("CLIENT_SECRET", "test-client-secret"))
	assert.NoError(t, os.Setenv("COUNTRY_CODES", "DE"))
	assert.NoError(t, os.Setenv("PRICE_SERVICE_URL", "http://price-service.test"))
	assert.NoError(t, os.Setenv("VOUCHER_SERVICE_URL", "http://voucher-service.test"))
	assert.NoError(t, os.Setenv("BALANCE_SERVICE_URL", "http://balance-service.test"))
	assert.NoError(t, os.Setenv("DB_HOST", "postgres"))
	assert.NoError(t, os.Setenv("DB_PORT", "5432"))
	assert.NoError(t, os.Setenv("DB_USER", "postgres"))
	assert.NoError(t, os.Setenv("DB_PASSWORD", "postgres"))
	assert.NoError(t, os.Setenv("DB_NAME", "test_db"))

	// viewconfig
	assert.NoError(t, os.Setenv("ENABLE_PROMETHEUS", "true"))
	assert.NoError(t, os.Setenv("PROMETHEUS_NAMESPACE", "test-namespace"))
	assert.NoError(t, os.Setenv("ENABLE_EXEMPLARS", "true"))

	// traceconfig
	assert.NoError(t, os.Setenv("ENABLE_PRINT", "true"))
	assert.NoError(t, os.Setenv("OTLP_EXPORTER_HOST", "otlp-collector"))
	assert.NoError(t, os.Setenv("ENABLE_OTLP_GRPC", "true"))
	assert.NoError(t, os.Setenv("OTLP_EXPORTER_GRPC_PORT", "4317"))
	assert.NoError(t, os.Setenv("ENABLE_OTLP_HTTP", "true"))
	assert.NoError(t, os.Setenv("OTLP_EXPORTER_HTTP_PORT", "4318"))
	assert.NoError(t, os.Setenv("SAMPLE_PROBABILITY", "0.5"))
}

// assertObservabilityConfig verifies that observability configs are correctly parsed.
func assertObservabilityConfig(t *testing.T, cfg *app.Configuration) {
	t.Helper()

	assert.Equal(t, true, cfg.View.EnablePrometheus)
	assert.Equal(t, "test-namespace", cfg.View.PrometheusNamespace)
	assert.Equal(t, true, cfg.View.EnableExemplars)

	assert.Equal(t, true, cfg.Trace.EnablePrint)
	assert.Equal(t, "otlp-collector", cfg.Trace.OTLPHost)
	assert.Equal(t, true, cfg.Trace.EnableOTLPGRPC)
	assert.Equal(t, "4317", cfg.Trace.OTLPGRPCPort)
	assert.Equal(t, true, cfg.Trace.EnableOTLPHTTP)
	assert.Equal(t, "4318", cfg.Trace.OTLPHTTPPort)
	assert.Equal(t, 0.5, cfg.Trace.SamplerProbability)
}
