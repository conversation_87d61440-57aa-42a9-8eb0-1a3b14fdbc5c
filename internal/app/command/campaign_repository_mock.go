// Code generated by mockery v2.53.3. DO NOT EDIT.

package command

import (
	context "context"

	campaign "github.com/hellofresh/credit-autocancel-service/internal/campaign"

	mock "github.com/stretchr/testify/mock"
)

// campaignRepositoryMock is an autogenerated mock type for the campaignRepository type
type campaignRepositoryMock struct {
	mock.Mock
}

// GetCampaignByID provides a mock function with given fields: ctx, campaignID, country
func (_m *campaignRepositoryMock) GetCampaignByID(ctx context.Context, campaignID string, country string) (campaign.Campaign, error) {
	ret := _m.Called(ctx, campaignID, country)

	if len(ret) == 0 {
		panic("no return value specified for GetCampaignByID")
	}

	var r0 campaign.Campaign
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (campaign.Campaign, error)); ok {
		return rf(ctx, campaignID, country)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) campaign.Campaign); ok {
		r0 = rf(ctx, campaignID, country)
	} else {
		r0 = ret.Get(0).(campaign.Campaign)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, campaignID, country)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// newCampaignRepositoryMock creates a new instance of campaignRepositoryMock. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newCampaignRepositoryMock(t interface {
	mock.TestingT
	Cleanup(func())
}) *campaignRepositoryMock {
	mock := &campaignRepositoryMock{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
