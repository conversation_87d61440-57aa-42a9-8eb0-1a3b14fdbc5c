// Code generated by mockery v2.53.3. DO NOT EDIT.

package command

import (
	priceservice "github.com/hellofresh/credit-autocancel-service/internal/clients/priceservice"
	mock "github.com/stretchr/testify/mock"
)

// priceServiceMock is an autogenerated mock type for the priceService type
type priceServiceMock struct {
	mock.Mock
}

// Calculate provides a mock function with given fields: params
func (_m *priceServiceMock) Calculate(params priceservice.CalculateParams) (*priceservice.Response, error) {
	ret := _m.Called(params)

	if len(ret) == 0 {
		panic("no return value specified for Calculate")
	}

	var r0 *priceservice.Response
	var r1 error
	if rf, ok := ret.Get(0).(func(priceservice.CalculateParams) (*priceservice.Response, error)); ok {
		return rf(params)
	}
	if rf, ok := ret.Get(0).(func(priceservice.CalculateParams) *priceservice.Response); ok {
		r0 = rf(params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*priceservice.Response)
		}
	}

	if rf, ok := ret.Get(1).(func(priceservice.CalculateParams) error); ok {
		r1 = rf(params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// newPriceServiceMock creates a new instance of priceServiceMock. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newPriceServiceMock(t interface {
	mock.TestingT
	Cleanup(func())
}) *priceServiceMock {
	mock := &priceServiceMock{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
