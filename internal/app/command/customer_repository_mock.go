// Code generated by mockery v2.43.1. DO NOT EDIT.

package command

import (
	context "context"

	uuid "github.com/gofrs/uuid"
	mock "github.com/stretchr/testify/mock"
)

// customerRepositoryMock is an autogenerated mock type for the customerRepository type
type customerRepositoryMock struct {
	mock.Mock
}

// FindCustomerID provides a mock function with given fields: ctx, customerUUID, country
func (_m *customerRepositoryMock) FindCustomerID(ctx context.Context, customerUUID uuid.NullUUID, country string) (int64, error) {
	ret := _m.Called(ctx, customerUUID, country)

	if len(ret) == 0 {
		panic("no return value specified for FindCustomerID")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.NullUUID, string) (int64, error)); ok {
		return rf(ctx, customerUUID, country)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.NullUUID, string) int64); ok {
		r0 = rf(ctx, customerUUID, country)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.NullUUID, string) error); ok {
		r1 = rf(ctx, customerUUID, country)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// newCustomerRepositoryMock creates a new instance of customerRepositoryMock. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newCustomerRepositoryMock(t interface {
	mock.TestingT
	Cleanup(func())
}) *customerRepositoryMock {
	mock := &customerRepositoryMock{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
