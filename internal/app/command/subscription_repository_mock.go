// Code generated by mockery v2.53.3. DO NOT EDIT.

package command

import (
	context "context"

	subscription "github.com/hellofresh/credit-autocancel-service/internal/subscription"
	mock "github.com/stretchr/testify/mock"

	uuid "github.com/gofrs/uuid"
)

// subscriptionRepositoryMock is an autogenerated mock type for the subscriptionRepository type
type subscriptionRepositoryMock struct {
	mock.Mock
}

// FindCanceled provides a mock function with given fields: ctx, customerPlanID
func (_m *subscriptionRepositoryMock) FindCanceled(ctx context.Context, customerPlanID uuid.NullUUID) error {
	ret := _m.Called(ctx, customerPlanID)

	if len(ret) == 0 {
		panic("no return value specified for FindCanceled")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.NullUUID) error); ok {
		r0 = rf(ctx, customerPlanID)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// FindRunning provides a mock function with given fields: ctx, customerPlanID
func (_m *subscriptionRepositoryMock) FindRunning(ctx context.Context, customerPlanID uuid.NullUUID) (*subscription.AutoCancel, error) {
	ret := _m.Called(ctx, customerPlanID)

	if len(ret) == 0 {
		panic("no return value specified for FindRunning")
	}

	var r0 *subscription.AutoCancel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.NullUUID) (*subscription.AutoCancel, error)); ok {
		return rf(ctx, customerPlanID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.NullUUID) *subscription.AutoCancel); ok {
		r0 = rf(ctx, customerPlanID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*subscription.AutoCancel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.NullUUID) error); ok {
		r1 = rf(ctx, customerPlanID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetRunningSubscription provides a mock function with given fields: ctx, autoCancel
func (_m *subscriptionRepositoryMock) SetRunningSubscription(ctx context.Context, autoCancel *subscription.AutoCancel) error {
	ret := _m.Called(ctx, autoCancel)

	if len(ret) == 0 {
		panic("no return value specified for SetRunningSubscription")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *subscription.AutoCancel) error); ok {
		r0 = rf(ctx, autoCancel)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SetSubscriptionForCancellation provides a mock function with given fields: ctx, autoCancel
func (_m *subscriptionRepositoryMock) SetSubscriptionForCancellation(ctx context.Context, autoCancel *subscription.AutoCancel) error {
	ret := _m.Called(ctx, autoCancel)

	if len(ret) == 0 {
		panic("no return value specified for SetSubscriptionForCancellation")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *subscription.AutoCancel) error); ok {
		r0 = rf(ctx, autoCancel)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// newSubscriptionRepositoryMock creates a new instance of subscriptionRepositoryMock. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newSubscriptionRepositoryMock(t interface {
	mock.TestingT
	Cleanup(func())
}) *subscriptionRepositoryMock {
	mock := &subscriptionRepositoryMock{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
