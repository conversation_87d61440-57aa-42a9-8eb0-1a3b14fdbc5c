// Code generated by mockery v2.53.4. DO NOT EDIT.

package command

import (
	context "context"

	balanceservice "github.com/hellofresh/credit-autocancel-service/internal/clients/balanceservice"

	mock "github.com/stretchr/testify/mock"

	uuid "github.com/gofrs/uuid"
)

// balanceServiceMock is an autogenerated mock type for the balanceService type
type balanceServiceMock struct {
	mock.Mock
}

// GetBalance provides a mock function with given fields: ctx, customerUUID
func (_m *balanceServiceMock) GetBalance(ctx context.Context, customerUUID uuid.UUID) (*balanceservice.BalanceResponse, error) {
	ret := _m.Called(ctx, customerUUID)

	if len(ret) == 0 {
		panic("no return value specified for GetBalance")
	}

	var r0 *balanceservice.BalanceResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) (*balanceservice.BalanceResponse, error)); ok {
		return rf(ctx, customerUUID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, uuid.UUID) *balanceservice.BalanceResponse); ok {
		r0 = rf(ctx, customerUUID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*balanceservice.BalanceResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, uuid.UUID) error); ok {
		r1 = rf(ctx, customerUUID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// newBalanceServiceMock creates a new instance of balanceServiceMock. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newBalanceServiceMock(t interface {
	mock.TestingT
	Cleanup(func())
}) *balanceServiceMock {
	mock := &balanceServiceMock{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
