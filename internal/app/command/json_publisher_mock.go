// Code generated by mockery v2.42.1. DO NOT EDIT.

package command

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// jsonPublisherMock is an autogenerated mock type for the jsonPublisher type
type jsonPublisherMock struct {
	mock.Mock
}

// Publish provides a mock function with given fields: ctx, exchange, routingKey, payload
func (_m *jsonPublisherMock) Publish(ctx context.Context, exchange string, routingKey string, payload interface{}) error {
	ret := _m.Called(ctx, exchange, routingKey, payload)

	if len(ret) == 0 {
		panic("no return value specified for Publish")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, interface{}) error); ok {
		r0 = rf(ctx, exchange, routingKey, payload)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// newJsonPublisherMock creates a new instance of jsonPublisherMock. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newJsonPublisherMock(t interface {
	mock.TestingT
	Cleanup(func())
}) *jsonPublisherMock {
	mock := &jsonPublisherMock{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
