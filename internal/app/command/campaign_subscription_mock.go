// Code generated by mockery v2.43.1. DO NOT EDIT.

package command

import (
	context "context"

	mock "github.com/stretchr/testify/mock"
)

// campaignSubscriptionMock is an autogenerated mock type for the campaignSubscription type
type campaignSubscriptionMock struct {
	mock.Mock
}

// Track provides a mock function with given fields: ctx, campaignID, subscriptionID, voucherCode
func (_m *campaignSubscriptionMock) Track(ctx context.Context, campaignID string, subscriptionID int64, voucherCode string) error {
	ret := _m.Called(ctx, campaignID, subscriptionID, voucherCode)

	if len(ret) == 0 {
		panic("no return value specified for Track")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, int64, string) error); ok {
		r0 = rf(ctx, campaignID, subscriptionID, voucherCode)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// newCampaignSubscriptionMock creates a new instance of campaignSubscriptionMock. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newCampaignSubscriptionMock(t interface {
	mock.TestingT
	Cleanup(func())
}) *campaignSubscriptionMock {
	mock := &campaignSubscriptionMock{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
