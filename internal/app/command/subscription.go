// nolint:revive
package command

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/growth-go-kit/logger"
	"github.com/hellofresh/growth-go-kit/observability"
	"go.uber.org/zap"

	"github.com/hellofresh/credit-autocancel-service/internal/campaign"
	"github.com/hellofresh/credit-autocancel-service/internal/campaignsubscription"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/balanceservice"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/priceservice"
	"github.com/hellofresh/credit-autocancel-service/internal/clients/voucherservice"
	"github.com/hellofresh/credit-autocancel-service/internal/customer"
	"github.com/hellofresh/credit-autocancel-service/internal/payload"
	"github.com/hellofresh/credit-autocancel-service/internal/subscription"
	"github.com/hellofresh/credit-autocancel-service/internal/tcs"
	"github.com/hellofresh/credit-autocancel-service/pkg/nullable"
)

const cancellationDelay = 72 * time.Hour

// PriceCalculationResult holds the result of price calculation
type PriceCalculationResult struct {
	GrandTotal              float64
	RemainingDiscountAmount float64
	IsFullyCoveredByCredits bool
}

// BenthosHandlerParams holds all the parameters required to create a BenthosHandler
type BenthosHandlerParams struct {
	VoucherClient          voucherservice.VoucherService
	CampaignRepository     campaign.Repository
	SubscriptionRepository subscription.Repository
	CustomerRepository     customerRepository
	CampaignSubscription   campaignsubscription.CampaignSubscription
	Logger                 *zap.Logger
	Authenticator          authenticator
	PriceService           priceService
	BalanceService         balanceService
	JSONPublisher          jsonPublisher
}

// Validate checks all parameters and returns a concatenated error message for any that are nil
func (p *BenthosHandlerParams) Validate() error {
	var validationErrors []string

	if p.VoucherClient == nil {
		validationErrors = append(validationErrors, "voucher client is required")
	}
	if p.CampaignRepository == nil {
		validationErrors = append(validationErrors, "campaign repository is required")
	}
	if p.SubscriptionRepository == nil {
		validationErrors = append(validationErrors, "subscription repository is required")
	}
	if p.CustomerRepository == nil {
		validationErrors = append(validationErrors, "customer repository is required")
	}
	if p.CampaignSubscription == nil {
		validationErrors = append(validationErrors, "campaign subscription is required")
	}
	if p.Logger == nil {
		validationErrors = append(validationErrors, "logger is required")
	}
	if p.Authenticator == nil {
		validationErrors = append(validationErrors, "authenticator is required")
	}
	if p.PriceService == nil {
		validationErrors = append(validationErrors, "price service is required")
	}
	if p.BalanceService == nil {
		validationErrors = append(validationErrors, "balance service is required")
	}
	if p.JSONPublisher == nil {
		validationErrors = append(validationErrors, "json publisher is required")
	}

	if len(validationErrors) > 0 {
		return fmt.Errorf("validation failed: %s", strings.Join(validationErrors, "; "))
	}

	return nil
}

// BenthosHandler holds the handler dependencies
// TODO: all of these should be in the internal/domain/ folder and have tests for each one
type BenthosHandler struct {
	campaignRepository     campaignRepository
	campaignSubscription   campaignSubscription
	logger                 *zap.Logger // TODO: remove this and replace logs with errors
	subscriptionRepository subscriptionRepository
	customerRepository     customerRepository
	voucherClient          voucherService
	authenticator          authenticator
	priceService           priceService
	balanceService         balanceService
	jsonPublisher          jsonPublisher
}

// NewBenthosHandler constructs a Benthos handler
func NewBenthosHandler(params *BenthosHandlerParams) (*BenthosHandler, error) {
	if err := params.Validate(); err != nil {
		return nil, err
	}

	return &BenthosHandler{
		campaignRepository:     params.CampaignRepository,
		campaignSubscription:   params.CampaignSubscription,
		logger:                 params.Logger,
		subscriptionRepository: params.SubscriptionRepository,
		customerRepository:     params.CustomerRepository,
		voucherClient:          params.VoucherClient,
		authenticator:          params.Authenticator,
		priceService:           params.PriceService,
		balanceService:         params.BalanceService,
		jsonPublisher:          params.JSONPublisher,
	}, nil
}

//go:generate mockery --name priceService --structname priceServiceMock --output . --outpkg command --filename price_service_mock.go --inpackage
type priceService interface {
	Calculate(params priceservice.CalculateParams) (*priceservice.Response, error)
}

//go:generate mockery --name balanceService --structname balanceServiceMock --output . --outpkg command --filename balance_service_mock.go --inpackage
type balanceService interface {
	GetBalance(ctx context.Context, customerUUID uuid.UUID) (*balanceservice.BalanceResponse, error)
}

//go:generate mockery --name authenticator --structname authenticatorMock --output . --outpkg command --filename authenticator_mock.go --inpackage
type authenticator interface {
	Token(ctx context.Context) (string, error)
}

//go:generate mockery --name jsonPublisher --structname jsonPublisherMock --output . --outpkg command --filename json_publisher_mock.go --inpackage
type jsonPublisher interface {
	Publish(ctx context.Context, exchange string, routingKey string, payload interface{}) error
}

//go:generate mockery --name voucherService --structname voucherServiceMock --output . --outpkg command --filename voucher_service_mock.go --inpackage
type voucherService interface {
	GetDetails(ctx context.Context, voucher string, country string) (*voucherservice.VoucherDetails, error)
}

//go:generate mockery --name campaignRepository --structname campaignRepositoryMock --output . --outpkg command --filename campaign_repository_mock.go --inpackage
type campaignRepository interface {
	GetCampaignByID(ctx context.Context, campaignID string, country string) (campaign.Campaign, error)
}

//go:generate mockery --name subscriptionRepository --structname subscriptionRepositoryMock --output . --outpkg command --filename subscription_repository_mock.go --inpackage
type subscriptionRepository interface {
	FindCanceled(ctx context.Context, customerPlanID uuid.NullUUID) error
	FindRunning(ctx context.Context, customerPlanID uuid.NullUUID) (*subscription.AutoCancel, error)
	SetSubscriptionForCancellation(ctx context.Context, autoCancel *subscription.AutoCancel) error
	SetRunningSubscription(ctx context.Context, autoCancel *subscription.AutoCancel) error
}

//go:generate mockery --name customerRepository --structname customerRepositoryMock --output . --outpkg command --filename customer_repository_mock.go --inpackage
type customerRepository interface {
	FindCustomerID(ctx context.Context, customerUUID uuid.NullUUID, country string) (int64, error)
}

//go:generate mockery --name campaignSubscription --structname campaignSubscriptionMock --output . --outpkg command --filename campaign_subscription_mock.go --inpackage
type campaignSubscription interface {
	Track(ctx context.Context, campaignID string, subscriptionID int64, voucherCode string) error
}

// checkCampaignType checks if the campaign type is cancel once
// and if the subscription is already cancelled
func (h *BenthosHandler) checkCampaignType(ctx context.Context, campaignID string, autoCancel *subscription.AutoCancel) error {
	ctx, span := observability.StartSpan(ctx, "command.BenthosHandler.checkCampaignType")
	defer span.End()

	logger.From(ctx).With(
		zap.String("campaign_id", campaignID),
		zap.String("country", autoCancel.Country),
		zap.String("customer_plan_id", autoCancel.CustomerPlanID.UUID.String()),
	).Info("checking campaign type")

	campaignFound, err := h.campaignRepository.GetCampaignByID(ctx, campaignID, autoCancel.Country)
	if err != nil {
		err = fmt.Errorf("failed to look up campaign in database: %w", err)
		if errors.Is(err, campaign.ErrVoucherNotPartOfCampaign) {
			return err
		}
		return span.Fail(err)
	}
	if campaignFound.CampaignType == payload.CampaignTypeCancelOnce {
		// check if there is a cancelled subscription for this customer
		err = h.subscriptionRepository.FindCanceled(ctx, autoCancel.CustomerPlanID)
		if err != nil {
			if errors.Is(err, subscription.ErrAlreadyCancelled) {
				return fmt.Errorf("found cancelled subscription: %w with campaign type: %s", err, campaignFound.CampaignType)
			}
			return span.Fail(fmt.Errorf("failed to find cancelled subscription: %w", err))
		}
	}

	return nil
}

// getVoucherDetails returns *voucherservice.VoucherDetails if voucher is found and is part of a campaign
func (h *BenthosHandler) getVoucherDetails(ctx context.Context, autoCancel *subscription.AutoCancel) (*voucherservice.VoucherDetails, error) {
	ctx, span := observability.StartSpan(ctx, "command.BenthosHandler.getVoucherDetails")
	defer span.End()

	voucherDetails, err := h.voucherClient.GetDetails(ctx, *autoCancel.Voucher, strings.ToUpper(autoCancel.Country))
	if err != nil {
		err = fmt.Errorf("failed to get details from voucher-validation-service client: %w", err)
		if errors.Is(err, voucherservice.ErrNotFound) {
			return nil, err
		}
		return nil, span.Fail(err)
	}

	err = h.checkCampaignType(ctx, voucherDetails.Campaign.CampaignID, autoCancel)
	if err != nil {
		return nil, err
	}

	return voucherDetails, nil
}

// setSubscriptionRunning sets the subscription to running
func (h *BenthosHandler) setSubscriptionRunning(ctx context.Context, autoCancel *subscription.AutoCancel, trigger string) error {
	ctx, span := observability.StartSpan(ctx, "command.BenthosHandler.setSubscriptionRunning")
	defer span.End()

	logger.From(ctx).With(
		zap.String("customer_plan_id", autoCancel.CustomerPlanID.UUID.String()),
		zap.String("voucher", nullable.ToEmptyString(autoCancel.Voucher)),
		zap.String("trigger", trigger),
	).Info("setting subscription to running")

	err := h.subscriptionRepository.SetRunningSubscription(ctx, autoCancel)
	if err != nil {
		return span.Fail(fmt.Errorf("failed to set a running subscription: %w", err))
	}

	return err
}

// calculatePrice calculates the price for a subscription and returns the calculation result
func (h *BenthosHandler) calculatePrice(ctx context.Context, autoCancel *subscription.AutoCancel) (*PriceCalculationResult, error) {
	ctx, span := observability.StartSpan(ctx, "command.BenthosHandler.calculatePrice")
	defer span.End()

	priceServiceToken, err := h.authenticator.Token(ctx)
	if err != nil {
		return nil, span.Fail(fmt.Errorf("failed to get auth token: %w", err))
	}

	priceCalculation, err := h.priceService.Calculate(priceservice.CalculateParams{
		Ctx:            ctx,
		AuthHeader:     priceServiceToken,
		IsFirstOrder:   autoCancel.IsSubscriptionInitialOrder,
		SKUs:           autoCancel.Products,
		VoucherCode:    nullable.ToEmptyString(autoCancel.Voucher),
		CustomerID:     autoCancel.CustomerID,
		CustomerUUID:   autoCancel.CustomerUUID.UUID,
		PlanID:         autoCancel.CustomerPlanID.UUID,
		SubscriptionID: *autoCancel.SubscriptionID,
		Country:        autoCancel.Country,
		Postcode:       *autoCancel.Postcode,
	})
	if err != nil {
		err = fmt.Errorf("failed to calculate price from price-service client: %w", err)
		var calculateErrors priceservice.CalculateErrors
		if errors.As(err, &calculateErrors) && calculateErrors.IsAddonError() {
			return nil, err
		}
		return nil, span.Fail(err)
	}

	// Get customer balance if grand total is greater than 0
	var finalTotal float64 = priceCalculation.GrandTotal //nolint:staticcheck
	var customerBalance float64 = 0
	var isFullyCoveredByCredits bool

	if priceCalculation.GrandTotal > 0 {
		balanceResponse, err := h.balanceService.GetBalance(ctx, autoCancel.CustomerUUID.UUID)
		if err != nil {
			// If balance service is unavailable or customer has no balance, continue with original grand total
			if !errors.Is(err, balanceservice.ErrNotFound) && !errors.Is(err, balanceservice.ErrNoBalance) {
				logger.From(ctx).Warn("failed to get customer balance, proceeding without balance check",
					zap.Error(err),
					zap.String("customer_uuid", autoCancel.CustomerUUID.UUID.String()))
			}
		} else {
			// Convert balance from cents to currency units (assuming balance is in cents like amount field)
			customerBalance = float64(balanceResponse.Amount) / 100.0
			finalTotal = priceCalculation.GrandTotal - customerBalance

			// If final total after balance is <= 0, order is fully covered
			if finalTotal <= 0 {
				finalTotal = 0
				isFullyCoveredByCredits = true
			}
		}
	} else {
		// If grand total is already 0, it's fully covered by existing credits/discounts
		isFullyCoveredByCredits = true
	}

	// log the successful price calculation
	logger.From(ctx).With(
		zap.String("customer_plan_id", autoCancel.CustomerPlanID.UUID.String()),
		zap.String("customer_uuid", autoCancel.CustomerUUID.UUID.String()),
		zap.Int64("customer_id", autoCancel.CustomerID),
		zap.Int64("subscription_id", *autoCancel.SubscriptionID),
		zap.String("voucher", nullable.ToEmptyString(autoCancel.Voucher)),
		zap.String("country", autoCancel.Country),
		zap.String("postcode", nullable.ToEmptyString(autoCancel.Postcode)),
		zap.Bool("is_first_order", autoCancel.IsSubscriptionInitialOrder),
		zap.String("sku", func() string {
			if len(autoCancel.Products) > 0 {
				return autoCancel.Products[0]
			}
			return "unknown"
		}()),
		zap.Float64("original_grand_total", priceCalculation.GrandTotal),
		zap.Float64("customer_balance", customerBalance),
		zap.Float64("final_total_after_balance", finalTotal),
		zap.Float64("remaining_discount_amount", priceCalculation.RemainingDiscountAmount),
		zap.Bool("is_fully_covered_by_credits", isFullyCoveredByCredits),
	).Info("successfully calculated price with balance check")

	result := &PriceCalculationResult{
		GrandTotal:              finalTotal,
		RemainingDiscountAmount: priceCalculation.RemainingDiscountAmount,
		IsFullyCoveredByCredits: isFullyCoveredByCredits,
	}

	// Update the autoCancel with remaining voucher amount if applicable
	if priceCalculation.RemainingDiscountAmount > 0 {
		autoCancel.RemainingVoucherAmount = &priceCalculation.RemainingDiscountAmount
	}

	return result, nil
}

// publishTCS publishes the autoCancel to the us.transactional.event.send rabbitmq routing key
func (h *BenthosHandler) publishTCS(ctx context.Context, autoCancel *subscription.AutoCancel) error {
	ctx, span := observability.StartSpan(ctx, "command.BenthosHandler.publishTCS")
	defer span.End()

	routingKey := fmt.Sprintf("%s.transactional.event.send", strings.ToLower(autoCancel.Country))

	tcsMessage := tcs.NewTCSMessage(autoCancel.CreatedAt.String(), autoCancel.Country, autoCancel.CustomerUUID.UUID, nullable.ToZeroFloat64(autoCancel.RemainingVoucherAmount), *autoCancel.SubscriptionID, *autoCancel.Voucher)

	err := h.jsonPublisher.Publish(ctx, "crm", routingKey, tcsMessage)
	if err != nil {
		return span.Fail(fmt.Errorf("failed to publish tcs message: %w", err))
	}

	return nil
}

// HandleOrderPaid is responsible for processing autoCancel from the order-paid streams.
func (h *BenthosHandler) HandleOrderPaid(ctx context.Context, autoCancel *subscription.AutoCancel) error {
	ctx, span := observability.StartSpan(ctx, "command.BenthosHandler.HandleOrderPaid")
	defer span.End()

	// check if the subscription is already running(enrolled)
	autoCancelFound, err := h.subscriptionRepository.FindRunning(ctx, autoCancel.CustomerPlanID)

	switch {
	case errors.Is(err, subscription.ErrRunningSubscriptionNotFound):
		// no running subscription found, we can proceed with voucher check
		voucherDetails, err := h.getVoucherDetails(ctx, autoCancel)
		if err != nil {
			return fmt.Errorf("failed to get voucher details for autocancel: %w", err)
		}

		err = h.campaignSubscription.Track(ctx, voucherDetails.Campaign.CampaignID, *autoCancel.SubscriptionID, *autoCancel.Voucher)
		if err != nil {
			return span.Fail(fmt.Errorf("failed to insert subscription id with campaign id and voucher: %w", err))
		}
	case err != nil:
		return fmt.Errorf("failed to find running subscription: %w", err)
	default:
		// we need to overwrite the incoming voucher code with the one from the database
		autoCancel.Voucher = autoCancelFound.Voucher
	}

	customerID, err := h.customerRepository.FindCustomerID(ctx, autoCancel.CustomerUUID, autoCancel.Country)
	if err != nil {
		if errors.Is(err, customer.ErrCustomerNotFound) {
			return err
		}
		return span.Fail(fmt.Errorf("failed to find customer id: %w", err))
	}
	autoCancel.CustomerID = customerID

	priceResult, err := h.calculatePrice(ctx, autoCancel)
	if err != nil {
		return fmt.Errorf("failed to calculate price for autocancel: %w", err)
	}

	// if the order is fully covered by credits, set subscription to running and return
	if priceResult.IsFullyCoveredByCredits {
		err = h.setSubscriptionRunning(ctx, autoCancel, "order-paid")
		if err != nil {
			return fmt.Errorf("failed to set subscription running: %w", err)
		}
		logger.From(ctx).Warn("order is fully covered with credits, not setting for cancellation")
		return nil
	}

	// set the time for the subscription to be cancelled
	cancellationDate := time.Now().Add(cancellationDelay)
	autoCancel.CancellationDate = &cancellationDate

	err = h.subscriptionRepository.SetSubscriptionForCancellation(ctx, autoCancel)
	if err != nil {
		return fmt.Errorf("failed to set subscription for cancellation: %w", err)
	}

	err = h.publishTCS(ctx, autoCancel)
	if err != nil {
		return fmt.Errorf("failed to publish autocacnel event to tcs: %w", err)
	}

	return nil
}

// HandleBenefitAttached is responsible for processing autoCancel from the benefit-attached streams.
func (h *BenthosHandler) HandleBenefitAttached(ctx context.Context, autoCancel *subscription.AutoCancel) error {
	ctx, span := observability.StartSpan(ctx, "command.BenthosHandler.HandleBenefitAttached")
	defer span.End()

	_, err := h.getVoucherDetails(ctx, autoCancel)
	if err != nil {
		return fmt.Errorf("failed to get voucher details for autocancel: %w", err)
	}

	err = h.setSubscriptionRunning(ctx, autoCancel, "benefit-attached")
	if err != nil {
		return fmt.Errorf("failed to set subscription running: %w", err)
	}

	return nil
}
