// Code generated by mockery v2.43.1. DO NOT EDIT.

package command

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	voucherservice "github.com/hellofresh/credit-autocancel-service/internal/clients/voucherservice"
)

// voucherServiceMock is an autogenerated mock type for the voucherService type
type voucherServiceMock struct {
	mock.Mock
}

// GetDetails provides a mock function with given fields: ctx, voucher, country
func (_m *voucherServiceMock) GetDetails(ctx context.Context, voucher string, country string) (*voucherservice.VoucherDetails, error) {
	ret := _m.Called(ctx, voucher, country)

	if len(ret) == 0 {
		panic("no return value specified for GetDetails")
	}

	var r0 *voucherservice.VoucherDetails
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*voucherservice.VoucherDetails, error)); ok {
		return rf(ctx, voucher, country)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *voucherservice.VoucherDetails); ok {
		r0 = rf(ctx, voucher, country)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*voucherservice.VoucherDetails)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, voucher, country)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// newVoucherServiceMock creates a new instance of voucherServiceMock. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func newVoucherServiceMock(t interface {
	mock.TestingT
	Cleanup(func())
}) *voucherServiceMock {
	mock := &voucherServiceMock{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
