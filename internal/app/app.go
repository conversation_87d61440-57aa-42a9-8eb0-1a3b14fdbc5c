package app

import (
	"github.com/hellofresh/growth-go-kit/observability"

	"github.com/hellofresh/credit-autocancel-service/internal/database"
)

// Configuration holds all configuration values required for the app
type Configuration struct {
	AMQPDSN           string   `envconfig:"AMQP_DSN" required:"true"`
	AuthURL           string   `envconfig:"AUTH_URL" required:"true"`
	AuthClientID      string   `envconfig:"CLIENT_ID" required:"true"`
	AuthClientSecret  string   `envconfig:"CLIENT_SECRET" required:"true"`
	CountryCodes      []string `envconfig:"COUNTRY_CODES" required:"true"`
	PriceServiceURL   string   `envconfig:"PRICE_SERVICE_URL" required:"true"`
	VoucherServiceURL string   `envconfig:"VOUCHER_SERVICE_URL" required:"true"`
	BalanceServiceURL string   `envconfig:"BALANCE_SERVICE_URL" required:"true"`
	AppEnv            string   `envconfig:"APP_ENV" default:"development"`

	Database database.Config
	View     observability.ViewConfig
	Trace    observability.TraceConfig
}
