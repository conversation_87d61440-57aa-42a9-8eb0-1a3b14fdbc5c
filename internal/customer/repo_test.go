package customer

import (
	"context"
	"database/sql"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/gofrs/uuid"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/hellofresh/credit-autocancel-service/internal/database"
	"github.com/jmoiron/sqlx"
)

func TestRepo_FindCustomerID(t *testing.T) {
	t.Parallel()

	ctx := context.Background()

	customerUUID := uuid.NullUUID{
		UUID:  uuid.FromStringOrNil("44887039-c04f-4687-84a5-3b88ab0adcee"),
		Valid: true,
	}
	businessUnit := "us"

	type args struct {
		ctx          context.Context
		customerUUID uuid.UUID
		businessUnit string
		expected     int64
		err          error
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
		setup   func(mock sqlmock.Sqlmock)
	}{
		{
			name: "QueryRowContext fails",
			args: args{
				ctx:          ctx,
				customerUUID: uuid.UUID{},
				businessUnit: businessUnit,
				expected:     0,
				err:          errors.New("queryrowcontext failed"),
			},
			wantErr: true,
		},
		{
			name: "QueryRowContext no rows",
			args: args{
				ctx:          ctx,
				customerUUID: customerUUID.UUID,
				businessUnit: businessUnit,
				expected:     0,
				err:          sql.ErrNoRows,
			},
			wantErr: true,
		},
		{
			name: "QueryRowContext success",
			args: args{
				ctx:          ctx,
				customerUUID: customerUUID.UUID,
				businessUnit: businessUnit,
				expected:     1,
				err:          nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			db, mock, err := sqlmock.New()
			if err != nil {
				t.Error(err)
			}
			mockDb := database.DB{
				DB: sqlx.NewDb(db, "sqlmock"),
			}

			if tt.setup != nil {
				tt.setup(mock)
			}

			mock.ExpectQuery(`SELECT
				customer_id
			FROM
				events_customer_v1beta2`).
				WithArgs(customerUUID, businessUnit).
				WillReturnRows(sqlmock.NewRows([]string{
					"customer_id",
				}).
					AddRow(1)).WillReturnError(tt.args.err)

			if tt.args.err != nil {
				mock.ExpectRollback()
			} else {
				mock.ExpectCommit()
			}

			r := NewRepo(mockDb.DB)
			res, err := r.FindCustomerID(tt.args.ctx, customerUUID, businessUnit)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindCustomerID() error = %v, wantErr %v", err, tt.wantErr)
			}

			assert.Equal(t, tt.args.expected, res)
		})
	}
}
