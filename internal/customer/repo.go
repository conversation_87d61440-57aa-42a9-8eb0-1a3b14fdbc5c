// Package: customer contains the repository of customer.
package customer

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/jmoiron/sqlx"
)

// ErrCustomerNotFound indicates the customer was not found in the database
var ErrCustomerNotFound = errors.New("customer not found")

//go:generate mockgen -source repo.go -destination customer_mock.go -package customer

// Repo repository of customer
type Repo struct {
	db *sqlx.DB
}

// NewRepo initializes repo of customer
func NewRepo(db *sqlx.DB) *Repo {
	return &Repo{db: db}
}

// FindCustomerID finds the customer ID for a given customer UUID and business unit.
func (r *Repo) FindCustomerID(ctx context.Context, customerUUID uuid.NullUUID, country string) (int64, error) {
	ctx, span := observability.StartSpan(ctx, "repo.customer.FindCustomerID")
	defer span.End()

	var customerID string

	query := `
		SELECT
			customer_id
		FROM
			events_customer_v1beta2
		WHERE
			customer_uuid = $1 AND
			business_unit = $2
		ORDER BY CAST(metadata->>'kafka_offset' AS bigint) DESC
		LIMIT 1;
	`

	err := r.db.QueryRowContext(ctx, query, customerUUID, strings.ToLower(country)).Scan(&customerID)
	if errors.Is(err, sql.ErrNoRows) {
		return 0, ErrCustomerNotFound
	}

	if err != nil {
		return 0, span.Fail(fmt.Errorf("could not execute query: %w", err))
	}

	customerIDInt, err := strconv.ParseInt(customerID, 10, 64)
	if err != nil {
		return 0, span.Fail(fmt.Errorf("could not convert customer ID to int: %w", err))
	}

	return customerIDInt, nil
}
