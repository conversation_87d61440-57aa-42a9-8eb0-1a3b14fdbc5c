package subscription

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/jmoiron/sqlx"
)

var (
	// ErrAlreadyCancelled indicates the subscription is already cancelled, so should not be set for cancellation
	ErrAlreadyCancelled = errors.New("subscription is already cancelled")
	// ErrRunningSubscriptionNotFound indicates that a subscription is not found in the running status
	ErrRunningSubscriptionNotFound = errors.New("running subscription not found")
)

const (
	// statusCancelled indicates that a subscription is cancelled
	statusCancelled = "cancelled"
	// statusToBeCanceled indicates that a subscription is set to be cancelled
	statusToBeCanceled = "tobecanceled"
	// statusRunning indicates that a subscription is active
	statusRunning = "running"

	// sqlUpdateAutoCancelStatus updates the status of a subscription to cancelled
	sqlUpdateAutoCancelStatus = `
		UPDATE autocancel SET status = $1 WHERE customer_plan_id = $2;
	`

	// sqlFindAutoCancel finds a subscription with a specific status
	sqlFindAutoCancel = `
		SELECT
			status,
			created_at,
			cancellation_date,
			customer_plan_id,
			voucher_code
		FROM
			autocancel
		WHERE
			customer_plan_id = $1 AND
			status = $2;
	`

	// sqlAllSetForCancellation returns all subscriptions that are set to be cancelled
	sqlAllSetForCancellation = `
		SELECT
			status,
			customer_plan_id
		FROM
			autocancel
		WHERE
			status = $1 AND
			cancellation_date < NOW();
	`
)

var (
	// sqlUpsertAutoCancelToBeCanceled upserts a subscription with tobecanceled status
	sqlUpsertAutoCancelToBeCanceled = fmt.Sprintf(`
		INSERT INTO autocancel (
			status,
			cancellation_date,
			customer_plan_id,
			voucher_code
		)
		VALUES (
			'%s', $1, $2, $3
		)
		ON CONFLICT ON CONSTRAINT autocancel_customer_plan_id_voucher_key DO UPDATE
		SET status = '%s',
			cancellation_date = $1
	`, statusToBeCanceled, statusToBeCanceled)

	// sqlUpsertAutoCancelRunning upserts a subscription with running status
	sqlUpsertAutoCancelRunning = fmt.Sprintf(`
		INSERT INTO autocancel (
			status,
			customer_plan_id,
			voucher_code
		)
		VALUES (
			'%s', $1, $2
		)
		ON CONFLICT ON CONSTRAINT autocancel_customer_plan_id_voucher_key DO NOTHING
	`, statusRunning)
)

//go:generate mockgen -source repo.go -destination repo_mock.go -package subscription

// Repo repository of subscriptions
type Repo struct {
	db        *sqlx.DB
	publisher Publisher
}

// Publisher defines method for publishing events
type Publisher interface {
	Publish(ctx context.Context, tx *sql.Tx, exchange, routingKey string, payload interface{}) error
}

// Repository defines method to prepare a subscription for cancellation
type Repository interface {
	FindCanceled(ctx context.Context, customerPlanID uuid.NullUUID) error
	FindRunning(ctx context.Context, customerPlanID uuid.NullUUID) (*AutoCancel, error)
	SetSubscriptionForCancellation(ctx context.Context, autoCancel *AutoCancel) error
	SetRunningSubscription(ctx context.Context, autoCancel *AutoCancel) error
}

// NewRepo initializes repo of subscriptions
func NewRepo(db *sqlx.DB, publisher Publisher) *Repo {
	return &Repo{db: db, publisher: publisher}
}

// SetAsCancelled updates status of a subscription to cancelled
func (r *Repo) SetAsCancelled(ctx context.Context, customerPlanID uuid.NullUUID) error {
	ctx, span := observability.StartSpan(ctx, "repo.subscription.SetAsCancelled")
	defer span.End()

	result, err := r.db.ExecContext(ctx, sqlUpdateAutoCancelStatus,
		statusCancelled,
		customerPlanID)
	if err != nil {
		return span.Fail(fmt.Errorf("unable to update status in autocancel table: %w", err))
	}

	n, err := result.RowsAffected()
	if err != nil {
		return span.Fail(fmt.Errorf("failed to retrieve rows affected from autocancel table update result: %w", err))
	}
	if n == 0 {
		return sql.ErrNoRows
	}

	return nil
}

// SetSubscriptionForCancellation adds record to autocancel table with status to be cancelled and cancellation date set.
func (r *Repo) SetSubscriptionForCancellation(ctx context.Context, autoCancel *AutoCancel) error {
	ctx, span := observability.StartSpan(ctx, "repo.subscription.SetSubscriptionForCancellation")
	defer span.End()

	_, err := r.db.ExecContext(
		ctx,
		sqlUpsertAutoCancelToBeCanceled,
		autoCancel.CancellationDate,
		autoCancel.CustomerPlanID,
		autoCancel.Voucher,
	)
	if err != nil {
		return span.Fail(fmt.Errorf("could not execute upsert autocancel to be canceled statement: %w", err))
	}

	return nil
}

// SetRunningSubscription upserts a running subscription.
func (r *Repo) SetRunningSubscription(ctx context.Context, autoCancel *AutoCancel) error {
	ctx, span := observability.StartSpan(ctx, "repo.subscription.SetRunningSubscription")
	defer span.End()

	_, err := r.db.ExecContext(
		ctx,
		sqlUpsertAutoCancelRunning,
		autoCancel.CustomerPlanID,
		autoCancel.Voucher,
	)
	if err != nil {
		return span.Fail(fmt.Errorf("could not execute upsert autocancel running statement: %w", err))
	}

	return nil
}

// find finds the autocancel status of the customer on the database.
func (r *Repo) find(ctx context.Context, customerPlanID uuid.NullUUID, status string) (*AutoCancel, error) {
	ctx, span := observability.StartSpan(ctx, "repo.autocancel.find")
	defer span.End()

	var autoCancel AutoCancel

	err := r.db.QueryRowContext(
		ctx,
		sqlFindAutoCancel,
		customerPlanID,
		status,
	).Scan(
		&autoCancel.Status,
		&autoCancel.CreatedAt,
		&autoCancel.CancellationDate,
		&autoCancel.CustomerPlanID,
		&autoCancel.Voucher,
	)
	if err != nil {
		return nil, span.Fail(fmt.Errorf("could not execute query: %w", err))
	}

	return &autoCancel, nil
}

// FindToBeCanceled finds the autocancel record with status tobecanceled.
func (r *Repo) FindToBeCanceled(ctx context.Context, customerPlanID uuid.NullUUID) (*AutoCancel, error) {
	ctx, span := observability.StartSpan(ctx, "repo.autocancel.FindToBeCanceled")
	defer span.End()

	autoCancel, err := r.find(ctx, customerPlanID, statusToBeCanceled)
	return autoCancel, err
}

// FindCanceled finds the autocancel record with status cancelled.
func (r *Repo) FindCanceled(ctx context.Context, customerPlanID uuid.NullUUID) error {
	ctx, span := observability.StartSpan(ctx, "repo.autocancel.FindCanceled")
	defer span.End()

	_, err := r.find(ctx, customerPlanID, statusCancelled)
	if errors.Is(err, sql.ErrNoRows) {
		// No rows means that the subscription is not cancelled
		return nil
	}
	if err != nil {
		return span.Fail(fmt.Errorf("could not execute query: %w", err))
	}

	return ErrAlreadyCancelled
}

// FindRunning finds the autocancel record with status running.
func (r *Repo) FindRunning(ctx context.Context, customerPlanID uuid.NullUUID) (*AutoCancel, error) {
	ctx, span := observability.StartSpan(ctx, "repo.autocancel.FindRunning")
	defer span.End()

	autoCancel, err := r.find(ctx, customerPlanID, statusRunning)
	if errors.Is(err, sql.ErrNoRows) {
		// No rows means that the subscription is not running
		return nil, ErrRunningSubscriptionNotFound
	}
	if err != nil {
		return nil, span.Fail(fmt.Errorf("could not execute query: %w", err))
	}
	return autoCancel, nil
}

// AllSetForCancellation returns all subscriptions that are set to be cancelled.
func (r *Repo) AllSetForCancellation(ctx context.Context) ([]AutoCancel, error) {
	ctx, span := observability.StartSpan(ctx, "repo.customer.AllSetForCancellation")
	defer span.End()

	var autoCancelList []AutoCancel

	rows, err := r.db.QueryContext(
		ctx,
		sqlAllSetForCancellation,
		statusToBeCanceled,
	)
	if err != nil {
		return nil, span.Fail(fmt.Errorf("could not execute query: %w", err))
	}

	for rows.Next() {
		var autoCancel AutoCancel

		scanFields := []interface{}{
			&autoCancel.Status,
			&autoCancel.CustomerPlanID,
		}

		err := rows.Scan(scanFields...)
		if err != nil {
			return nil, span.Fail(fmt.Errorf("could not scan row: %w", err))
		}

		autoCancelList = append(autoCancelList, autoCancel)
	}

	return autoCancelList, nil
}

// NoopRepo noop object for mocking
type NoopRepo struct{}

// SetAsCancelled updates status of a subscription to cancelled
func (r *NoopRepo) SetAsCancelled(ctx context.Context, subscriptionID int64, country string) error {
	return nil
}

// SetSubscriptionForCancellation upserts a subscription setting it for cancellation.
func (r *NoopRepo) SetSubscriptionForCancellation(ctx context.Context, autoCancel *AutoCancel) error {
	return nil
}
