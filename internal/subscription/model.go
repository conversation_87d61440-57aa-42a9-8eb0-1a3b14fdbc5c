package subscription

import (
	"time"

	"github.com/gofrs/uuid"
)

type (
	// AutoCancel model for the `autocancel` table.
	AutoCancel struct {
		Country                    string
		CreatedAt                  time.Time
		CustomerID                 int64
		CustomerUUID               uuid.NullUUID
		Postcode                   *string
		Products                   []string
		RemainingVoucherAmount     *float64
		Status                     *string
		SubscriptionID             *int64
		Voucher                    *string
		CancellationDate           *time.Time
		CustomerPlanID             uuid.NullUUID
		IsSubscriptionInitialOrder bool
	}
)
