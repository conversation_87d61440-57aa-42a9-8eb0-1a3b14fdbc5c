package subscription

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/gofrs/uuid"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
)

func TestRepo_FindToBeCanceled(t *testing.T) {
	t.Parallel()

	status := statusToBeCanceled
	voucher := "ABC123"
	customerPlanID := uuid.NullUUID{UUID: uuid.Must(uuid.NewV4()), Valid: true}
	now := time.Now()
	cancellationTime := now.Add(72 * time.Hour)
	autoCancel := AutoCancel{
		Status:           &status,
		CustomerPlanID:   customerPlanID,
		CancellationDate: &cancellationTime,
		CreatedAt:        now,
		Voucher:          &voucher,
	}

	type args struct {
		customerPlanID uuid.NullUUID
		expected       *AutoCancel
		err            error
	}

	tests := []struct {
		name    string
		setup   func(mock sqlmock.Sqlmock)
		args    args
		wantErr bool
	}{
		{
			name: "QueryContext fails",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(sqlFindAutoCancel).
					WithArgs(customerPlanID, statusToBeCanceled).
					WillReturnRows(sqlmock.NewRows([]string{"status", "created_at", "cancellation_date", "customer_plan_id", "voucher_code"}).
						AddRow(statusToBeCanceled, now, cancellationTime, customerPlanID, voucher)).
					WillReturnError(errors.New("queryrowcontext failed"))
			},
			args: args{
				customerPlanID: customerPlanID,
				expected:       nil,
				err:            errors.New("queryrowcontext failed"),
			},
			wantErr: true,
		},
		{
			name: "QueryContext success",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(sqlFindAutoCancel).
					WithArgs(customerPlanID, statusToBeCanceled).
					WillReturnRows(sqlmock.NewRows([]string{"status", "created_at", "cancellation_date", "customer_plan_id", "voucher_code"}).
						AddRow(statusToBeCanceled, now, cancellationTime, customerPlanID, voucher))
			},
			args: args{
				customerPlanID: customerPlanID,
				expected:       &autoCancel,
				err:            nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB, mock, err := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			if tt.setup != nil {
				tt.setup(mock)
			}

			if tt.args.err != nil {
				mock.ExpectRollback()
			} else {
				mock.ExpectCommit()
			}

			r := NewRepo(sqlxDB, nil)
			res, err := r.FindToBeCanceled(t.Context(), tt.args.customerPlanID)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindToBeCanceled() error = %v, wantErr %v", err, tt.wantErr)
			}

			assert.Equal(t, tt.args.expected, res)
		})
	}
}

func TestRepo_FindCanceled(t *testing.T) {
	t.Parallel()

	customerPlanID := uuid.NullUUID{UUID: uuid.Must(uuid.NewV4()), Valid: true}
	now := time.Now()
	cancellationTime := now.Add(72 * time.Hour)

	type args struct {
		customerPlanID uuid.NullUUID
	}

	tests := []struct {
		name  string
		setup func(mock sqlmock.Sqlmock)
		args  args
		err   error
	}{
		{
			name: "QueryContext fails",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(sqlFindAutoCancel).
					WithArgs(customerPlanID, statusCancelled).
					WillReturnRows(sqlmock.NewRows([]string{"status", "created_at", "cancellation_date", "customer_plan_id", "voucher_code"}).
						AddRow(statusCancelled, now, cancellationTime, customerPlanID, "ABC123")).
					WillReturnError(errors.New("queryrowcontext failed"))
			},
			args: args{
				customerPlanID: customerPlanID,
			},
			err: errors.New("queryrowcontext failed"),
		},
		{
			name: "QueryContext success",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(sqlFindAutoCancel).
					WithArgs(customerPlanID, statusCancelled).
					WillReturnRows(sqlmock.NewRows([]string{"status", "created_at", "cancellation_date", "customer_plan_id", "voucher_code"}).
						AddRow(statusCancelled, now, cancellationTime, customerPlanID, "ABC123"))
			},
			args: args{
				customerPlanID: customerPlanID,
			},
			err: ErrAlreadyCancelled,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB, mock, err := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			if tt.setup != nil {
				tt.setup(mock)
			}

			if tt.err != nil {
				mock.ExpectRollback()
			} else {
				mock.ExpectCommit()
			}

			r := NewRepo(sqlxDB, nil)
			err = r.FindCanceled(t.Context(), tt.args.customerPlanID)
			if (err != nil) != (tt.err != nil) {
				t.Errorf("FindCanceled() error = %v, wantErr %v", err, tt.err)
			}
		})
	}
}

func TestRepo_FindRunning(t *testing.T) {
	t.Parallel()

	status := statusRunning
	voucher := "ABC123"
	customerPlanID := uuid.NullUUID{UUID: uuid.Must(uuid.NewV4()), Valid: true}
	now := time.Now()
	cancellationTime := now.Add(72 * time.Hour)
	autoCancel := AutoCancel{
		Status:           &status,
		CustomerPlanID:   customerPlanID,
		CancellationDate: &cancellationTime,
		CreatedAt:        now,
		Voucher:          &voucher,
	}

	type args struct {
		customerPlanID uuid.NullUUID
		expected       *AutoCancel
		err            error
	}

	tests := []struct {
		name    string
		setup   func(mock sqlmock.Sqlmock)
		args    args
		wantErr bool
	}{
		{
			name: "QueryContext fails",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(sqlFindAutoCancel).
					WithArgs(customerPlanID, statusRunning).
					WillReturnRows(sqlmock.NewRows([]string{"status", "created_at", "cancellation_date", "customer_plan_id", "voucher_code"}).
						AddRow(statusRunning, now, cancellationTime, customerPlanID, voucher)).
					WillReturnError(errors.New("queryrowcontext failed"))
			},
			args: args{
				customerPlanID: customerPlanID,
				expected:       nil,
				err:            errors.New("queryrowcontext failed"),
			},
			wantErr: true,
		},
		{
			name: "QueryContext success",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(sqlFindAutoCancel).
					WithArgs(customerPlanID, statusRunning).
					WillReturnRows(sqlmock.NewRows([]string{"status", "created_at", "cancellation_date", "customer_plan_id", "voucher_code"}).
						AddRow(statusRunning, now, cancellationTime, customerPlanID, voucher))
			},
			args: args{
				customerPlanID: customerPlanID,
				expected:       &autoCancel,
				err:            nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB, mock, err := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			if tt.setup != nil {
				tt.setup(mock)
			}

			if tt.args.err != nil {
				mock.ExpectRollback()
			} else {
				mock.ExpectCommit()
			}

			r := NewRepo(sqlxDB, nil)
			res, err := r.FindRunning(t.Context(), tt.args.customerPlanID)
			if (err != nil) != tt.wantErr {
				t.Errorf("FindRunning() error = %v, wantErr %v", err, tt.wantErr)
			}

			assert.Equal(t, tt.args.expected, res)
		})
	}
}

func TestRepo_SetAsCancelled(t *testing.T) {
	t.Parallel()

	customerPlanID := uuid.NullUUID{UUID: uuid.Must(uuid.NewV4()), Valid: true}

	type args struct {
		customerPlanID uuid.NullUUID
		res            int64
		err            error
	}
	tests := []struct {
		name    string
		setup   func(mock sqlmock.Sqlmock)
		args    args
		wantErr bool
	}{
		{
			name: "Update succeed",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(sqlUpdateAutoCancelStatus).
					WithArgs(statusCancelled, customerPlanID).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			args: args{
				customerPlanID: customerPlanID,
				res:            1,
				err:            nil,
			},
			wantErr: false,
		},
		{
			name: "Row is not exist",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(sqlUpdateAutoCancelStatus).
					WithArgs(statusCancelled, customerPlanID).
					WillReturnResult(sqlmock.NewResult(0, 0))
			},
			args: args{
				customerPlanID: customerPlanID,
				res:            0,
				err:            sql.ErrNoRows,
			},
			wantErr: true,
		},
		{
			name: "Update failed",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(sqlUpdateAutoCancelStatus).
					WithArgs(statusCancelled, customerPlanID).
					WillReturnError(errors.New("error"))
			},
			args: args{
				customerPlanID: customerPlanID,
				res:            1,
				err:            errors.New("error"),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB, mock, err := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			if tt.setup != nil {
				tt.setup(mock)
			}

			sr := NewRepo(sqlxDB, nil)
			err = sr.SetAsCancelled(t.Context(), tt.args.customerPlanID)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetAsCancelled() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestRepo_SetSubscriptionForCancellation(t *testing.T) {
	t.Parallel()

	customerPlanID := uuid.NullUUID{UUID: uuid.Must(uuid.NewV4()), Valid: true}
	now := time.Now()
	cancellationTime := now.Add(72 * time.Hour)

	voucher := "ABC123"
	setupStatement := `INSERT INTO autocancel`

	type args struct {
		autoCancel *AutoCancel
		res        int64
		err        error
	}
	tests := []struct {
		name    string
		setup   func(mock sqlmock.Sqlmock)
		args    args
		wantErr bool
	}{
		{
			name: "Exec fails",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(setupStatement).
					WithArgs(statusToBeCanceled, AnyTime{}, customerPlanID, voucher).
					WillReturnResult(sqlmock.NewResult(0, 0)).
					WillReturnError(errors.New("exec failed"))
			},
			args: args{
				autoCancel: &AutoCancel{
					CustomerPlanID:   customerPlanID,
					Voucher:          &voucher,
					CancellationDate: &cancellationTime,
				},
				res: 0,
				err: errors.New("exec failed"),
			},
			wantErr: true,
		},
		{
			name: "Upsert works",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(setupStatement).
					WithArgs(AnyTime{}, customerPlanID, voucher).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			args: args{
				autoCancel: &AutoCancel{
					CustomerPlanID:   customerPlanID,
					Voucher:          &voucher,
					CancellationDate: &cancellationTime,
				},
				res: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB, mock, err := sqlmock.New()
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			if tt.setup != nil {
				tt.setup(mock)
			}

			r := NewRepo(sqlxDB, nil)
			if err := r.SetSubscriptionForCancellation(t.Context(), tt.args.autoCancel); (err != nil) != tt.wantErr {
				t.Errorf("SetSubscriptionForCancellation() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRepo_AllSetForCancellation(t *testing.T) {
	ctx := t.Context()

	status := statusToBeCanceled
	customerPlanID := uuid.NullUUID{UUID: uuid.Must(uuid.NewV4()), Valid: true}
	setupStatement := `SELECT
		status,
		customer_plan_id
	FROM
		autocancel`

	type args struct {
		ctx       context.Context
		toggleCPS bool
		expected  []AutoCancel
		err       error
	}

	tests := []struct {
		name    string
		setup   func(mock sqlmock.Sqlmock)
		args    args
		wantErr bool
	}{
		{
			name: "QueryContext fails",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(setupStatement).
					WithArgs(statusToBeCanceled).
					WillReturnRows(sqlmock.NewRows([]string{"status", "customer_plan_id"}).
						AddRow(statusToBeCanceled, customerPlanID)).
					WillReturnError(errors.New("querycontext failed"))
			},
			args: args{
				ctx:       ctx,
				toggleCPS: true,
				expected:  nil,
				err:       errors.New("querycontext failed"),
			},
			wantErr: true,
		},
		{
			name: "QueryContext success",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectQuery(setupStatement).
					WithArgs(statusToBeCanceled).
					WillReturnRows(sqlmock.NewRows([]string{"status", "customer_plan_id"}).
						AddRow(statusToBeCanceled, customerPlanID))
			},
			args: args{
				ctx:       ctx,
				toggleCPS: true,
				expected: []AutoCancel{
					{
						Status:         &status,
						CustomerPlanID: customerPlanID,
					},
				},
				err: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB, mock, err := sqlmock.New()
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			if tt.setup != nil {
				tt.setup(mock)
			}

			if tt.args.err != nil {
				mock.ExpectRollback()
			} else {
				mock.ExpectCommit()
			}

			r := NewRepo(sqlxDB, nil)
			res, err := r.AllSetForCancellation(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetSubscriptionForCancellation() error = %v, wantErr %v", err, tt.wantErr)
			}

			assert.Equal(t, tt.args.expected, res)
		})
	}

}

func TestRepo_SetRunningSubscription(t *testing.T) {
	t.Parallel()

	voucher := "ABC123"
	customerPlanID := uuid.NullUUID{UUID: uuid.FromStringOrNil("123e4567-e89b-12d3-a456-************"), Valid: true}
	setupStatement := `INSERT INTO autocancel`

	type args struct {
		autoCancel *AutoCancel
		res        int64
		err        error
	}
	tests := []struct {
		name    string
		setup   func(mock sqlmock.Sqlmock)
		args    args
		wantErr bool
	}{
		{
			name: "Exec fails",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(setupStatement).
					WithArgs(statusRunning, AnyTime{}, customerPlanID, voucher).
					WillReturnResult(sqlmock.NewResult(0, 0)).
					WillReturnError(errors.New("exec failed"))
			},
			args: args{
				autoCancel: &AutoCancel{
					CustomerPlanID: customerPlanID,
					Voucher:        &voucher,
				},
				res: 0,
				err: errors.New("exec failed"),
			},
			wantErr: true,
		},
		{
			name: "Upsert works",
			setup: func(mock sqlmock.Sqlmock) {
				mock.ExpectExec(setupStatement).
					WithArgs(customerPlanID, voucher).
					WillReturnResult(sqlmock.NewResult(1, 1))
			},
			args: args{
				autoCancel: &AutoCancel{
					CustomerPlanID: customerPlanID,
					Voucher:        &voucher,
				},
				res: 1,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB, mock, err := sqlmock.New()
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			if tt.setup != nil {
				tt.setup(mock)
			}

			r := NewRepo(sqlxDB, nil)
			if err := r.SetRunningSubscription(t.Context(), tt.args.autoCancel); (err != nil) != tt.wantErr {
				t.Errorf("SetRunningSubscription() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

type AnyTime struct{}

// Match satisfies sqlmock.Argument interface
func (a AnyTime) Match(v driver.Value) bool {
	_, ok := v.(time.Time)
	return ok
}
