// Code generated by MockGen. DO NOT EDIT.
// Source: repo.go

// Package subscription is a generated GoMock package.
package subscription

import (
	context "context"
	sql "database/sql"
	reflect "reflect"

	uuid "github.com/gofrs/uuid"
	gomock "github.com/golang/mock/gomock"
)

// MockPublisher is a mock of Publisher interface.
type MockPublisher struct {
	ctrl     *gomock.Controller
	recorder *MockPublisherMockRecorder
}

// MockPublisherMockRecorder is the mock recorder for MockPublisher.
type MockPublisherMockRecorder struct {
	mock *MockPublisher
}

// NewMockPublisher creates a new mock instance.
func NewMockPublisher(ctrl *gomock.Controller) *MockPublisher {
	mock := &MockPublisher{ctrl: ctrl}
	mock.recorder = &MockPublisherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPublisher) EXPECT() *MockPublisherMockRecorder {
	return m.recorder
}

// Publish mocks base method.
func (m *MockPublisher) Publish(ctx context.Context, tx *sql.Tx, exchange, routingKey string, payload interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", ctx, tx, exchange, routingKey, payload)
	ret0, _ := ret[0].(error)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockPublisherMockRecorder) Publish(ctx, tx, exchange, routingKey, payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockPublisher)(nil).Publish), ctx, tx, exchange, routingKey, payload)
}

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// Find mocks base method.
func (m *MockRepository) Find(ctx context.Context, customerPlanID uuid.NullUUID) (*AutoCancel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Find", ctx, customerPlanID)
	ret0, _ := ret[0].(*AutoCancel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockRepositoryMockRecorder) Find(ctx, customerPlanID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockRepository)(nil).Find), ctx, customerPlanID)
}

// SetSubscriptionForCancellation mocks base method.
func (m *MockRepository) SetSubscriptionForCancellation(ctx context.Context, autoCancel *AutoCancel) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSubscriptionForCancellation", ctx, autoCancel)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetSubscriptionForCancellation indicates an expected call of SetSubscriptionForCancellation.
func (mr *MockRepositoryMockRecorder) SetSubscriptionForCancellation(ctx, autoCancel interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSubscriptionForCancellation", reflect.TypeOf((*MockRepository)(nil).SetSubscriptionForCancellation), ctx, autoCancel)
}
