package subscription

import "github.com/gofrs/uuid"

// AutocancelOrderConfirmationEvent event when subscription has been created with autocancel voucher code
type AutocancelOrderConfirmationEvent struct {
	AutoCancellationDate   int64   `json:"auto_cancellation_date"`
	Country                string  `json:"country"`
	CustomerID             int64   `json:"customer_id"`
	Event                  string  `json:"event"`
	RemainingVoucherAmount float64 `json:"remaining_voucher_amount"`
	SubscriptionID         int64   `json:"subscription_id"`
	VoucherCode            string  `json:"voucher_code"`
}

// CancellationEvent external event to cancel subscription
type CancellationEvent struct {
	Type      string                   `json:"type"`
	Producer  string                   `json:"producer"`
	Payload   CancellationEventPayload `json:"payload"`
	Timestamp string                   `json:"timestamp"`
}

// CancellationEventPayload payload with all data for CancellationEvents.
type CancellationEventPayload struct {
	CancelOrders           bool    `json:"cancel_orders"`
	CancellationDate       string  `json:"cancellation_date"`
	CustomerID             int64   `json:"customer_id"`
	Isb2bSale              bool    `json:"is_b2b_sale"`
	RemainingVoucherAmount float64 `json:"remaining_voucher_amount"`
	SubscriptionID         int     `json:"subscription_id"`
	VoucherCode            string  `json:"voucher_code"`
}

// AutoCancelProjectionUpdated internal event to notify consumer that the projection was updated.
type AutoCancelProjectionUpdated struct {
	SubscriptionID int64     `json:"subscription_id"`
	CustomerID     int64     `json:"customer_id"`
	CustomerUUID   uuid.UUID `json:"customer_uuid"`
	Country        string    `json:"country"`
	Timestamp      int64     `json:"timestamp"`
}
