// Code generated by MockGen. DO NOT EDIT.
// Source: repo.go

// Package campaignsubscription is a generated GoMock package.
package campaignsubscription

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockCampaignSubscription is a mock of CampaignSubscription interface.
type MockCampaignSubscription struct {
	ctrl     *gomock.Controller
	recorder *MockCampaignSubscriptionMockRecorder
}

// MockCampaignSubscriptionMockRecorder is the mock recorder for MockCampaignSubscription.
type MockCampaignSubscriptionMockRecorder struct {
	mock *MockCampaignSubscription
}

// NewMockCampaignSubscription creates a new mock instance.
func NewMockCampaignSubscription(ctrl *gomock.Controller) *MockCampaignSubscription {
	mock := &MockCampaignSubscription{ctrl: ctrl}
	mock.recorder = &MockCampaignSubscriptionMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCampaignSubscription) EXPECT() *MockCampaignSubscriptionMockRecorder {
	return m.recorder
}

// Track mocks base method.
func (m *MockCampaignSubscription) Track(ctx context.Context, campaignID string, subscriptionID int64, voucherCode string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Track", ctx, campaignID, subscriptionID, voucherCode)
	ret0, _ := ret[0].(error)
	return ret0
}

// Track indicates an expected call of Track.
func (mr *MockCampaignSubscriptionMockRecorder) Track(ctx, campaignID, subscriptionID, voucherCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Track", reflect.TypeOf((*MockCampaignSubscription)(nil).Track), ctx, campaignID, subscriptionID, voucherCode)
}
