package campaignsubscription

import (
	"context"
	"errors"
	"testing"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/golang/mock/gomock"
	"github.com/jmoiron/sqlx"

	"github.com/hellofresh/credit-autocancel-service/internal/database"
)

func TestRepo_Track(t *testing.T) {
	ctx := context.Background()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	db, mock, err := sqlmock.New()
	if err != nil {
		t.Error(err)
	}
	mockDb := database.DB{
		DB: sqlx.NewDb(db, "sqlmock"),
	}

	tests := []struct {
		ctx            context.Context
		testType       string
		campaignID     string
		subscriptionID int64
		voucherCode    string
		res            int64
		err            error
		wantErr        bool
	}{
		{
			ctx:            ctx,
			testType:       "Insert works",
			campaignID:     "00000000-0000-0000-0000-000000000000",
			subscriptionID: 12345,
			voucherCode:    "VOUCHER",
			res:            1,
			wantErr:        false,
		},
		{
			ctx:            ctx,
			testType:       "Exec fails",
			campaignID:     "",
			subscriptionID: 1234,
			voucherCode:    "VOUCHER",
			res:            0,
			err:            errors.New("exec failed"),
			wantErr:        true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.testType, func(t *testing.T) {
			mock.ExpectBegin()
			mock.ExpectExec("INSERT INTO campaign_subscription").
				WithArgs(
					tt.campaignID,
					tt.subscriptionID,
					tt.voucherCode,
				).
				WillReturnResult(sqlmock.NewResult(tt.res, tt.res)).WillReturnError(tt.err)

			if tt.err != nil {
				mock.ExpectRollback()
			} else {
				mock.ExpectCommit()
			}

			r := NewRepo(mockDb.DB)
			if err := r.Track(tt.ctx, tt.campaignID, tt.subscriptionID, tt.voucherCode); (err != nil) != tt.wantErr {
				t.Errorf("Track() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
