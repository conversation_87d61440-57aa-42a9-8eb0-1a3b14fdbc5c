//go:generate mockgen -source repo.go -destination campaignsubscription_mock.go -package campaignsubscription

package campaignsubscription

import (
	"context"
	"fmt"

	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/jmoiron/sqlx"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// CampaignSubscription defines method to be able to track campaigns with subscriptions
type CampaignSubscription interface {
	Track(ctx context.Context, campaignID string, subscriptionID int64, voucherCode string) error
}

// Repo repository of campaignsubscription
type Repo struct {
	db *sqlx.DB
}

// NewRepo initializes repo of campaignsubscription
func NewRepo(db *sqlx.DB) *Repo {
	return &Repo{db: db}
}

// Track CampaignID and SubscriptionID
func (r *Repo) Track(ctx context.Context, campaignID string, subscriptionID int64, voucherCode string) error {
	ctx, span := observability.StartSpan(ctx, "repo.campaignsubscription.Track")
	defer span.End()

	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return span.Fail(fmt.Errorf("unable to start transaction: %w", err))
	}
	query := `
		INSERT INTO campaign_subscription (
			campaign_id,
			subscription_id,
			voucher_code
		)
		VALUES (
			$1, $2, $3
		)
	`

	_, err = tx.ExecContext(ctx, query,
		campaignID,
		subscriptionID,
		voucherCode,
	)

	if err != nil {
		_ = tx.Rollback()
		return span.Fail(fmt.Errorf("unable to insert into campaign_subscription: %w", err))
	}

	if err = tx.Commit(); err != nil {
		_ = tx.Rollback()
		return span.Fail(fmt.Errorf("unable to commit transaction: %w", err))
	}

	trace.SpanFromContext(ctx).SetStatus(
		codes.Ok,
		"campaign subscription track completed",
	)

	return nil
}
