package database

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/hellofresh/growth-go-kit/database"
	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq" // Postgres driver
)

const (
	postgresDriver = "postgres"
	retryWaitTime  = time.Second * 5
)

type (
	// Config holds all database configurations.
	Config struct {
		DBHost          string        `envconfig:"DB_HOST" required:"true"`
		DBPort          int           `envconfig:"DB_PORT" required:"true"`
		DBUser          string        `envconfig:"DB_USER" required:"true"`
		DBPassword      string        `envconfig:"DB_PASSWORD" required:"true"`
		DBName          string        `envconfig:"DB_NAME" required:"true"`
		Retries         int           `envconfig:"DB_RETRIES" default:"5"`
		MaxOpenConns    int           `envconfig:"DB_MAX_OPEN_CONNS" default:"20"`
		MaxIdleConns    int           `envconfig:"DB_MAX_IDLE_CONNS" default:"20"`
		MaxConnLifetime time.Duration `envconfig:"DB_MAX_CONN_LIFETIME" default:"10m"`
	}

	// DB wraps a sqlx connection.
	DB struct {
		*sqlx.DB
	}
)

// Connect connects to the database using the specified configs.
func Connect(ctx context.Context, config Config) (*DB, error) {
	db, err := connect(ctx, config)
	if err != nil {
		if config.Retries > 1 {
			time.Sleep(retryWaitTime)
			config.Retries--
			return connect(ctx, config)
		}

		return nil, err
	}

	return db, nil
}

func connect(ctx context.Context, config Config) (*DB, error) {
	postgresDSN := fmt.Sprintf(
		"postgres://%s:%s@%s:%s/%s?sslmode=disable",
		config.DBUser, config.DBPassword, config.DBHost, strconv.Itoa(config.DBPort), config.DBName,
	)

	db, err := observability.NewDBX(
		"postgres",
		database.Config{
			DSN:          postgresDSN,
			MaxOpenConns: config.MaxOpenConns,
			MaxIdleConns: config.MaxIdleConns,
			MaxLifetime:  config.MaxConnLifetime,
		},
	)
	if err != nil {
		return nil, err
	}
	err = db.Ping()
	if err != nil {
		return nil, err
	}

	return &DB{db}, nil
}
