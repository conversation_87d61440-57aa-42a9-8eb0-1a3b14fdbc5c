package payload

import (
	"encoding/json"
	"fmt"

	validation "github.com/go-ozzo/ozzo-validation/v4"
)

// UpdateCampaignPayload payload for updating campaign status and/or campaign type
type UpdateCampaignPayload struct {
	Active       *bool   `json:"active,omitempty"`
	CampaignType *string `json:"campaign_type,omitempty"`
}

// Validate validates the data that is passed inside UpdateCampaignPayload struct
func (cp UpdateCampaignPayload) Validate() error {
	// Check if at least one field is provided
	if cp.Active == nil && cp.CampaignType == nil {
		return fmt.Errorf("at least one field (active or campaign_type) must be provided")
	}

	// Build field validation rules conditionally
	rules := make([]*validation.FieldRules, 0)

	if cp.CampaignType != nil {
		rules = append(rules, validation.Field(&cp.CampaignType,
			validation.In(CampaignTypeCancelAny, CampaignTypeCancelOnce).Error(fmt.Sprintf("must be either %s or %s", CampaignTypeCancelAny, CampaignTypeCancelOnce))))
	}

	return validation.ValidateStruct(&cp, rules...)
}

// NewUpdateCampaignPayload will unmarshall the serialized payload, run validation, and return the UpdateCampaignPayload struct
func NewUpdateCampaignPayload(serializedPayload []byte) (*UpdateCampaignPayload, error) {
	var updateCampaignPayload UpdateCampaignPayload

	if err := json.Unmarshal(serializedPayload, &updateCampaignPayload); err != nil {
		return nil, fmt.Errorf("could not unmarshal message with an invalid payload: %w", err)
	}

	err := updateCampaignPayload.Validate()
	if err != nil {
		return nil, fmt.Errorf("update campaign payload validation error: %w", err)
	}

	return &updateCampaignPayload, nil
}
