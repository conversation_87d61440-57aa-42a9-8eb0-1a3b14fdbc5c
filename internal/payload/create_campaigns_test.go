package payload

import (
	"reflect"
	"testing"

	"github.com/gofrs/uuid"
)

func TestNewCampaignsPayload(t *testing.T) {
	createCampaignsSuccessPayload := `{
    "campaign_ids":  ["d7052649-d227-4e64-9448-ed38621b803f"],
    "active": 		 true,
    "country": 		 "us",
    "campaign_type": "CANCEL_ANY"
}`
	createCampaignsEmptyTypeValidPayload := `{
		"campaign_ids":  ["d7052649-d227-4e64-9448-ed38621b803f"],
		"active": 		 true,
		"country": 		 "us",
		"campaign_type": ""
	}`
	createCampaignsNoTypeValidPayload := `{
		"campaign_ids":  ["d7052649-d227-4e64-9448-ed38621b803f"],
		"active": 		 true,
		"country": 		 "us"
	}`
	createCampaignsInvalidCampaignTypePayload := `{
    "campaign_ids":  ["d7052649-d227-4e64-9448-ed38621b803f"],
    "active": 		 true,
    "country": 		 "UU",
    "campaign_type": "INVALID"
}`
	createCampaignsInvalidCountryPayload := `{
    "campaign_ids": ["d7052649-d227-4e64-9448-ed38621b803f"],
    "active": true,
    "country": "UU"
}`
	createCampaignsMissingActiveStatus := `{
    "campaign_ids": ["d7052649-d227-4e64-9448-ed38621b803f"],
    "country": "US"
}`
	createCampaignsMissingCampaignIDs := `{
    "campaign_ids": [],
    "active": true,
    "country": "us"
}`

	createCampaignsValidPayload := CampaignsPayload{
		CampaignIDs:  []uuid.UUID{uuid.Must(uuid.FromString("d7052649-d227-4e64-9448-ed38621b803f"))},
		Active:       true,
		Country:      "US",
		CampaignType: "CANCEL_ANY",
	}

	type args struct {
		serializedPayload []byte
	}
	tests := []struct {
		name    string
		args    args
		want    *CampaignsPayload
		wantErr bool
	}{
		{
			name:    "valid payload all fields populated",
			args:    args{serializedPayload: []byte(createCampaignsSuccessPayload)},
			want:    &createCampaignsValidPayload,
			wantErr: false,
		},
		{
			name:    "valid payload empty campaign_type",
			args:    args{serializedPayload: []byte(createCampaignsEmptyTypeValidPayload)},
			want:    &createCampaignsValidPayload,
			wantErr: false,
		},
		{
			name:    "valid payload no campaign_type",
			args:    args{serializedPayload: []byte(createCampaignsNoTypeValidPayload)},
			want:    &createCampaignsValidPayload,
			wantErr: false,
		},
		{
			name:    "invalid payload incorrect campaign type",
			args:    args{serializedPayload: []byte(createCampaignsInvalidCampaignTypePayload)},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "invalid payload incorrect country code",
			args:    args{serializedPayload: []byte(createCampaignsInvalidCountryPayload)},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "invalid payload missing active status",
			args:    args{serializedPayload: []byte(createCampaignsMissingActiveStatus)},
			want:    nil,
			wantErr: true,
		},
		{
			name:    "invalid payload no campaign IDs",
			args:    args{serializedPayload: []byte(createCampaignsMissingCampaignIDs)},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := NewCampaignsPayload(tt.args.serializedPayload)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewCampaignsPayload() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewCampaignsPayload() got = %v, want %v", got, tt.want)
			}
		})
	}
}
