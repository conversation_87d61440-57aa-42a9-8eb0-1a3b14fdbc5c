package payload

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/gofrs/uuid"

	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/go-ozzo/ozzo-validation/v4/is"
)

// CampaignsPayload payload for all the data that will be added to the campaign table
type CampaignsPayload struct {
	CampaignIDs  []uuid.UUID `json:"campaign_ids"`
	Active       bool        `json:"active"`
	Country      string      `json:"country"`
	CampaignType string      `json:"campaign_type"`
}

// CampaignTypeCancelAny and CampaignTypeCancelOnce are the two types of campaigns that can be created
// CampaignTypeCancelAny means that all the subscriptions of a customer with an eligible voucher will be cancelled
// CampaignTypeCancelOnce means that only the first subscription of a customer with an eligible voucher will be cancelled
const (
	CampaignTypeCancelAny  = "CANCEL_ANY"
	CampaignTypeCancelOnce = "CANCEL_ONCE"
)

// Validate validates the data that is passed inside CreateChallengesPayload struct
func (cp CampaignsPayload) Validate() error {
	return validation.ValidateStruct(&cp,
		validation.Field(&cp.CampaignIDs, validation.Required),
		validation.Field(&cp.Active, validation.Required),
		validation.Field(&cp.Country, validation.Required, is.CountryCode2),
		validation.Field(&cp.CampaignType, validation.Required, validation.In(CampaignTypeCancelAny, CampaignTypeCancelOnce).Error(fmt.Sprintf("must be either %s or %s", CampaignTypeCancelAny, CampaignTypeCancelOnce))),
	)
}

// NewCampaignsPayload will unmarshall the serialized payload, run validation, and return the CampaignsPayload struct
func NewCampaignsPayload(serializedPayload []byte) (*CampaignsPayload, error) {
	var campaignsPayload CampaignsPayload

	if err := json.Unmarshal(serializedPayload, &campaignsPayload); err != nil {
		return nil, fmt.Errorf("could not unmarshal message with an invalid payload: %w", err)
	}

	// Set default value for CampaignType if not provided
	if campaignsPayload.CampaignType == "" {
		defaultCampaignType := CampaignTypeCancelAny
		campaignsPayload.CampaignType = defaultCampaignType
	}

	// setting country to avoid user errors and pass validation
	campaignsPayload.Country = strings.ToUpper(campaignsPayload.Country)
	err := campaignsPayload.Validate()
	if err != nil {
		return nil, fmt.Errorf("campaigns payload validation error: %w", err)
	}

	return &campaignsPayload, nil
}
