package payload

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewUpdateCampaignPayload(t *testing.T) {
	t.<PERSON>lle<PERSON>()
	updateCampaignSuccessPayload := `{
    "active": false
}`
	updateCampaignActiveTruePayload := `{
    "active": true
}`
	updateCampaignTypePayload := `{
    "campaign_type": "CANCEL_ONCE"
}`
	updateBothFieldsPayload := `{
    "active": true,
    "campaign_type": "CANCEL_ANY"
}`
	updateInvalidCampaignTypePayload := `{
    "campaign_type": "INVALID_TYPE"
}`
	updateCampaignMissingBothPayload := `{}`
	invalidJSONPayload := `{invalid json}`

	activeFalse := false
	activeTrue := true
	campaignTypeOnce := "CANCEL_ONCE"
	campaignTypeAny := "CANCEL_ANY"

	updateCampaignValidPayload := UpdateCampaignPayload{
		Active: &activeFalse,
	}

	updateCampaignActiveTrueValidPayload := UpdateCampaignPayload{
		Active: &activeTrue,
	}

	updateCampaignTypeValidPayload := UpdateCampaignPayload{
		CampaignType: &campaignTypeOnce,
	}

	updateBothFieldsValidPayload := UpdateCampaignPayload{
		Active:       &activeTrue,
		CampaignType: &campaignTypeAny,
	}

	type args struct {
		serializedPayload []byte
	}

	tests := []struct {
		name    string
		args    args
		want    *UpdateCampaignPayload
		wantErr bool
	}{
		{
			name: "success - active false",
			args: args{
				serializedPayload: []byte(updateCampaignSuccessPayload),
			},
			want:    &updateCampaignValidPayload,
			wantErr: false,
		},
		{
			name: "success - active true",
			args: args{
				serializedPayload: []byte(updateCampaignActiveTruePayload),
			},
			want:    &updateCampaignActiveTrueValidPayload,
			wantErr: false,
		},
		{
			name: "success - campaign type only",
			args: args{
				serializedPayload: []byte(updateCampaignTypePayload),
			},
			want:    &updateCampaignTypeValidPayload,
			wantErr: false,
		},
		{
			name: "success - both fields",
			args: args{
				serializedPayload: []byte(updateBothFieldsPayload),
			},
			want:    &updateBothFieldsValidPayload,
			wantErr: false,
		},
		{
			name: "invalid campaign type",
			args: args{
				serializedPayload: []byte(updateInvalidCampaignTypePayload),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "missing both fields",
			args: args{
				serializedPayload: []byte(updateCampaignMissingBothPayload),
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "invalid JSON",
			args: args{
				serializedPayload: []byte(invalidJSONPayload),
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := NewUpdateCampaignPayload(tt.args.serializedPayload)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewUpdateCampaignPayload() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestUpdateCampaignPayload_Validate(t *testing.T) {
	t.Parallel()
	activeFalse := false
	activeTrue := true
	campaignTypeOnce := "CANCEL_ONCE"
	campaignTypeAny := "CANCEL_ANY"
	invalidCampaignType := "INVALID_TYPE"

	tests := []struct {
		name    string
		payload UpdateCampaignPayload
		wantErr bool
	}{
		{
			name: "valid payload - active true",
			payload: UpdateCampaignPayload{
				Active: &activeTrue,
			},
			wantErr: false,
		},
		{
			name: "valid payload - active false",
			payload: UpdateCampaignPayload{
				Active: &activeFalse,
			},
			wantErr: false,
		},
		{
			name: "valid payload - campaign type CANCEL_ONCE",
			payload: UpdateCampaignPayload{
				CampaignType: &campaignTypeOnce,
			},
			wantErr: false,
		},
		{
			name: "valid payload - campaign type CANCEL_ANY",
			payload: UpdateCampaignPayload{
				CampaignType: &campaignTypeAny,
			},
			wantErr: false,
		},
		{
			name: "valid payload - both fields",
			payload: UpdateCampaignPayload{
				Active:       &activeTrue,
				CampaignType: &campaignTypeOnce,
			},
			wantErr: false,
		},
		{
			name: "invalid payload - missing both fields",
			payload: UpdateCampaignPayload{
				Active:       nil,
				CampaignType: nil,
			},
			wantErr: true,
		},
		{
			name: "invalid payload - invalid campaign type",
			payload: UpdateCampaignPayload{
				CampaignType: &invalidCampaignType,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			err := tt.payload.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateCampaignPayload.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
