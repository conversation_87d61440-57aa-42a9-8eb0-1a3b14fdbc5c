package migrate

import (
	"fmt"
	"net/url"
	"sort"
	"strconv"
	"strings"

	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/postgres" //imports postgres driver
	"github.com/golang-migrate/migrate/v4/source/iofs"
	"github.com/pkg/errors"

	"github.com/hellofresh/credit-autocancel-service/internal/database"
	"github.com/hellofresh/credit-autocancel-service/migrations"
)

type byPrefix []string

// Len is the number of elements in the collection.
func (c byPrefix) Len() int {
	return len(c)
}

// Swap swaps the elements with indexes i and j.
func (c byPrefix) Swap(i, j int) {
	c[i], c[j] = c[j], c[i]
}

// Less reports whether the element with
// index i should sort before the element with index j by comparing the
// prefix of the migration file.
func (c byPrefix) Less(i, j int) bool {
	fileNumberLeft, _, err := validateMigrationFile(c[i])
	if err != nil {
		return false
	}

	fileNumberRight, _, err := validateMigrationFile(c[j])
	if err != nil {
		return false
	}

	return fileNumberLeft < fileNumberRight
}

// Up runs all up migrations.
func Up(config database.Config) error {
	migrationNames := migrations.AssetNames()
	sort.Sort(byPrefix(migrationNames))

	expectedVersion := 1
	for _, name := range migrationNames {
		version, direction, err := validateMigrationFile(name)
		if err != nil {
			return errors.Wrap(err, "error validating migration name")
		}

		if direction == "down" {
			continue
		}

		if expectedVersion != version {
			return errors.Errorf("expecting migration version %d got %d", expectedVersion, version)
		}

		expectedVersion++
	}

	sourceDriver, err := iofs.New(migrations.FS(), ".")
	if err != nil {
		return errors.Wrap(err, "can not load files from embedded filesystem")
	}

	dsn := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=disable&client_encoding=UTF8",
		config.DBUser,
		url.QueryEscape(config.DBPassword),
		config.DBHost,
		config.DBPort,
		config.DBName,
	)
	migration, err := migrate.NewWithSourceInstance("iofs", sourceDriver, dsn)
	if err != nil {
		return errors.Wrap(err, "can not initialize migrations")
	}

	err = migration.Up()
	if err == migrate.ErrNoChange {
		return nil
	}

	return errors.Wrap(err, "error executing migrations")
}

func validateMigrationFile(name string) (int, string, error) {
	versionParts := strings.Split(name, "_")
	if len(versionParts) < 2 {
		return 0, "", errors.Errorf("invalid migration name %s", name)
	}
	fileNumber, err := strconv.Atoi(versionParts[0])
	if err != nil {
		return 0, "", errors.Errorf("invalid migration number %s", versionParts[0])
	}

	nameParts := strings.Split(name, ".")
	if len(nameParts) != 3 || (nameParts[1] != "down" && nameParts[1] != "up") {
		return 0, "", errors.Errorf("invalid migration name %s", name)
	}
	return fileNumber, nameParts[1], nil
}
