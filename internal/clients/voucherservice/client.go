//go:generate mockgen -source client.go -destination voucher_mock.go -package voucherservice

package voucherservice

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"

	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/hellofresh/growth-go-kit/observability"
)

// ErrNotFound error type for the 404 response
var ErrNotFound = errors.New("not found")

// VoucherService defines method to be able to get voucher details
type VoucherService interface {
	GetDetails(ctx context.Context, voucher string, country string) (*VoucherDetails, error)
}

// Client represents the voucher service client
type Client struct {
	httpClient *httputil.Client
	baseURL    string
}

// NewClient creates a new voucher service client
func NewClient(baseURL string) *Client {
	baseURL = strings.TrimRight(baseURL, "/")
	return &Client{
		httputil.NewClient(
			httputil.WithHTTPClient(
				&http.Client{
					Transport: observability.NewHTTPTransport(),
				},
			),
		),
		baseURL,
	}
}

// VoucherDetails response from the voucher details endpoint
type VoucherDetails struct {
	Code     string   `json:"code"`
	Campaign Campaign `json:"campaign"`
}

// Campaign object in the VoucherDetails response
type Campaign struct {
	CampaignID   string `json:"campaign_id"`
	CampaignName string `json:"campaign_name"`
	IsActive     bool   `json:"is_active"`
}

// GetDetails returns voucher details from the voucher service
func (c *Client) GetDetails(ctx context.Context, voucher string, country string) (*VoucherDetails, error) {
	ctx, span := observability.StartSpan(ctx, "clients.voucherservice.GetDetails")
	defer span.End()

	url := c.prepareURL(voucher, country)

	var vourcherDetails VoucherDetails
	err := c.httpClient.GetJSON(
		ctx,
		url,
		nil,
		&vourcherDetails,
	)
	if err != nil {
		var unexpectedStatusCodeError *httputil.UnexpectedStatusCodeError
		if errors.As(err, &unexpectedStatusCodeError) && unexpectedStatusCodeError.StatusCode == http.StatusNotFound {
			return nil, span.Fail(errors.Join(ErrNotFound, err))
		}

		return nil, span.Fail(fmt.Errorf("failed calling voucher service API: %w", err))
	}

	trace.SpanFromContext(ctx).SetStatus(
		codes.Ok,
		"got voucher details from voucher service",
	)

	return &vourcherDetails, nil
}

func (c *Client) prepareURL(voucher string, country string) string {
	voucherCode := url.PathEscape(strings.TrimSpace(voucher))
	return fmt.Sprintf("%s/vouchers/code/%s?country=%s", c.baseURL, voucherCode, country)
}

// Noop noop object for mocking
type Noop struct {
}

// NewNoop noop object for mocking
func NewNoop() *Noop {
	return &Noop{}
}

// GetDetails noop func for mocking
func (c *Noop) GetDetails(ctx context.Context, authHeader string, voucher string, country string) (*VoucherDetails, error) {
	return nil, nil
}
