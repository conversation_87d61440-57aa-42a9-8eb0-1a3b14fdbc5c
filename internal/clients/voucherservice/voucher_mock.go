// Code generated by MockGen. DO NOT EDIT.
// Source: client.go

// Package voucherservice is a generated GoMock package.
package voucherservice

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockVoucherService is a mock of VoucherService interface.
type MockVoucherService struct {
	ctrl     *gomock.Controller
	recorder *MockVoucherServiceMockRecorder
}

// MockVoucherServiceMockRecorder is the mock recorder for MockVoucherService.
type MockVoucherServiceMockRecorder struct {
	mock *MockVoucherService
}

// NewMockVoucherService creates a new mock instance.
func NewMockVoucherService(ctrl *gomock.Controller) *MockVoucherService {
	mock := &MockVoucherService{ctrl: ctrl}
	mock.recorder = &MockVoucherServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVoucherService) EXPECT() *MockVoucherServiceMockRecorder {
	return m.recorder
}

// GetDetails mocks base method.
func (m *MockVoucherService) GetDetails(ctx context.Context, voucher, country string) (*VoucherDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDetails", ctx, voucher, country)
	ret0, _ := ret[0].(*VoucherDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDetails indicates an expected call of GetDetails.
func (mr *MockVoucherServiceMockRecorder) GetDetails(ctx, voucher, country interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDetails", reflect.TypeOf((*MockVoucherService)(nil).GetDetails), ctx, voucher, country)
}
