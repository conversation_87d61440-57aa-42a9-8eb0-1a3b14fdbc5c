package voucherservice

import (
	"context"
	"net/http"
	"strings"
	"testing"

	"github.com/h2non/gock"
	"github.com/stretchr/testify/assert"
)

// nolint: paralleltest // Gock is not parallelizable
func TestClient_GetDetails(t *testing.T) {
	defer gock.Off()

	tests := []struct {
		name     string
		status   int
		response string
		give     string
		want     *VoucherDetails
		wantErr  string
	}{
		{
			name:     "success",
			give:     "TEST",
			status:   http.StatusOK,
			response: `{"campaign":{"campaign_id":"a870d5e1-8781-41a2-a611-b6cc757d1ccc"}}`,
			want: &VoucherDetails{
				Campaign: Campaign{
					CampaignID: "a870d5e1-8781-41a2-a611-b6cc757d1ccc",
				},
			},
		},
		{
			name:     "success with spaces in voucher code",
			give:     " TEST ",
			status:   http.StatusOK,
			response: `{"campaign":{"campaign_id":"a870d5e1-8781-41a2-a611-b6cc757d1ccc"}}`,
			want: &VoucherDetails{
				Campaign: Campaign{
					CampaignID: "a870d5e1-8781-41a2-a611-b6cc757d1ccc",
				},
			},
		},
		{
			name:     "success with escaped # voucher code",
			give:     "#TEST",
			status:   http.StatusOK,
			response: `{"campaign":{"campaign_id":"a870d5e1-8781-41a2-a611-b6cc757d1ccc"}}`,
			want: &VoucherDetails{
				Campaign: Campaign{
					CampaignID: "a870d5e1-8781-41a2-a611-b6cc757d1ccc",
				},
			},
		},
		{
			name:     "success with escaped + voucher code",
			give:     "TEST+TEST",
			status:   http.StatusOK,
			response: `{"campaign":{"campaign_id":"a870d5e1-8781-41a2-a611-b6cc757d1ccc"}}`,
			want: &VoucherDetails{
				Campaign: Campaign{
					CampaignID: "a870d5e1-8781-41a2-a611-b6cc757d1ccc",
				},
			},
		},
		{
			name:     "success with escaped % voucher code",
			give:     "TEST%TEST",
			status:   http.StatusOK,
			response: `{"campaign":{"campaign_id":"a870d5e1-8781-41a2-a611-b6cc757d1ccc"}}`,
			want: &VoucherDetails{
				Campaign: Campaign{
					CampaignID: "a870d5e1-8781-41a2-a611-b6cc757d1ccc",
				},
			},
		},
		{
			name:     "invalid response",
			status:   http.StatusOK,
			response: `{"campaign":{"campaign_id":123123}}`,
			wantErr:  "failed calling voucher service API: json: cannot unmarshal number into Go struct field Campaign.campaign.campaign_id of type string",
		},
		{
			name:     "500 response",
			status:   http.StatusInternalServerError,
			response: `{"campaign":{"campaign_id":123123}}`,
			wantErr:  "failed calling voucher service API: received unexpected status code: 500 for https://voucher-service.example/vouchers/code/?country=US",
		},
		{
			name:     "404 response",
			status:   http.StatusNotFound,
			response: `{"campaign":{"campaign_id":123123}}`,
			wantErr:  "not found\nreceived unexpected status code: 404 for https://voucher-service.example/vouchers/code/?country=US",
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			gock.New("https://voucher-service.example").
				MatchParam("country", "US").
				Get("/vouchers/code/" + strings.TrimSpace(tt.give)).
				Reply(tt.status).
				JSON(tt.response)

			c := NewClient("https://voucher-service.example")

			got, err := c.GetDetails(context.Background(), tt.give, "US")
			if tt.wantErr == "" {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			} else {
				assert.EqualError(t, err, tt.wantErr)
			}

			assert.True(t, gock.IsDone())
		})
	}
}

func TestClient_prepareURL(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name     string
		give     string
		country  string
		want     string
		wantErr  string
		wantBody string
	}{
		{
			name:    "success",
			give:    "TEST",
			country: "US",
			want:    "https://voucher-service.example/vouchers/code/TEST?country=US",
		},
		{
			name:    "success with spaces in voucher code",
			give:    " TEST ",
			country: "US",
			want:    "https://voucher-service.example/vouchers/code/TEST?country=US",
		},
		{
			name:    "success with a space in between voucher code",
			give:    "TEST TEST",
			country: "US",
			want:    "https://voucher-service.example/vouchers/code/TEST%20TEST?country=US",
		},
		{
			name:    "success with escaped # voucher code",
			give:    "#TEST",
			country: "US",
			want:    "https://voucher-service.example/vouchers/code/%23TEST?country=US",
		},
		{
			name:    "success with escaped + voucher code",
			give:    "TEST+TEST",
			country: "US",
			want:    "https://voucher-service.example/vouchers/code/TEST+TEST?country=US",
		},
		{
			name:    "success with escaped % voucher code",
			give:    "TEST%TEST",
			country: "US",
			want:    "https://voucher-service.example/vouchers/code/TEST%25TEST?country=US",
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			c := NewClient("https://voucher-service.example")

			got := c.prepareURL(tt.give, tt.country)
			assert.Equal(t, tt.want, got)
		})
	}
}
