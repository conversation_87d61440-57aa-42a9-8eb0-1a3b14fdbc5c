package priceservice

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestErrCalculate_IsAddonError(t *testing.T) {
	t.<PERSON>llel()

	tests := []struct {
		name   string
		give   CalculateErrors
		expect bool
	}{
		{
			name:   "true",
			give:   ErrTestAddonCalculate,
			expect: true,
		},
		{
			name: "unexpected body false",
			give: CalculateErrors{
				responseError{Body: "some error"},
				responseError{Body: addonErrorMsg},
			},
		},
		{
			name: "not errResp false",
			give: CalculateErrors{
				fmt.Errorf("some error"),
				responseError{Body: addonErrorMsg},
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.<PERSON>lle<PERSON>()

			assert.Equal(t, tt.expect, tt.give.IsAddonError())
		})
	}
}
