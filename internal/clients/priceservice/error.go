package priceservice

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/hellofresh/growth-go-kit/net/http/httputil"
)

// errAddonMsg is the message in error responses price-service that indicates the failure was due to an addon sku.
// We only care about price calculations for classic box skus, so these can be logged and squashed.
const addonErrorMsg = "unable to create calculable object with addon price"

type (
	// responseError is an error response from price-service.
	responseError struct {
		Body string `json:"error"`
	}

	// CalculateErrors contains errors for each individual sku's price-service calculation
	CalculateErrors []error
)

// ErrTestAddonCalculate contains an example ErrCalculate is IsAddonError so you can unit test ErrCalculate handling from other calling packages
var ErrTestAddonCalculate = CalculateErrors{responseError{Body: addonErrorMsg}}

// Error returns the error message.
func (e responseError) Error() string {
	return e.Body
}

// Error returns the error message.
func (e CalculateErrors) Error() string {
	var msg string
	for i, err := range e {
		msg += err.Error()
		if i < len(e)-1 {
			msg += ": "
		}
	}
	return fmt.Sprintf("failed to calculate price: %s", msg)
}

// IsAddonError checks if any error message contains the known addon error message from price-service.
func (e CalculateErrors) IsAddonError() bool {
	for _, err := range e {
		var er responseError
		if !errors.As(err, &er) ||
			er.Body == "" ||
			!strings.Contains(er.Body, addonErrorMsg) {
			return false
		}
	}
	return len(e) > 0
}

// errorHandler returns price-service error response.
func errorHandler(req *http.Request, resp *http.Response, givenErr error) error {
	// use default error handler to identify successful or failed request
	handledErr := httputil.ErrorHandlerSafe()(req, resp, givenErr)
	if handledErr == nil {
		return nil
	}

	// if we have no response at all, just return the error immediately
	if resp == nil {
		return handledErr
	}

	// in case of a failure, lets see if we can extract the error message from the response body
	respBody, err := io.ReadAll(resp.Body)
	if err == nil {
		var er responseError
		err = json.Unmarshal(respBody, &er)
		if err == nil && er.Body != "" {
			return er
		}
	}

	return handledErr
}
