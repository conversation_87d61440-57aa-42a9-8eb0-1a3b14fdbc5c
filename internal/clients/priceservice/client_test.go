package priceservice

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/gofrs/uuid"
	"github.com/h2non/gock"
	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/stretchr/testify/assert"
)

var (
	customerID     int64 = 123456789
	subscriptionID int64 = 987654321
	customerUUID         = uuid.Must(uuid.NewV4())
	planID               = uuid.Must(uuid.NewV4())
	skus1                = []string{"US-SKU-003"}
	skus2                = []string{"US-SKU-005", "US-SKU-006"}
	voucherCode          = "ABC123"
	country              = "US"
	postcode             = "12345"
)

// nolint: paralleltest // Gock is not parallelizable
func TestClient_Calculate(t *testing.T) {
	gock.Observe(gock.DumpRequest)
	defer gock.Off()

	context := t.Context()

	type args struct {
		params    CalculateParams
		setupGock func(
			bool,
			[]string,
			string,
			int64,
			uuid.UUID,
			uuid.UUID,
			int64,
			string,
			string,
		)
	}
	tests := []struct {
		name    string
		args    args
		want    *Response
		wantErr string
	}{
		{
			name: "Success, order is free",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus1,
					VoucherCode:    voucherCode,
					CustomerID:     customerID,
					CustomerUUID:   customerUUID,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					customerID int64,
					customerUUID uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusOK).
						BodyString(`{"grandTotal":0}`)
				},
			},
			want: &Response{
				GrandTotal: 0,
			},
		},
		{
			name: "Success, empty customerID",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus1,
					VoucherCode:    voucherCode,
					CustomerID:     int64(0),
					CustomerUUID:   customerUUID,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					_ int64,
					customerUUID uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusOK).
						BodyString(`{"grandTotal":0}`)
				},
			},
			want: &Response{
				GrandTotal: 0,
			},
		},
		{
			name: "Success, empty subscriptionID",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus1,
					VoucherCode:    voucherCode,
					CustomerID:     customerID,
					CustomerUUID:   customerUUID,
					PlanID:         planID,
					SubscriptionID: int64(0),
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					customerID int64,
					customerUUID uuid.UUID,
					planID uuid.UUID,
					_ int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerID, customerUUID.String(), planID.String(), country, !isFirstOrder, postcode)).
						Reply(http.StatusOK).
						BodyString(`{"grandTotal":0}`)
				},
			},
			want: &Response{
				GrandTotal: 0,
			},
		},
		{
			name: "Success, empty customerUUID",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus1,
					VoucherCode:    voucherCode,
					CustomerID:     customerID,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					customerID int64,
					_ uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerID, planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusOK).
						BodyString(`{"grandTotal":0}`)
				},
			},
			want: &Response{
				GrandTotal: 0,
			},
		},
		{
			name: "Success, nil customerUUID",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus1,
					VoucherCode:    voucherCode,
					CustomerID:     customerID,
					CustomerUUID:   uuid.Nil,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					customerID int64,
					_ uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerID, planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusOK).
						BodyString(`{"grandTotal":0}`)
				},
			},
			want: &Response{
				GrandTotal: 0,
			},
		},
		{
			name: "Success, empty voucherCode",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus1,
					CustomerID:     customerID,
					CustomerUUID:   customerUUID,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					_ string,
					customerID int64,
					customerUUID uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusOK).
						BodyString(`{"grandTotal":0}`)
				},
			},
			want: &Response{
				GrandTotal: 0,
			},
		},
		{
			name: "Success, order is paid",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus1,
					VoucherCode:    voucherCode,
					CustomerID:     customerID,
					CustomerUUID:   customerUUID,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					customerID int64,
					customerUUID uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusOK).
						BodyString(`{"grandTotal":100.00}`)
				},
			},
			want: &Response{
				GrandTotal: 100,
			},
		},
		{
			name: "Success, multiple SKUs",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus2,
					VoucherCode:    voucherCode,
					CustomerID:     customerID,
					CustomerUUID:   customerUUID,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					customerID int64,
					customerUUID uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusInternalServerError).
						BodyString("some error")

					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[1], voucherCode, customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusOK).
						BodyString(`{"grandTotal":0}`)
				},
			},
			want: &Response{
				GrandTotal: 0,
			},
		},
		{
			name: "Success, multiple SKUs, addon error",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus2,
					VoucherCode:    voucherCode,
					CustomerID:     customerID,
					CustomerUUID:   customerUUID,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					customerID int64,
					customerUUID uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusInternalServerError).
						BodyString(`{"error": "unable to create calculable object with addon price: new-calc-obj.apply-surcharges: [subscription: 0] chargecalculator.getcharges: hfWeek must be provided for chargeable handle: US-AMY-0-0-0"}`)

					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[1], voucherCode, customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusOK).
						BodyString(`{"grandTotal":0}`)
				},
			},
			want: &Response{
				GrandTotal: 0,
			},
		},
		{
			name: "Success, multiple SKUs, multiple addon errors",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus2,
					VoucherCode:    voucherCode,
					CustomerID:     customerID,
					CustomerUUID:   customerUUID,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					customerID int64,
					customerUUID uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusInternalServerError).
						BodyString(`{"error": "unable to create calculable object with addon price: new-calc-obj.apply-surcharges: [subscription: 0] chargecalculator.getcharges: hfWeek must be provided for chargeable handle: US-AMY-0-0-0"}`)

					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[1], voucherCode, customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusInternalServerError).
						BodyString(`{"error": "unable to create calculable object with addon price: new-calc-obj.apply-surcharges: [subscription: 0] chargecalculator.getcharges: hfWeek must be provided for chargeable handle: US-CHARGE-0-0-0"}`)
				},
			},
			wantErr: "failed to calculate price: failed calling price service API: unable to create calculable object with addon price: new-calc-obj.apply-surcharges: [subscription: 0] chargecalculator.getcharges: hfWeek must be provided for chargeable handle: US-AMY-0-0-0: failed calling price service API: unable to create calculable object with addon price: new-calc-obj.apply-surcharges: [subscription: 0] chargecalculator.getcharges: hfWeek must be provided for chargeable handle: US-CHARGE-0-0-0",
		},
		{
			name: "failed request",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus1,
					VoucherCode:    voucherCode,
					CustomerID:     customerID,
					CustomerUUID:   customerUUID,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					customerID int64,
					customerUUID uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusInternalServerError).
						BodyString("some error")
				},
			},
			wantErr: "failed to calculate price: failed calling price service API: received unexpected status code: 500 for https://price-service.example/calculate",
		},
		{
			name: "failed request empty body",
			args: args{
				params: CalculateParams{
					Ctx:            context,
					IsFirstOrder:   false,
					SKUs:           skus1,
					VoucherCode:    voucherCode,
					CustomerID:     customerID,
					CustomerUUID:   customerUUID,
					PlanID:         planID,
					SubscriptionID: subscriptionID,
					Country:        country,
					Postcode:       postcode,
				},
				setupGock: func(
					isFirstOrder bool,
					skus []string,
					voucherCode string,
					customerID int64,
					customerUUID uuid.UUID,
					planID uuid.UUID,
					subscriptionID int64,
					country string,
					postcode string,
				) {
					gock.New("https://price-service.example").
						Post("/calculate").
						JSON(fmt.Sprintf(`{"products":[{"handle":"%s"}],"couponCode":"%s","customerID":%d,"customerUUID":"%s","planID":"%s","subscriptionID":%d,"country":"%s","isRecurring":%t,"shippingAddress":{"postcode":"%s"}}`, skus[0], voucherCode, customerID, customerUUID.String(), planID.String(), subscriptionID, country, !isFirstOrder, postcode)).
						Reply(http.StatusInternalServerError)
				},
			},
			wantErr: "failed to calculate price: failed calling price service API: received unexpected status code: 500 for https://price-service.example/calculate",
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			baseURL := "https://price-service.example"
			c := &Client{
				httputil.NewClient(
					httputil.WithHTTPClient(
						&http.Client{
							Transport: gock.DefaultTransport,
						},
					),
				),
				baseURL,
			}

			tt.args.setupGock(
				tt.args.params.IsFirstOrder,
				tt.args.params.SKUs,
				tt.args.params.VoucherCode,
				tt.args.params.CustomerID,
				tt.args.params.CustomerUUID,
				tt.args.params.PlanID,
				tt.args.params.SubscriptionID,
				tt.args.params.Country,
				tt.args.params.Postcode,
			)

			got, errs := c.Calculate(tt.args.params)
			if tt.wantErr == "" {
				assert.NoError(t, errs)
				assert.Equal(t, tt.want, got)
			} else {
				assert.EqualError(t, errs, tt.wantErr)
			}

			assert.True(t, gock.IsDone())
		})
	}
}

func Test_buildRequestBody(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name string
		args buildRequestBodyParams
		want RequestBody
	}{
		{
			name: "success",
			args: buildRequestBodyParams{
				IsFirstOrder:   true,
				SKU:            skus1[0],
				VoucherCode:    voucherCode,
				CustomerID:     customerID,
				CustomerUUID:   customerUUID,
				PlanID:         planID,
				SubscriptionID: subscriptionID,
				Country:        country,
				Postcode:       postcode,
			},
			want: RequestBody{
				Country:        country,
				CouponCode:     voucherCode,
				CustomerID:     customerID,
				CustomerUUID:   customerUUID.String(),
				PlanID:         planID.String(),
				SubscriptionID: subscriptionID,
				IsRecurring:    false,
				ShippingAddress: ShippingAddress{
					Postcode: postcode,
				},
				Products: []Products{{Handle: skus1[0]}},
			},
		},
		{
			name: "success with empty sku",
			args: buildRequestBodyParams{
				IsFirstOrder:   true,
				VoucherCode:    voucherCode,
				CustomerID:     customerID,
				CustomerUUID:   customerUUID,
				PlanID:         planID,
				SubscriptionID: subscriptionID,
				Country:        country,
				Postcode:       postcode,
			},
			want: RequestBody{
				Country:        country,
				CouponCode:     voucherCode,
				CustomerID:     customerID,
				CustomerUUID:   customerUUID.String(),
				PlanID:         planID.String(),
				SubscriptionID: subscriptionID,
				IsRecurring:    false,
				ShippingAddress: ShippingAddress{
					Postcode: postcode,
				},
			},
		},
		{
			name: "success with empty postcode",
			args: buildRequestBodyParams{
				IsFirstOrder:   true,
				SKU:            skus1[0],
				VoucherCode:    voucherCode,
				CustomerID:     customerID,
				CustomerUUID:   customerUUID,
				PlanID:         planID,
				SubscriptionID: subscriptionID,
				Country:        country,
				Postcode:       "",
			},
			want: RequestBody{
				Country:        country,
				CouponCode:     voucherCode,
				CustomerID:     customerID,
				CustomerUUID:   customerUUID.String(),
				PlanID:         planID.String(),
				SubscriptionID: subscriptionID,
				IsRecurring:    false,
				ShippingAddress: ShippingAddress{
					Postcode: "",
				},
				Products: []Products{{Handle: skus1[0]}},
			},
		},
		{
			name: "success with empty customerID",
			args: buildRequestBodyParams{
				IsFirstOrder:   true,
				SKU:            skus1[0],
				VoucherCode:    voucherCode,
				CustomerUUID:   customerUUID,
				PlanID:         planID,
				SubscriptionID: subscriptionID,
				Country:        country,
				Postcode:       postcode,
			},
			want: RequestBody{
				Country:        country,
				CouponCode:     voucherCode,
				CustomerID:     int64(0),
				CustomerUUID:   customerUUID.String(),
				PlanID:         planID.String(),
				SubscriptionID: subscriptionID,
				IsRecurring:    false,
				ShippingAddress: ShippingAddress{
					Postcode: postcode,
				},
				Products: []Products{{Handle: skus1[0]}},
			},
		},
		{
			name: "success with empty subscriptionID",
			args: buildRequestBodyParams{
				IsFirstOrder: true,
				SKU:          skus1[0],
				VoucherCode:  voucherCode,
				CustomerID:   customerID,
				CustomerUUID: customerUUID,
				PlanID:       planID,
				Country:      country,
				Postcode:     postcode,
			},
			want: RequestBody{
				Country:        country,
				CouponCode:     voucherCode,
				CustomerID:     customerID,
				CustomerUUID:   customerUUID.String(),
				PlanID:         planID.String(),
				SubscriptionID: int64(0),
				IsRecurring:    false,
				ShippingAddress: ShippingAddress{
					Postcode: postcode,
				},
				Products: []Products{{Handle: skus1[0]}},
			},
		},
		{
			name: "success with empty customer uuid",
			args: buildRequestBodyParams{
				IsFirstOrder:   true,
				SKU:            skus1[0],
				VoucherCode:    voucherCode,
				CustomerID:     customerID,
				PlanID:         planID,
				SubscriptionID: subscriptionID,
				Country:        country,
				Postcode:       postcode,
			},
			want: RequestBody{
				Country:        country,
				CustomerID:     customerID,
				PlanID:         planID.String(),
				SubscriptionID: subscriptionID,
				CouponCode:     voucherCode,
				IsRecurring:    false,
				ShippingAddress: ShippingAddress{
					Postcode: postcode,
				},
				Products: []Products{{Handle: skus1[0]}},
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			got := buildRequestBody(tt.args)

			assert.Equal(t, tt.want, got)
		})
	}
}
