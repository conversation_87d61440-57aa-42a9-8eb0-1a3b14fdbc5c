package priceservice

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/hellofresh/growth-go-kit/observability"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// Client represents the price service client
type Client struct {
	httpClient *httputil.Client
	baseURL    string
}

// NewClient creates an instance of a price service client
func NewClient(baseURL string) *Client {
	baseURL = strings.TrimRight(baseURL, "/")
	return &Client{
		httputil.NewClient(
			httputil.WithHTTPClient(
				&http.Client{
					Transport: observability.NewHTTPTransport(),
				},
			),
		),
		baseURL,
	}
}

// RequestBody is an object that used to build request
type RequestBody struct {
	Products        []Products      `json:"products"`
	CouponCode      string          `json:"couponCode,omitempty"`     // nolint:tagliatelle  // json tag should be in camel case
	CustomerID      int64           `json:"customerID,omitempty"`     // nolint:tagliatelle  // json tag should be in camel case
	CustomerUUID    string          `json:"customerUUID,omitempty"`   // nolint:tagliatelle  // json tag should be in camel case
	PlanID          string          `json:"planID,omitempty"`         // nolint:tagliatelle  // json tag should be in camel case
	SubscriptionID  int64           `json:"subscriptionID,omitempty"` // nolint:tagliatelle  // json tag should be in camel case
	Country         string          `json:"country"`
	IsRecurring     bool            `json:"isRecurring"`
	ShippingAddress ShippingAddress `json:"shippingAddress"`
}

// Products is a part of a request body
type Products struct {
	Handle string `json:"handle"`
}

// ShippingAddress is a part of a request body
type ShippingAddress struct {
	Postcode string `json:"postcode"`
}

// Response is struct that holds the response of calculate endpoint
type Response struct {
	GrandTotal              float64 `json:"grandTotal"`              // nolint:tagliatelle  // json tag should be in camel case
	DiscountAmount          float64 `json:"discountAmount"`          // nolint:tagliatelle  // json tag should be in camel case
	RemainingDiscountAmount float64 `json:"remainingDiscountAmount"` // nolint:tagliatelle  // json tag should be in camel case
	ShippingDiscountAmount  float64 `json:"shippingDiscountAmount"`  // nolint:tagliatelle  // json tag should be in camel case
	CouponCode              string  `json:"couponCode"`              // nolint:tagliatelle  // json tag should be in camel case
}

// CalculateParams groups all parameters for the Calculate function.
type CalculateParams struct {
	Ctx            context.Context
	AuthHeader     string
	IsFirstOrder   bool
	SKUs           []string
	VoucherCode    string
	CustomerID     int64
	CustomerUUID   uuid.UUID
	PlanID         uuid.UUID
	SubscriptionID int64
	Country        string
	Postcode       string
}

// Calculate calls price service to calculate price for the product
func (c *Client) Calculate(params CalculateParams) (*Response, error) {
	ctx, span := observability.StartSpan(params.Ctx, "client.priceservice.Calculate")
	defer span.End()

	var calculateErrors CalculateErrors
	for _, sku := range params.SKUs {
		reqBody := buildRequestBody(buildRequestBodyParams{
			IsFirstOrder:   params.IsFirstOrder,
			SKU:            sku,
			VoucherCode:    params.VoucherCode,
			CustomerID:     params.CustomerID,
			CustomerUUID:   params.CustomerUUID,
			PlanID:         params.PlanID,
			SubscriptionID: params.SubscriptionID,
			Country:        params.Country,
			Postcode:       params.Postcode,
		})
		url := c.baseURL + "/calculate"

		var resp Response
		err := c.httpClient.PostJSON(
			ctx,
			url,
			reqBody,
			&resp,
			httputil.WithRequestErrorHandler(errorHandler),
			httputil.WithHeader("Authorization", params.AuthHeader),
		)
		if err != nil {
			calculateErrors = append(calculateErrors, fmt.Errorf("failed calling price service API: %w", err))
			continue
		}

		trace.SpanFromContext(ctx).SetStatus(
			codes.Ok,
			"price calculated successfully",
		)

		return &resp, nil
	}

	return nil, span.Fail(calculateErrors)
}

// buildRequestBodyParams is a struct that holds the parameters for building request body
type buildRequestBodyParams struct {
	IsFirstOrder   bool
	SKU            string
	VoucherCode    string
	CustomerID     int64
	CustomerUUID   uuid.UUID
	PlanID         uuid.UUID
	SubscriptionID int64
	Country        string
	Postcode       string
}

func buildRequestBody(params buildRequestBodyParams) RequestBody {
	b := RequestBody{
		Country:     params.Country,
		IsRecurring: !params.IsFirstOrder,
		ShippingAddress: ShippingAddress{
			Postcode: params.Postcode,
		},
		PlanID: params.PlanID.String(),
	}

	if params.CustomerID != int64(0) {
		b.CustomerID = params.CustomerID
	}

	if params.SubscriptionID != int64(0) {
		b.SubscriptionID = params.SubscriptionID
	}

	if params.CustomerUUID != uuid.Nil {
		b.CustomerUUID = params.CustomerUUID.String()
	}
	if params.VoucherCode != "" {
		b.CouponCode = params.VoucherCode
	}

	if params.SKU != "" {
		b.Products = []Products{{Handle: params.SKU}}
	}

	return b
}
