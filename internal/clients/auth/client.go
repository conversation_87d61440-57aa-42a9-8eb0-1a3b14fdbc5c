package auth

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/hellofresh/growth-go-kit/observability"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// Client is used to call Auth Service
type Client struct {
	authServiceBaseURL string
	credentials        Credentials
	httpClient         *httputil.Client
	token              string
	expiration         time.Time
}

// Credentials credentials that auth service is called with
type Credentials struct {
	Password     string `json:"password"`
	Country      string `json:"country"`
	Scope        string `json:"scope"`
	GrantType    string `json:"grant_type"`
	Username     string `json:"username"`
	ClientSecret string `json:"client_secret"`
	ClientID     string `json:"client_id"`
}

// TokenResponse response from the /token endpoint
type TokenResponse struct {
	AccessToken  string      `json:"access_token"`
	ExpiresIn    int         `json:"expires_in"`
	RefreshToken string      `json:"refresh_token"`
	Scope        string      `json:"scope"`
	TokenType    string      `json:"token_type"`
	UserData     interface{} `json:"_"`
}

// NewClient creates a new Auth Service Client
func NewClient(authServiceBaseURL string, credentials Credentials) *Client {
	return &Client{
		authServiceBaseURL: strings.TrimSuffix(authServiceBaseURL, "/"),
		credentials:        credentials,
		httpClient: httputil.NewClient(
			httputil.WithHTTPClient(
				&http.Client{
					Transport: observability.NewHTTPTransport(),
				},
			),
		),
	}
}

// Token returns a valid auth token from voucher service, includes refresh if expired
func (c *Client) Token(ctx context.Context) (string, error) {
	ctx, span := observability.StartSpan(ctx, "client.authservice.Token")
	defer span.End()

	if time.Now().After(c.expiration) {
		trace.SpanFromContext(ctx).SetStatus(
			codes.Ok,
			"token expired, requesting new token",
		)

		err := c.getToken(ctx)
		if err != nil {
			return "", span.Fail(fmt.Errorf("unable to get authorization token: %w", err))
		}
	}

	trace.SpanFromContext(ctx).SetStatus(
		codes.Ok,
		"token returned from memory",
	)

	return c.token, nil
}

func (c *Client) getToken(ctx context.Context) error {
	ctx, span := observability.StartSpan(ctx, "client.authservice.getToken")
	defer span.End()

	requestBody := c.credentials
	url := fmt.Sprintf("%s/token", c.authServiceBaseURL)

	var tokenResponse TokenResponse
	err := c.httpClient.PostJSON(
		ctx,
		url,
		requestBody,
		&tokenResponse,
	)
	if err != nil {
		return span.Fail(fmt.Errorf("failed calling auth service API: %w", err))
	}

	c.token = fmt.Sprintf("%s %s", tokenResponse.TokenType, tokenResponse.AccessToken)
	c.expiration = time.Now().Add(time.Second * time.Duration(tokenResponse.ExpiresIn))

	trace.SpanFromContext(ctx).SetStatus(
		codes.Ok,
		"token received successfully",
	)

	return nil
}

// Noop noop object for mocking
type Noop struct {
}

// NewNoop noop object for mocking
func NewNoop() *Noop {
	return &Noop{}
}

// Token noop func for mocking
func (c *Noop) Token(ctx context.Context) (string, error) {
	return "", nil
}
