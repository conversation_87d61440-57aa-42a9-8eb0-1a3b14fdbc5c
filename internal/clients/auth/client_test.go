package auth

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/stretchr/testify/assert"
)

func TestClient_getToken(t *testing.T) {
	type args struct {
		credentials Credentials
		status      int
	}
	type expectation struct {
		token string
		body  string
	}
	tests := []struct {
		name     string
		args     args
		expected expectation
		wantErr  bool
	}{
		{
			name: "Success",
			expected: expectation{
				body:  `{"token_type":"Bearer", "access_token": "jwt-token-hash", "expires_in":123}`,
				token: "Bearer jwt-token-hash",
			},
			args: args{
				status: 200,
			},
			wantErr: false,
		},
		{
			name: "Failed request",
			args: args{
				status: 200,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCtrl := gomock.NewController(t)
			defer mockCtrl.Finish()

			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.Write<PERSON>eader(tt.args.status)
				if _, err := w.Write([]byte(tt.expected.body)); err != nil {
					t.Error(err)
				}
			}))

			c := NewClient(server.URL, tt.args.credentials)

			if err := c.getToken(context.Background()); (err != nil) != tt.wantErr {
				t.Errorf("getToken() error = %v, wantErr %v", err, tt.wantErr)
			} else {
				assert.Equal(t, tt.expected.token, c.token)
			}
		})
	}
}

func TestClient_Token(t *testing.T) {
	type args struct {
		credentials Credentials
		oldToken    string
		status      int
		expiration  time.Time
	}
	type expectation struct {
		wantErr   bool
		wantToken string
		body      string
	}
	tests := []struct {
		name     string
		args     args
		expected expectation
	}{
		{
			name: "Success, old token has expired, expecting new",
			expected: expectation{
				body:      `{"token_type":"Bearer", "access_token": "jwt-token-hash", "expires_in":123}`,
				wantToken: "Bearer jwt-token-hash",
				wantErr:   false,
			},
			args: args{
				status:   http.StatusOK,
				oldToken: "Bearer OLD",
			},
		},
		{
			name: "Success, old token still active, expecting old",
			expected: expectation{
				wantToken: "Bearer OLD",
				wantErr:   false,
			},
			args: args{
				oldToken:   "Bearer OLD",
				expiration: time.Unix(2102676136, 0),
			},
		},
		{
			name: "Failed request",
			expected: expectation{
				wantErr: true,
			},
			args: args{
				status: http.StatusInternalServerError,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockCtrl := gomock.NewController(t)
			defer mockCtrl.Finish()

			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(tt.args.status)
				if _, err := w.Write([]byte(tt.expected.body)); err != nil {
					t.Error(err)
				}
			}))

			httpClient := &http.Client{Transport: server.Client().Transport}

			c := &Client{
				authServiceBaseURL: server.URL,
				credentials:        tt.args.credentials,
				httpClient:         httputil.NewClient(httputil.WithHTTPClient(httpClient)),
				token:              tt.args.oldToken,
				expiration:         tt.args.expiration,
			}

			got, err := c.Token(context.Background())
			if (err != nil) != tt.expected.wantErr {
				t.Errorf("Token() error = %v, wantErr %v", err, tt.expected.wantErr)
				return
			}
			if got != tt.expected.wantToken && c.token == tt.expected.wantToken {
				t.Errorf("Token() got = %v, want %v", got, tt.expected.wantToken)
			}
		})
	}
}
