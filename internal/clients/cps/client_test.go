package cps

import (
	"context"
	"fmt"
	"net/http"
	"reflect"
	"testing"

	"github.com/gofrs/uuid"
	"github.com/h2non/gock"
	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/stretchr/testify/assert"
)

// nolint: paralleltest // Gock is not parallelizable
func TestClient_Cancellation(t *testing.T) {
	defer gock.Off()

	type args struct {
		status       int
		PlanID       uuid.UUID
		responseBody []byte
	}
	tests := []struct {
		name    string
		args    args
		want    bool
		wantErr bool
	}{
		{
			name: "Successful request, plan is cancelled",
			args: args{
				status: http.StatusOK,
				PlanID: uuid.Must(uuid.NewV4()),
				responseBody: []byte(`{
  "id": "e31a4ca0-17a7-4deb-95ea-03e6ce212580",
  "canceledAt": "2022-06-10T08:42:58.185511Z"
}`),
			},
			want:    true,
			wantErr: false,
		},
		{
			name: "Successful request, plan is not cancelled",
			args: args{
				status: http.StatusBadRequest,
				PlanID: uuid.Must(uuid.NewV4()),
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "Failed request",
			args: args{
				status: http.StatusInternalServerError,
				PlanID: uuid.Must(uuid.NewV4()),
			},
			want:    false,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			gock.New("https://customer-plans-service.example").
				Post(fmt.Sprintf("/plans/%s/cancel", tt.args.PlanID)).
				Reply(tt.args.status).
				BodyString(string(tt.args.responseBody))

			baseURL := "https://customer-plans-service.example"
			c := &Client{
				httputil.NewClient(
					httputil.WithHTTPClient(
						&http.Client{
							Timeout:   httpClientTimeout,
							Transport: gock.DefaultTransport,
						},
					),
				),
				baseURL,
			}

			got, err := c.Cancellation(context.Background(), tt.args.PlanID, false)
			if (err != nil) != tt.wantErr {
				t.Errorf("Cancellation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Cancellation() got = %v, want %v", got, tt.want)
			}

			assert.True(t, gock.IsDone())
		})
	}
}
