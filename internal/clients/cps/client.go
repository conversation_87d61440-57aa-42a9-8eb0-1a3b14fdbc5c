package cps

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/hellofresh/growth-go-kit/observability"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

const httpClientTimeout = 1 * time.Second

// Client struct implements canceller interface
type Client struct {
	httpClient *httputil.Client
	baseURL    string
}

// NewClient returns http-client with circuit breaker  for customer plan service
func NewClient(baseURL string) *Client {
	// make baseURL always NOT have a trailing slash so whether we are given one or not the request will be correct
	baseURL = strings.TrimRight(baseURL, "/")
	return &Client{
		httputil.NewClient(
			httputil.WithHTTPClient(
				&http.Client{
					Timeout:   httpClientTimeout,
					Transport: observability.NewHTTPTransport(),
				},
			),
		),
		baseURL,
	}
}

// CancelRequest represents a plan cancellation request body
type CancelRequest struct {
	FinalDeliveryWeek   string                     `json:"finalDeliveryWeek" valid:"validateFinalDeliveryWeek~Invalid final delivery week"`
	CancellationOptions CancellationOptionsPayload `json:"cancellationOptions"`
}

// CancellationOptionsPayload represents a plan cancellation options payload
type CancellationOptionsPayload struct {
	ExcludeFromReactivation bool `json:"excludeFromReactivation"`
	ExcludeFromNotification bool `json:"excludeFromNotification"`
	CancelOrders            bool `json:"cancelOrders"`
}

// CancelResponse represents cancellation response body
type CancelResponse struct {
	ID         uuid.UUID `json:"id"`
	CanceledAt time.Time `json:"canceledAt"`
}

// Cancellation performs plans cancellation through CPS
func (c *Client) Cancellation(ctx context.Context, planID uuid.UUID, cancelOrders bool) (bool, error) {
	ctx, span := observability.StartSpan(ctx, "client.cps.Cancellation")
	defer span.End()

	reqBody := buildBody(cancelOrders)
	url := c.baseURL + fmt.Sprintf("/plans/%s/cancel", planID.String())

	var cancelResponse CancelResponse
	err := c.httpClient.PostJSON(
		ctx,
		url,
		reqBody,
		&cancelResponse,
	)
	if err != nil {
		return false, span.Fail(fmt.Errorf("failed calling customer plans service API: %w", err))
	}

	if cancelResponse.CanceledAt.IsZero() {
		err = errors.New("plan was not cancelled for unknown reason")
		return false, span.Fail(err)
	}

	trace.SpanFromContext(ctx).SetStatus(
		codes.Ok,
		"cancellation call performed successfully",
	)

	return true, nil
}

func buildBody(cancelOrders bool) *CancelRequest {
	return &CancelRequest{
		CancellationOptions: CancellationOptionsPayload{
			CancelOrders: cancelOrders,
		},
	}
}
