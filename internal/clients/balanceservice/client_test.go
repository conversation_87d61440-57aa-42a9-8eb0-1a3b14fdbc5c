package balanceservice

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gofrs/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestClient_GetBalance_Success(t *testing.T) {
	t.<PERSON>()

	// Create a test server
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Verify the request
		expectedPath := "/payments/customers/550e8400-e29b-41d4-a716-************/balance"
		assert.Equal(t, expectedPath, r.URL.Path)
		assert.Equal(t, "GET", r.Method)

		// Return mock response
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		_, err := w.Write([]byte(`{
			"amount": 1500,
			"currency_code": "USD",
			"bonus": 500,
			"cash": 1000
		}`))
		require.NoError(t, err)
	}))
	defer server.Close()

	// Create client
	client := NewClient(server.URL)

	// Test the method
	customerUUID := uuid.FromStringOrNil("550e8400-e29b-41d4-a716-************")
	ctx := t.Context()

	balance, err := client.GetBalance(ctx, customerUUID)

	// Assertions
	require.NoError(t, err)
	assert.NotNil(t, balance)
	assert.Equal(t, int64(1500), balance.Amount)
	assert.Equal(t, "USD", balance.CurrencyCode)
	assert.Equal(t, int64(500), balance.Bonus)
	assert.Equal(t, int64(1000), balance.Cash)
}

func TestClient_GetBalance_NotFound(t *testing.T) {
	t.Parallel()

	// Create a test server that returns 404
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, _ *http.Request) {
		w.WriteHeader(http.StatusNotFound)
		_, err := w.Write([]byte(`{"error": "Customer not found"}`))
		require.NoError(t, err)
	}))
	defer server.Close()

	// Create client
	client := NewClient(server.URL)

	// Test the method
	customerUUID := uuid.FromStringOrNil("550e8400-e29b-41d4-a716-************")
	ctx := t.Context()

	balance, err := client.GetBalance(ctx, customerUUID)

	// Assertions
	assert.Error(t, err)
	assert.Nil(t, balance)
	assert.ErrorIs(t, err, ErrNotFound)
}

func TestClient_prepareURL(t *testing.T) {
	t.Parallel()

	client := NewClient("https://balance-service.example.com")
	customerUUID := uuid.FromStringOrNil("550e8400-e29b-41d4-a716-************")

	url := client.prepareURL(customerUUID)

	expected := "https://balance-service.example.com/payments/customers/550e8400-e29b-41d4-a716-************/balance"
	assert.Equal(t, expected, url)
}

func TestClient_prepareURL_WithTrailingSlash(t *testing.T) {
	t.Parallel()

	client := NewClient("https://balance-service.example.com/")
	customerUUID := uuid.FromStringOrNil("550e8400-e29b-41d4-a716-************")

	url := client.prepareURL(customerUUID)

	expected := "https://balance-service.example.com/payments/customers/550e8400-e29b-41d4-a716-************/balance"
	assert.Equal(t, expected, url)
}
