//go:generate mockgen -source client.go -destination balance_mock.go -package balanceservice

// Package balanceservice provides a client for interacting with the balance service API.
package balanceservice

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/gofrs/uuid"
	"github.com/hellofresh/growth-go-kit/net/http/httputil"
	"github.com/hellofresh/growth-go-kit/observability"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/trace"
)

// ErrNotFound error type for the 404 response
var ErrNotFound = errors.New("balance not found")

// ErrNoBalance sentinel error for when no balance is available (used in Noop)
var ErrNoBalance = errors.New("no balance available")

// BalanceService defines method to be able to get balance details
type BalanceService interface {
	GetBalance(ctx context.Context, customerUUID uuid.UUID) (*BalanceResponse, error)
}

// Client represents the balance service client
type Client struct {
	httpClient *httputil.Client
	baseURL    string
}

// NewClient creates a new balance service client
func NewClient(baseURL string) *Client {
	baseURL = strings.TrimRight(baseURL, "/")
	return &Client{
		httputil.NewClient(
			httputil.WithHTTPClient(
				&http.Client{
					Transport: observability.NewHTTPTransport(),
				},
			),
		),
		baseURL,
	}
}

// BalanceResponse response from the balance service
type BalanceResponse struct {
	Amount       int64  `json:"amount"`        // Total balance (cash + bonus)
	CurrencyCode string `json:"currency_code"` // Currency code (e.g., "USD", "EUR")
	Bonus        int64  `json:"bonus"`         // Total bonus amount
	Cash         int64  `json:"cash"`          // Total cash amount
}

// GetBalance returns balance details from the balance service
func (c *Client) GetBalance(ctx context.Context, customerUUID uuid.UUID) (*BalanceResponse, error) {
	ctx, span := observability.StartSpan(ctx, "clients.balanceservice.GetBalance")
	defer span.End()

	url := c.prepareURL(customerUUID)

	var balanceResponse BalanceResponse
	err := c.httpClient.GetJSON(
		ctx,
		url,
		nil,
		&balanceResponse,
	)
	if err != nil {
		var unexpectedStatusCodeError *httputil.UnexpectedStatusCodeError
		if errors.As(err, &unexpectedStatusCodeError) && unexpectedStatusCodeError.StatusCode == http.StatusNotFound {
			return nil, span.Fail(errors.Join(ErrNotFound, err))
		}

		return nil, span.Fail(fmt.Errorf("failed calling balance service API: %w", err))
	}

	trace.SpanFromContext(ctx).SetStatus(
		codes.Ok,
		"got balance details from balance service",
	)

	return &balanceResponse, nil
}

func (c *Client) prepareURL(customerUUID uuid.UUID) string {
	return fmt.Sprintf("%s/payments/customers/%s/balance", c.baseURL, customerUUID.String())
}

// Noop noop object for mocking
type Noop struct{}

// NewNoop noop object for mocking
func NewNoop() *Noop {
	return &Noop{}
}

// GetBalance noop func for mocking
func (c *Noop) GetBalance(_ context.Context, _ uuid.UUID) (*BalanceResponse, error) {
	return nil, ErrNoBalance
}
