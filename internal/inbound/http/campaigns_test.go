package http

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"github.com/hellofresh/credit-autocancel-service/internal/app"
	"github.com/hellofresh/credit-autocancel-service/internal/campaign"
	"github.com/hellofresh/credit-autocancel-service/internal/domain"
	"github.com/hellofresh/credit-autocancel-service/internal/payload"
)

func TestCampaignsHandler_Handle(t *testing.T) {
	setGlobalConfigEnv()
	createCampaignSuccessPayload := `{
   "campaign_ids": ["d7052649-d227-4e64-9448-ed38621b803f"],
   "active": true,
   "country": "us",
   "campaign_type": "CANCEL_ANY"
}`
	createCampaignMissingCampaignTypePayload := `{
    "campaign_ids": ["d7052649-d227-4e64-9448-ed38621b803f"],
    "active": true,
    "country": "us"
}`
	createCampaignMissingActiveStatusPayload := `{
    "campaign_ids": ["d7052649-d227-4e64-9448-ed38621b803f"],
    "country": "us",
    "campaign_type": "CANCEL_ANY"
}`
	createCampaignMissingCampaignIDPayload := `{
    "active": true,
    "country": "us",
    "campaign_type": "CANCEL_ANY"
}`
	emptyPayload := `{}`
	type fields struct {
		context          context.Context
		campaignsService *domain.MockCampaignsService
		cfg              *app.Configuration
	}
	tests := []struct {
		name           string
		expected       func(f fields)
		request        string
		wantStatusCode int
	}{
		{
			name: "success",
			expected: func(f fields) {
				createCampaignSuccessPayloadStruct, err := payload.NewCampaignsPayload([]byte(createCampaignSuccessPayload))
				assert.NoError(t, err)
				config, err := loadConfig()
				assert.NoError(t, err)
				countryExists := contains(config.CountryCodes, createCampaignSuccessPayloadStruct.Country)
				assert.True(t, countryExists, true)
				f.campaignsService.EXPECT().CreateCampaigns(gomock.Any(), *createCampaignSuccessPayloadStruct).Return(nil)
			},
			request:        createCampaignSuccessPayload,
			wantStatusCode: http.StatusOK,
		},
		{
			name: "good request, database insert error",
			expected: func(f fields) {
				createCampaignSuccessPayloadStruct, err := payload.NewCampaignsPayload([]byte(createCampaignSuccessPayload))
				assert.NoError(t, err)
				config, err := loadConfig()
				assert.NoError(t, err)
				countryExists := contains(config.CountryCodes, createCampaignSuccessPayloadStruct.Country)
				assert.True(t, countryExists, true)
				f.campaignsService.EXPECT().CreateCampaigns(gomock.Any(), *createCampaignSuccessPayloadStruct).Return(errors.New("generic database error"))
			},
			request:        createCampaignSuccessPayload,
			wantStatusCode: http.StatusBadRequest,
		},
		{
			name: "success, empty campaign type",
			expected: func(f fields) {
				createCampaignSuccessPayloadStruct, err := payload.NewCampaignsPayload([]byte(createCampaignSuccessPayload))
				assert.NoError(t, err)
				config, err := loadConfig()
				assert.NoError(t, err)
				countryExists := contains(config.CountryCodes, createCampaignSuccessPayloadStruct.Country)
				assert.True(t, countryExists, true)
				f.campaignsService.EXPECT().CreateCampaigns(gomock.Any(), *createCampaignSuccessPayloadStruct).Return(nil)
			},
			request:        createCampaignMissingCampaignTypePayload,
			wantStatusCode: http.StatusOK,
		},
		{
			name:           "missing active status",
			expected:       func(_ fields) {},
			request:        createCampaignMissingActiveStatusPayload,
			wantStatusCode: http.StatusBadRequest,
		},
		{
			name:           "missing campaign IDs",
			expected:       func(_ fields) {},
			request:        createCampaignMissingCampaignIDPayload,
			wantStatusCode: http.StatusBadRequest,
		},
		{
			name:           "empty payload",
			expected:       func(_ fields) {},
			request:        emptyPayload,
			wantStatusCode: http.StatusBadRequest,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			campaignsService := domain.NewMockCampaignsService(ctrl)

			h := &CampaignsHandler{
				context:          context.Background(),
				campaignsService: campaignsService,
			}

			if tt.expected != nil {
				tt.expected(fields{
					campaignsService: campaignsService,
				})
			}

			req, err := http.NewRequestWithContext(t.Context(), http.MethodPost, "/campaigns", bytes.NewBufferString(tt.request))
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()
			http.HandlerFunc(h.HandleCreate).ServeHTTP(rr, req)
			if rr.Code != tt.wantStatusCode {
				t.Errorf("h.HandleCreate() code = %d, expectedCode %d", rr.Code, tt.wantStatusCode)
			}

		})
	}
}

func TestCampaignsHandler_HandleGet(t *testing.T) {
	type fields struct {
		context          context.Context
		campaignsService *domain.MockCampaignsService
	}
	type args struct {
		country string
	}
	validCountry := "US"
	var campaignsList []campaign.Campaign
	testTime := time.Now()
	campaignsList = append(campaignsList, campaign.Campaign{
		CampaignID:   "3aa8b647-3389-4d9a-bfee-21cbf9f32efc",
		Active:       true,
		Country:      "US",
		CampaignType: "CANCEL_ANY",
		CreatedAt:    testTime,
	})

	tests := []struct {
		name           string
		expected       func(f fields)
		args           args
		wantStatusCode int
		wantBody       string
	}{
		{
			name: "empty country",
			args: args{
				country: "",
			},
			wantStatusCode: 400,
			wantBody:       `"country parameter must not be empty"`,
		},
		{
			name: "database error",
			expected: func(f fields) {
				f.campaignsService.EXPECT().GetCampaigns(context.Background(), validCountry).Return(nil, errors.New("database error"))
			},
			args: args{
				country: validCountry,
			},
			wantStatusCode: 500,
			wantBody:       `"database error"`,
		},
		{
			name: "no campaigns for country",
			expected: func(f fields) {
				f.campaignsService.EXPECT().GetCampaigns(context.Background(), validCountry).Return([]campaign.Campaign{}, nil)
			},
			args: args{
				country: validCountry,
			},
			wantStatusCode: 204,
			wantBody:       "",
		},
		{
			name: "success",
			expected: func(f fields) {
				f.campaignsService.EXPECT().GetCampaigns(context.Background(), validCountry).Return(campaignsList, nil)
			},
			args: args{
				country: validCountry,
			},
			wantStatusCode: 200,
			wantBody: fmt.Sprintf(
				`[{"campaign_id":"%s","active":true,"country":"US","campaign_type":"CANCEL_ANY","created_at":"%s"}]`,
				campaignsList[0].CampaignID,
				testTime.Format(time.RFC3339Nano),
			),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			campaignsService := domain.NewMockCampaignsService(ctrl)

			h := &CampaignsHandler{
				context:          context.Background(),
				campaignsService: campaignsService,
			}

			if tt.expected != nil {
				tt.expected(fields{
					campaignsService: campaignsService,
				})
			}

			req, err := http.NewRequestWithContext(context.Background(), http.MethodGet, fmt.Sprintf("/campaigns?country=%v", tt.args.country), nil)
			if err != nil {
				t.Fatal(err)
			}

			rr := httptest.NewRecorder()

			http.HandlerFunc(h.HandleGet).ServeHTTP(rr, req)

			// Check status code
			if rr.Code != tt.wantStatusCode {
				t.Errorf("h.HandleGet() code = %d, expected %d", rr.Code, tt.wantStatusCode)
			}

			// Check body
			responseBody := rr.Body.String()
			if responseBody != tt.wantBody {
				t.Errorf("h.HandleGet() response body mismatch, want: %q, got: %q", tt.wantBody, responseBody)
			}
		})
	}
}

func TestCampaignsHandler_HandleUpdate(t *testing.T) {
	t.Parallel()
	setGlobalConfigEnv()
	updateCampaignSuccessPayload := `{
   "active": false
}`
	updateCampaignTypePayload := `{
   "campaign_type": "CANCEL_ONCE"
}`
	updateBothFieldsPayload := `{
   "active": true,
   "campaign_type": "CANCEL_ANY"
}`
	updateCampaignMissingActivePayload := `{}`
	emptyPayload := `{}`

	type fields struct {
		campaignsService *domain.MockCampaignsService
	}
	tests := []struct {
		name           string
		expected       func(f fields)
		request        string
		campaignID     string
		country        string
		wantStatusCode int
		wantBody       string
	}{
		{
			name: "success - disable campaign",
			expected: func(f fields) {
				updateCampaignSuccessPayloadStruct, err := payload.NewUpdateCampaignPayload([]byte(updateCampaignSuccessPayload))
				assert.NoError(t, err)
				config, err := loadConfig()
				assert.NoError(t, err)
				countryExists := contains(config.CountryCodes, "US")
				assert.True(t, countryExists)
				f.campaignsService.EXPECT().UpdateCampaign(gomock.Any(), "d7052649-d227-4e64-9448-ed38621b803f", "US", updateCampaignSuccessPayloadStruct.Active, updateCampaignSuccessPayloadStruct.CampaignType).Return(nil)
			},
			request:        updateCampaignSuccessPayload,
			campaignID:     "d7052649-d227-4e64-9448-ed38621b803f",
			country:        "us",
			wantStatusCode: http.StatusOK,
			wantBody:       `"campaign updated successfully"`,
		},
		{
			name: "success - update campaign type only",
			expected: func(f fields) {
				updateCampaignTypePayloadStruct, err := payload.NewUpdateCampaignPayload([]byte(updateCampaignTypePayload))
				assert.NoError(t, err)
				config, err := loadConfig()
				assert.NoError(t, err)
				countryExists := contains(config.CountryCodes, "US")
				assert.True(t, countryExists)
				f.campaignsService.EXPECT().UpdateCampaign(gomock.Any(), "d7052649-d227-4e64-9448-ed38621b803f", "US", updateCampaignTypePayloadStruct.Active, updateCampaignTypePayloadStruct.CampaignType).Return(nil)
			},
			request:        updateCampaignTypePayload,
			campaignID:     "d7052649-d227-4e64-9448-ed38621b803f",
			country:        "us",
			wantStatusCode: http.StatusOK,
			wantBody:       `"campaign updated successfully"`,
		},
		{
			name: "success - update both fields",
			expected: func(f fields) {
				updateBothFieldsPayloadStruct, err := payload.NewUpdateCampaignPayload([]byte(updateBothFieldsPayload))
				assert.NoError(t, err)
				config, err := loadConfig()
				assert.NoError(t, err)
				countryExists := contains(config.CountryCodes, "US")
				assert.True(t, countryExists)
				f.campaignsService.EXPECT().UpdateCampaign(gomock.Any(), "d7052649-d227-4e64-9448-ed38621b803f", "US", updateBothFieldsPayloadStruct.Active, updateBothFieldsPayloadStruct.CampaignType).Return(nil)
			},
			request:        updateBothFieldsPayload,
			campaignID:     "d7052649-d227-4e64-9448-ed38621b803f",
			country:        "us",
			wantStatusCode: http.StatusOK,
			wantBody:       `"campaign updated successfully"`,
		},
		{
			name: "campaign not found",
			expected: func(f fields) {
				updateCampaignSuccessPayloadStruct, err := payload.NewUpdateCampaignPayload([]byte(updateCampaignSuccessPayload))
				assert.NoError(t, err)
				config, err := loadConfig()
				assert.NoError(t, err)
				countryExists := contains(config.CountryCodes, "US")
				assert.True(t, countryExists)
				f.campaignsService.EXPECT().UpdateCampaign(gomock.Any(), "d7052649-d227-4e64-9448-ed38621b803f", "US", updateCampaignSuccessPayloadStruct.Active, updateCampaignSuccessPayloadStruct.CampaignType).Return(errors.New("campaign not found: campaign_id=test, country=US"))
			},
			request:        updateCampaignSuccessPayload,
			campaignID:     "d7052649-d227-4e64-9448-ed38621b803f",
			country:        "us",
			wantStatusCode: http.StatusNotFound,
			wantBody:       `"campaign not found: campaign_id=test, country=US"`,
		},
		{
			name: "database error",
			expected: func(f fields) {
				updateCampaignSuccessPayloadStruct, err := payload.NewUpdateCampaignPayload([]byte(updateCampaignSuccessPayload))
				assert.NoError(t, err)
				config, err := loadConfig()
				assert.NoError(t, err)
				countryExists := contains(config.CountryCodes, "US")
				assert.True(t, countryExists)
				f.campaignsService.EXPECT().UpdateCampaign(gomock.Any(), "d7052649-d227-4e64-9448-ed38621b803f", "US", updateCampaignSuccessPayloadStruct.Active, updateCampaignSuccessPayloadStruct.CampaignType).Return(errors.New("generic database error"))
			},
			request:        updateCampaignSuccessPayload,
			campaignID:     "d7052649-d227-4e64-9448-ed38621b803f",
			country:        "us",
			wantStatusCode: http.StatusInternalServerError,
			wantBody:       `"generic database error"`,
		},
		{
			name: "unsupported country",
			expected: func(_ fields) {
				// No service expectation because it should fail before reaching the service
			},
			request:        updateCampaignSuccessPayload,
			campaignID:     "d7052649-d227-4e64-9448-ed38621b803f",
			country:        "de",
			wantStatusCode: http.StatusBadRequest,
			wantBody:       `"Country code is not yet enabled. Please reach out to #squad-rte-systems to enable"`,
		},
		{
			name: "missing campaign ID",
			expected: func(_ fields) {
				// No service expectation because it should fail before reaching the service
			},
			request:        updateCampaignSuccessPayload,
			campaignID:     "",
			country:        "us",
			wantStatusCode: http.StatusBadRequest,
			wantBody:       `"campaign ID must not be empty"`,
		},
		{
			name: "missing country",
			expected: func(_ fields) {
				// No service expectation because it should fail before reaching the service
			},
			request:        updateCampaignSuccessPayload,
			campaignID:     "d7052649-d227-4e64-9448-ed38621b803f",
			country:        "",
			wantStatusCode: http.StatusBadRequest,
			wantBody:       `"country parameter must not be empty"`,
		},
		{
			name: "invalid country format",
			expected: func(_ fields) {
				// No service expectation because it should fail before reaching the service
			},
			request:        updateCampaignSuccessPayload,
			campaignID:     "d7052649-d227-4e64-9448-ed38621b803f",
			country:        "uu",
			wantStatusCode: http.StatusBadRequest,
			wantBody:       `"country: must be a valid two-letter country code"`,
		},
		{
			name: "missing both fields",
			expected: func(_ fields) {
				// No service expectation because it should fail validation
			},
			request:        updateCampaignMissingActivePayload,
			campaignID:     "d7052649-d227-4e64-9448-ed38621b803f",
			country:        "us",
			wantStatusCode: http.StatusBadRequest,
		},
		{
			name: "empty payload",
			expected: func(_ fields) {
				// No service expectation because it should fail validation
			},
			request:        emptyPayload,
			campaignID:     "d7052649-d227-4e64-9448-ed38621b803f",
			country:        "us",
			wantStatusCode: http.StatusBadRequest,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			campaignsService := domain.NewMockCampaignsService(ctrl)

			h := &CampaignsHandler{
				context:          t.Context(),
				campaignsService: campaignsService,
			}

			if tt.expected != nil {
				tt.expected(fields{
					campaignsService: campaignsService,
				})
			}

			// Create URL with path parameter and query parameter
			url := "/campaigns/" + tt.campaignID
			if tt.country != "" {
				url += "?country=" + tt.country
			}

			req, err := http.NewRequestWithContext(t.Context(), http.MethodPatch, url, bytes.NewBufferString(tt.request))
			if err != nil {
				t.Fatal(err)
			}

			// Set up chi router context for URL parameters
			rctx := chi.NewRouteContext()
			rctx.URLParams.Add("campaignId", tt.campaignID)
			req = req.WithContext(context.WithValue(req.Context(), chi.RouteCtxKey, rctx))

			rr := httptest.NewRecorder()
			http.HandlerFunc(h.HandleUpdate).ServeHTTP(rr, req)

			if rr.Code != tt.wantStatusCode {
				t.Errorf("h.HandleUpdate() code = %d, expectedCode %d", rr.Code, tt.wantStatusCode)
			}

			if tt.wantBody != "" {
				responseBody := rr.Body.String()
				if responseBody != tt.wantBody {
					t.Errorf("h.HandleUpdate() response body mismatch, want: %q, got: %q", tt.wantBody, responseBody)
				}
			}
		})
	}
}

func setGlobalConfigEnv() {
	_ = os.Setenv("AMQP_DSN", "8000")
	_ = os.Setenv("AUTH_URL", "development")
	_ = os.Setenv("CLIENT_ID", "community-discount-service")
	_ = os.Setenv("CLIENT_SECRET", "info")
	_ = os.Setenv("COUNTRY_CODES", "FJ,CK,US")
	_ = os.Setenv("PRICE_SERVICE_URL", "postgres")
	_ = os.Setenv("VOUCHER_SERVICE_URL", "voucher-service-url")
	_ = os.Setenv("BALANCE_SERVICE_URL", "balance-service-url")
	_ = os.Setenv("DB_HOST", "postgres")
	_ = os.Setenv("DB_PORT", "90")
	_ = os.Setenv("DB_DSN", "postgres")
	_ = os.Setenv("DB_USER", "postgres")
	_ = os.Setenv("DB_NAME", "postgres")
	_ = os.Setenv("DB_PASSWORD", "postgres")
}
