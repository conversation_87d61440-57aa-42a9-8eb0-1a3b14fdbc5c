package http

import (
	"context"
	"io"
	"net/http"
	"strings"

	"github.com/go-chi/chi/v5"
	"github.com/go-ozzo/ozzo-validation/v4/is"
	"github.com/kelseyhightower/envconfig"
	"github.com/nicklaw5/go-respond"

	"github.com/hellofresh/credit-autocancel-service/internal/app"
	"github.com/hellofresh/credit-autocancel-service/internal/domain"
	"github.com/hellofresh/credit-autocancel-service/internal/payload"
)

type (
	// CampaignsHandler handles requests to the campaigns endpoint
	CampaignsHandler struct {
		context          context.Context
		campaignsService domain.CampaignsService
	}
)

// NewCampaigns creates a new CampaignsHandler
func NewCampaigns(ctx context.Context, campaignsService domain.CampaignsService) *CampaignsHandler {
	return &CampaignsHandler{
		context:          ctx,
		campaignsService: campaignsService,
	}
}

// HandleCreate receives a context and handles response for the create campaigns endpoint
func (h *CampaignsHandler) HandleCreate(w http.ResponseWriter, r *http.Request) {
	response := respond.NewResponse(w)
	rb, err := io.ReadAll(r.Body)

	w.Header().Set("Content-Type", "application/json")
	if err != nil {
		response.InternalServerError(err.Error())
		return
	}
	campaignsPayload, err := payload.NewCampaignsPayload(rb)
	if err != nil {
		response.BadRequest(err.Error())
		return
	}

	cfg, err := loadConfig()
	if err != nil {
		response.InternalServerError(err.Error())
	}

	countryExists := contains(cfg.CountryCodes, campaignsPayload.Country)
	if !countryExists {
		response.BadRequest("Country code is not yet enabled. Please reach out to #squad-rte-systems to enable")
		return
	}
	err = h.campaignsService.CreateCampaigns(r.Context(), *campaignsPayload)
	if err != nil {
		response.BadRequest(err.Error())
		return
	}

	response.Ok("campaigns created")
}

// HandleGet receives a context and handles response for the create campaigns endpoint
func (h *CampaignsHandler) HandleGet(w http.ResponseWriter, r *http.Request) {
	response := respond.NewResponse(w)

	country := strings.ToUpper(r.URL.Query().Get("country"))
	if country == "" {
		response.BadRequest("country parameter must not be empty")
		return
	}

	campaigns, err := h.campaignsService.GetCampaigns(r.Context(), country)
	if err != nil {
		response.InternalServerError(err.Error())
		return
	}

	if len(campaigns) == 0 {
		response.NoContent()
		return
	}

	response.Ok(campaigns)
}

// HandleUpdate receives a context and handles response for the update campaign endpoint
func (h *CampaignsHandler) HandleUpdate(w http.ResponseWriter, r *http.Request) {
	response := respond.NewResponse(w)

	// Extract campaign ID from URL path
	campaignID := chi.URLParam(r, "campaignId")
	if campaignID == "" {
		response.BadRequest("campaign ID must not be empty")
		return
	}

	// Extract country from query parameter
	country := strings.ToUpper(r.URL.Query().Get("country"))
	if country == "" {
		response.BadRequest("country parameter must not be empty")
		return
	}

	// Validate country code format
	if err := is.CountryCode2.Validate(country); err != nil {
		response.BadRequest("country: must be a valid two-letter country code")
		return
	}

	rb, err := io.ReadAll(r.Body)
	w.Header().Set("Content-Type", "application/json")
	if err != nil {
		response.InternalServerError(err.Error())
		return
	}

	updateCampaignPayload, err := payload.NewUpdateCampaignPayload(rb)
	if err != nil {
		response.BadRequest(err.Error())
		return
	}

	cfg, err := loadConfig()
	if err != nil {
		response.InternalServerError(err.Error())
		return
	}

	countryExists := contains(cfg.CountryCodes, country)
	if !countryExists {
		response.BadRequest("Country code is not yet enabled. Please reach out to #squad-rte-systems to enable")
		return
	}

	err = h.campaignsService.UpdateCampaign(r.Context(), campaignID, country, updateCampaignPayload.Active, updateCampaignPayload.CampaignType)
	if err != nil {
		if strings.Contains(err.Error(), "campaign not found") {
			response.NotFound(err.Error())
			return
		}
		response.InternalServerError(err.Error())
		return
	}

	response.Ok("campaign updated successfully")
}

func contains(s []string, str string) bool {
	for _, v := range s {
		if v == str {
			return true
		}
	}

	return false
}

func loadConfig() (*app.Configuration, error) {
	cfg := &app.Configuration{}
	err := envconfig.Process("", cfg)
	if err != nil {
		return cfg, err
	}

	return cfg, nil
}
