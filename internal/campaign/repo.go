//go:generate mockgen -source repo.go -destination campaign_mock.go -package campaign

package campaign

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/hellofresh/growth-go-kit/logger"
	"github.com/hellofresh/growth-go-kit/observability"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/hellofresh/credit-autocancel-service/internal/payload"
)

// Repository defines method to determine if campaign is part of CACS program
type Repository interface {
	GetCampaignByID(ctx context.Context, campaignID string, country string) (Campaign, error)
}

// Repo repository of campaigns
type Repo struct {
	db *sqlx.DB
}

// NewRepo initializes repo of campaigns
func NewRepo(db *sqlx.DB) *Repo {
	return &Repo{db: db}
}

// ErrVoucherNotPartOfCampaign indicates the voucher is not part of a campaign configured in the service db campaign table
var ErrVoucherNotPartOfCampaign = errors.New("voucher must be part of a campaign")

const (
	// sqlInsertCampaignID inserts a campaign ID, and it's active status for a specific country
	sqlInsertCampaignID = `
		INSERT INTO campaign (campaign_id, active, country, campaign_type)
		VALUES($1, $2, $3, $4);
`
	// sqlGetActiveCampaignByID retrieves a campaign ID for a specific country
	sqlGetActiveCampaignByID = `
		SELECT campaign_id, campaign_type
		FROM campaign
		WHERE campaign_id = $1 AND country = $2 and active = true
		LIMIT 1;
`

	// sqlGetCampaignsByCountry inserts a campaign ID, and it's active status for a specific country
	sqlGetCampaignsByCountry = `
		SELECT campaign_id, active, created_at, country, campaign_type
		FROM campaign
		WHERE country=$1
		ORDER BY created_at DESC;
`

	// dynamicParamStartIndex is the starting index for dynamic SQL parameters in update queries
	// $1 and $2 are reserved for campaignID and country respectively
	dynamicParamStartIndex = 3
)

// Campaign entity from a campaign table
type Campaign struct {
	CampaignID   string    `json:"campaign_id"`
	Active       bool      `json:"active"`
	Country      string    `json:"country"`
	CampaignType string    `json:"campaign_type"`
	CreatedAt    time.Time `json:"created_at"`
}

// CreateCampaigns inserts into the campaign table to create one or more campaigns
func (r *Repo) CreateCampaigns(ctx context.Context, campaignsPayload payload.CampaignsPayload) error {
	for _, campaignID := range campaignsPayload.CampaignIDs {

		tx, err := r.db.BeginTx(ctx, &sql.TxOptions{})
		if err != nil {
			return fmt.Errorf("could not prepare insert campaign transaction: %w", err)
		}

		defer func() {
			if err != nil {
				tx.Rollback()
				return
			}
			err = errors.Wrap(tx.Commit(), "could not commit insert campaign transaction")
		}()

		stmt, err := tx.PrepareContext(ctx, sqlInsertCampaignID)
		if err != nil {
			return fmt.Errorf("could not prepare insert campaign statement: %w", err)
		}
		defer stmt.Close() // nolint: errcheck

		if _, err = stmt.ExecContext(ctx,
			campaignID,
			campaignsPayload.Active,
			campaignsPayload.Country,
			campaignsPayload.CampaignType,
		); err != nil {
			return fmt.Errorf("could not execute insert campaign statement: %w", err)
		}
	}

	return nil
}

// GetCampaignByID returns a campaign by ID
func (r *Repo) GetCampaignByID(ctx context.Context, campaignID string, country string) (Campaign, error) {
	ctx, span := observability.StartSpan(ctx, "repo.campaign.GetCampaignByID")
	defer span.End()

	logger.From(ctx).With(
		zap.String("campaign_id", campaignID),
		zap.String("country", country),
	).Info("getting campaign by ID")

	var campaign Campaign
	stmt, err := r.db.PrepareContext(ctx, sqlGetActiveCampaignByID)
	if err != nil {
		return campaign, fmt.Errorf("could not prepare get campaign by ID statement: %w", err)
	}
	defer stmt.Close() // nolint: errcheck

	err = stmt.QueryRowContext(ctx, campaignID, strings.ToUpper(country)).Scan(&campaign.CampaignID, &campaign.CampaignType)
	if errors.Is(err, sql.ErrNoRows) {
		return campaign, ErrVoucherNotPartOfCampaign
	}
	if err != nil {
		return campaign, fmt.Errorf("could not execute get campaign by ID statement: %w", err)
	}

	return campaign, nil
}

// GetCampaigns returns a list of all campaigns for a given country
func (r *Repo) GetCampaigns(ctx context.Context, country string) ([]Campaign, error) {
	var campaignsForCountry []Campaign
	stmt, err := r.db.PrepareContext(ctx, sqlGetCampaignsByCountry)
	if err != nil {
		return nil, fmt.Errorf("could not prepare get campaigns for country statement: %w", err)
	}
	defer stmt.Close()

	rows, err := stmt.QueryContext(ctx, country)
	if err != nil {
		return nil, fmt.Errorf("could not execute get campaigns for country statement: %w", err)
	}

	for rows.Next() {
		campaign := Campaign{}
		err = rows.Scan(&campaign.CampaignID, &campaign.Active, &campaign.CreatedAt, &campaign.Country, &campaign.CampaignType)
		if err != nil {
			return nil, fmt.Errorf("could not scan get campaigns for country row: %w", err)
		}
		campaignsForCountry = append(campaignsForCountry, campaign)
	}

	return campaignsForCountry, nil
}

// UpdateCampaign updates the active status and/or campaign type of a campaign for a specific country
func (r *Repo) UpdateCampaign(ctx context.Context, campaignID, country string, active *bool, campaignType *string) error {
	ctx, span := observability.StartSpan(ctx, "repo.campaign.UpdateCampaign")
	defer span.End()

	logger.From(ctx).With(
		zap.String("campaign_id", campaignID),
		zap.String("country", country),
	).Info("updating campaign")

	// Build dynamic SQL query based on provided fields
	var setParts []string
	var args []interface{}
	argIndex := dynamicParamStartIndex

	args = append(args, campaignID, strings.ToUpper(country))

	if active != nil {
		setParts = append(setParts, fmt.Sprintf("active = $%d", argIndex))
		args = append(args, *active)
		argIndex++
	}

	if campaignType != nil {
		setParts = append(setParts, fmt.Sprintf("campaign_type = $%d", argIndex))
		args = append(args, *campaignType)
	}

	if len(setParts) == 0 {
		return fmt.Errorf("no fields to update")
	}

	query := fmt.Sprintf("UPDATE campaign SET %s WHERE campaign_id = $1 AND country = $2", strings.Join(setParts, ", "))

	stmt, err := r.db.PrepareContext(ctx, query)
	if err != nil {
		return fmt.Errorf("could not prepare update campaign statement: %w", err)
	}
	defer stmt.Close() // nolint: errcheck

	result, err := stmt.ExecContext(ctx, args...)
	if err != nil {
		return fmt.Errorf("could not execute update campaign statement: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("could not get rows affected for update campaign: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("campaign not found: campaign_id=%s, country=%s", campaignID, country)
	}

	return nil
}
