package campaign

import (
	"context"
	"database/sql"
	"strings"
	"testing"
	"time"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/jmoiron/sqlx"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"github.com/hellofresh/credit-autocancel-service/internal/payload"
)

const createCampaignsSuccessPayload = `{
    "campaign_ids":  ["03d90675-acbe-4821-97c9-45a4676ac668"],
    "active": 		 true,
    "country": 		 "US",
    "campaign_type": "CANCEL_ANY"
}`

func TestRepo_GetCampaignByID(t *testing.T) {
	t.Parallel()

	validCountry := "US"
	result := Campaign{
		CampaignID:   "9ec755ea-0b2d-4942-a3a7-166ed654728a",
		CampaignType: "CANCEL_ANY",
	}

	type args struct {
		campaignID string
		country    string
	}
	tests := []struct {
		name     string
		expected func(mock sqlmock.Sqlmock)
		args     args
		want     Campaign
		wantErr  bool
	}{
		{
			name: "Campaign exists",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare(sqlGetActiveCampaignByID).ExpectQuery().WithArgs(result.CampaignID, validCountry).WillReturnRows(
					sqlmock.NewRows([]string{"campaign_id", "campaign_type"}).
						AddRow(result.CampaignID, result.CampaignType),
				)
			},
			args: args{
				campaignID: "9ec755ea-0b2d-4942-a3a7-166ed654728a",
				country:    "US",
			},
			want:    result,
			wantErr: false,
		},
		{
			name: "Campaign does not exists",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare(sqlGetActiveCampaignByID).WillReturnError(sql.ErrNoRows)
			},
			args: args{
				campaignID: "9ec755ea-0b2d-4942-a3a7-166ed654728a",
				country:    "US",
			},
			want:    Campaign{},
			wantErr: true,
		},
		{
			name: "prepare error",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare(sqlGetActiveCampaignByID).WillReturnError(errors.New("Prepare error"))
			},
			args: args{
				campaignID: "9ec755ea-0b2d-4942-a3a7-166ed654728a",
				country:    "US",
			},
			want:    Campaign{},
			wantErr: true,
		},
		{
			name: "database query error",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare(sqlGetActiveCampaignByID).ExpectQuery().WithArgs(result.CampaignID, validCountry).WillReturnError(errors.New("Query error"))
			},
			args: args{
				campaignID: "9ec755ea-0b2d-4942-a3a7-166ed654728a",
				country:    "US",
			},
			want:    Campaign{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB, mock, err := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			sr := &Repo{db: sqlxDB}
			if tt.expected != nil {
				tt.expected(mock)
			}

			got, err := sr.GetCampaignByID(t.Context(), tt.args.campaignID, tt.args.country)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCampaignByID() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestRepo_CreateCampaigns(t *testing.T) {
	createCampaignsSuccessPayloadStruct, _ := payload.NewCampaignsPayload([]byte(createCampaignsSuccessPayload))
	type args struct {
		campaignsPayload payload.CampaignsPayload
	}
	tests := []struct {
		name     string
		expected func(mock sqlmock.Sqlmock)
		args     args
		wantErr  bool
	}{
		{
			name: "success",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectBegin()
				mock.ExpectPrepare(sqlInsertCampaignID).ExpectExec().WithArgs(
					createCampaignsSuccessPayloadStruct.CampaignIDs[0],
					createCampaignsSuccessPayloadStruct.Active,
					createCampaignsSuccessPayloadStruct.Country,
					createCampaignsSuccessPayloadStruct.CampaignType,
				).WillReturnResult(sqlmock.NewResult(1, 1))
				mock.ExpectCommit()
			},
			args: args{
				campaignsPayload: *createCampaignsSuccessPayloadStruct,
			},
			wantErr: false,
		},
		{
			name: "begin error",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectBegin().WillReturnError(errors.New("Generic Begin Error"))
			},
			args: args{
				campaignsPayload: *createCampaignsSuccessPayloadStruct,
			},
			wantErr: true,
		},
		{
			name: "database insert error",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectBegin()
				mock.ExpectPrepare(sqlInsertCampaignID).ExpectExec().WithArgs(
					createCampaignsSuccessPayloadStruct.CampaignIDs[0],
					createCampaignsSuccessPayloadStruct.Active,
					createCampaignsSuccessPayloadStruct.Country,
					createCampaignsSuccessPayloadStruct.CampaignType,
				).WillReturnError(errors.New("Campaign already exists"))
				mock.ExpectRollback()
			},
			args: args{
				campaignsPayload: *createCampaignsSuccessPayloadStruct,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB, mock, err := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			r := &Repo{
				db: sqlxDB,
			}

			if tt.expected != nil {
				tt.expected(mock)
			}

			if err := r.CreateCampaigns(context.Background(), tt.args.campaignsPayload); (err != nil) != tt.wantErr {
				t.Errorf("CreateCampaigns() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRepo_GetCampaigns(t *testing.T) {
	type fields struct {
		dbClient *sqlx.DB
		mock     sqlmock.Sqlmock
	}
	type args struct {
		country string
	}

	validCountry := "US"
	testTime := time.Now()
	result := Campaign{
		CampaignID:   "d484f341-963e-4d45-b078-e74972666560",
		Active:       true,
		Country:      "US",
		CampaignType: "CANCEL_ANY",
		CreatedAt:    testTime,
	}

	tests := []struct {
		name     string
		expected func(mock sqlmock.Sqlmock)
		args     args
		want     []Campaign
		wantErr  bool
	}{
		{
			name: "prepare error",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare(sqlInsertCampaignID).WillReturnError(errors.New("Prepare error"))
			},
			args: args{
				country: "US",
			},
			wantErr: true,
		},
		{
			name: "database query error",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare(sqlInsertCampaignID).ExpectQuery().WithArgs(validCountry).WillReturnError(errors.New("Query error"))
			},
			args: args{
				country: "US",
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				country: "US",
			},
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare(sqlGetCampaignsByCountry).ExpectQuery().WithArgs(validCountry).WillReturnRows(
					sqlmock.NewRows([]string{"campaign_id", "active", "created_at", "country", "campaign_type"}).
						AddRow(result.CampaignID, result.Active, result.CreatedAt, result.Country, result.CampaignType),
				)
			},
			want: []Campaign{
				result,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockDB, mock, err := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherEqual))
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			r := &Repo{
				db: sqlxDB,
			}

			if tt.expected != nil {
				tt.expected(mock)
			}

			got, err := r.GetCampaigns(context.Background(), tt.args.country)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCampaigns() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestRepo_UpdateCampaign(t *testing.T) {
	t.Parallel()

	validCampaignID := "9ec755ea-0b2d-4942-a3a7-166ed654728a"
	validCountry := "US"
	activeFalse := false
	activeTrue := true
	campaignTypeCancel := "CANCEL_ONCE"

	type args struct {
		campaignID   string
		country      string
		active       *bool
		campaignType *string
	}

	tests := []struct {
		name       string
		expected   func(mock sqlmock.Sqlmock)
		args       args
		wantErr    bool
		wantErrMsg string
	}{
		{
			name: "success - disable campaign only",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare("UPDATE campaign SET active = \\$3 WHERE campaign_id = \\$1 AND country = \\$2").
					ExpectExec().
					WithArgs(validCampaignID, validCountry, false).
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			args: args{
				campaignID:   validCampaignID,
				country:      validCountry,
				active:       &activeFalse,
				campaignType: nil,
			},
			wantErr: false,
		},
		{
			name: "success - enable campaign only",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare("UPDATE campaign SET active = \\$3 WHERE campaign_id = \\$1 AND country = \\$2").
					ExpectExec().
					WithArgs(validCampaignID, validCountry, true).
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			args: args{
				campaignID:   validCampaignID,
				country:      validCountry,
				active:       &activeTrue,
				campaignType: nil,
			},
			wantErr: false,
		},
		{
			name: "success - update campaign type only",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare("UPDATE campaign SET campaign_type = \\$3 WHERE campaign_id = \\$1 AND country = \\$2").
					ExpectExec().
					WithArgs(validCampaignID, validCountry, "CANCEL_ONCE").
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			args: args{
				campaignID:   validCampaignID,
				country:      validCountry,
				active:       nil,
				campaignType: &campaignTypeCancel,
			},
			wantErr: false,
		},
		{
			name: "success - update both fields",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare("UPDATE campaign SET active = \\$3, campaign_type = \\$4 WHERE campaign_id = \\$1 AND country = \\$2").
					ExpectExec().
					WithArgs(validCampaignID, validCountry, true, "CANCEL_ONCE").
					WillReturnResult(sqlmock.NewResult(0, 1))
			},
			args: args{
				campaignID:   validCampaignID,
				country:      validCountry,
				active:       &activeTrue,
				campaignType: &campaignTypeCancel,
			},
			wantErr: false,
		},
		{
			name: "no fields to update",
			expected: func(_ sqlmock.Sqlmock) {
				// No expectation since it should fail before database call
			},
			args: args{
				campaignID:   validCampaignID,
				country:      validCountry,
				active:       nil,
				campaignType: nil,
			},
			wantErr:    true,
			wantErrMsg: "no fields to update",
		},
		{
			name: "campaign not found",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare("UPDATE campaign SET active = \\$3 WHERE campaign_id = \\$1 AND country = \\$2").
					ExpectExec().
					WithArgs(validCampaignID, validCountry, false).
					WillReturnResult(sqlmock.NewResult(0, 0))
			},
			args: args{
				campaignID:   validCampaignID,
				country:      validCountry,
				active:       &activeFalse,
				campaignType: nil,
			},
			wantErr:    true,
			wantErrMsg: "campaign not found",
		},
		{
			name: "database error",
			expected: func(mock sqlmock.Sqlmock) {
				mock.ExpectPrepare("UPDATE campaign SET active = \\$3 WHERE campaign_id = \\$1 AND country = \\$2").
					ExpectExec().
					WithArgs(validCampaignID, validCountry, false).
					WillReturnError(errors.New("database error"))
			},
			args: args{
				campaignID:   validCampaignID,
				country:      validCountry,
				active:       &activeFalse,
				campaignType: nil,
			},
			wantErr:    true,
			wantErrMsg: "could not execute update campaign statement",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB, mock, err := sqlmock.New(sqlmock.QueryMatcherOption(sqlmock.QueryMatcherRegexp))
			if err != nil {
				t.Fatal(err)
			}
			defer mockDB.Close()

			sqlxDB := sqlx.NewDb(mockDB, "sqlmock")

			sr := &Repo{db: sqlxDB}
			if tt.expected != nil {
				tt.expected(mock)
			}

			err = sr.UpdateCampaign(t.Context(), tt.args.campaignID, tt.args.country, tt.args.active, tt.args.campaignType)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateCampaign() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && tt.wantErrMsg != "" && !strings.Contains(err.Error(), tt.wantErrMsg) {
				t.Errorf("UpdateCampaign() error = %v, want error containing %v", err, tt.wantErrMsg)
			}
		})
	}
}
