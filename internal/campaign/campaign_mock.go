// Code generated by MockGen. DO NOT EDIT.
// Source: repo.go

// Package campaign is a generated GoMock package.
package campaign

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetCampaignByID mocks base method.
func (m *MockRepository) GetCampaignByID(ctx context.Context, campaignID, country string) (Campaign, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCampaignByID", ctx, campaignID, country)
	ret0, _ := ret[0].(Campaign)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCampaignByID indicates an expected call of GetCampaignByID.
func (mr *MockRepositoryMockRecorder) GetCampaignByID(ctx, campaignID, country interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCampaignByID", reflect.TypeOf((*MockRepository)(nil).GetCampaignByID), ctx, campaignID, country)
}
