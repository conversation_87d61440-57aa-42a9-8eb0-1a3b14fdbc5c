version: "2"
linters:
  default: none
  enable:
    - asasalint
    - asciicheck
    - bidichk
    - bodyclose
    - contextcheck
    - copyloopvar
    - durationcheck
    - errcheck
    - errchkjson
    - errname
    - errorlint
    - exhaustive
    - forcetypeassert
    - gocritic
    - goprintffuncname
    - gosec
    - govet
    - ineffassign
    - ireturn
    - loggercheck
    - makezero
    - misspell
    - nilerr
    - nilnil
    - noctx
    - paralleltest
    - revive
    - rowserrcheck
    - sqlclosecheck
    - staticcheck
    - tagliatelle
    - thelper
    - tparallel
    - unconvert
    - unparam
    - unused
    - usestdlibvars
    - usetesting
    - wastedassign
    - whitespace
  settings:
    errcheck:
      exclude-functions:
        - (*database/sql.DB).Close
        - (*database/sql.Rows).Close
        - (*encoding/json.Encoder).Encode
        - (*github.com/go-redis/redis.baseClient).Close
        - (*github.com/golang-migrate/migrate/v4.Migrate).Close
        - (*github.com/streadway/amqp.Connection).Close
        - (*go.uber.org/zap.Logger).Sync
        - (io.Closer).Close
        - (net/http.ResponseWriter).Write
        - (*os.File).Close
    gocritic:
      disabled-checks:
        - exitAfterDefer
        - sloppyLen
    gosec:
      excludes:
        - G104
        - G112
        - G404
        - G307
    govet:
      disable:
        - copylocks
    tagliatelle:
      case:
        rules:
          json: snake
  exclusions:
    generated: lax
    rules:
      - linters:
          - revive
        text: package comment should be of the form
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  enable:
    - gofmt
    - gofumpt
    - goimports
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
