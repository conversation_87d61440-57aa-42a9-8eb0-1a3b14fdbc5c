name: credit-autocancel-service

services:
  credit-autocancel-service-api:
    build:
      context: .
      dockerfile: ./dev.api.Dockerfile
      args:
        GITHUB_TOKEN: ${GITHUB_TOKEN}
    ports:
      - '9090:9090'
    volumes:
      - .:/credit-autocancel-service
    env_file:
      - .env
    depends_on:
      - postgres
    restart: always

  credit-autocancel-service-job:
    build:
      context: .
      dockerfile: ./dev.job.Dockerfile
      args:
        GITHUB_TOKEN: ${GITHUB_TOKEN}
    ports:
      - '9091:9090'
    volumes:
      - .:/credit-autocancel-service
    env_file:
      - .env
    restart: always
    depends_on:
      - postgres

  credit-autocancel-service-migrate:
    image: migrate/migrate
    volumes:
      - ./migrations:/migrations
    depends_on:
      - postgres
    restart: on-failure
    command:
      - "-database"
      - "**************************************************************/creditautocanceldb?sslmode=disable"
      - "-path"
      - "/migrations"
      - "up"

  credit-autocancel-service-benthos:
    build:
      context: .
      dockerfile: ./dev.benthos.Dockerfile
      args:
        GITHUB_TOKEN: ${GITHUB_TOKEN}
    ports:
      - '9092:9090'
    volumes:
      - .:/credit-autocancel-service
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=credit-autocancel-service
      - DB_PASSWORD=freshforyou
      - DB_NAME=creditautocanceldb
      - KAFKA_SECURITY_PROTOCOL=PLAINTEXT
      - KAFKA_SASL_MECHANISM=none
      - KAFKA_DSN=kafka:29092
      - KAFKA_USERNAME=
      - KAFKA_PASSWORD=
      - KAFKA_CA_CERT_FILE=
      - BENTHOS_LOG_LEVEL=DEBUG
      - TLS_ENABLED=false
      - VOUCHER_SERVICE_URL=http://voucher-service.staging-k8s.hellofresh.io
      - AMQP_DSN=amqp://credit-autocancel-service:credit-autocancel-service@rabbit:5672/
      - BALANCE_SERVICE_URL=http://balance-service.staging-k8s.hellofresh.io
    restart: always
    depends_on:
      - postgres
      - kafka
    profiles: ["benthos"]

  postgres:
    image: postgres:13
    restart: always
    ports:
      - '5432:5432'
    env_file:
      - .env
    environment:
      LC_ALL: C.UTF-8
    volumes:
      - postgres-data:/var/lib/postgresql/data
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready" ]
      interval: 5s
      timeout: 10s
      retries: 5

  rabbit:
    image: rabbitmq:3.7-management
    ports:
      - '15672:15672'
      - '5672:5672'
    environment:
      - RABBITMQ_DEFAULT_USER=credit-autocancel-service
      - RABBITMQ_DEFAULT_PASS=credit-autocancel-service
    volumes:
      - rabbit-data:/var/lib/rabbitmq
    healthcheck:
      test: [ "CMD-SHELL", "rabbitmq-diagnostics -q check_running" ]
      interval: 5s
      timeout: 5s
      retries: 60
    restart: always

  outbox-worker:
    image: 489198589229.dkr.ecr.eu-west-1.amazonaws.com/outbox-worker:stable
    platform: linux/amd64
    restart: always
    depends_on:
      - postgres
      - rabbit
    environment:
      - AMQP_DSN=amqp://credit-autocancel-service:@rabbit:5672
      - AMQP_PASSWORD=credit-autocancel-service
      - DB_DSN=postgres://credit-autocancel-service:@postgres:5432/creditautocanceldb?sslmode=disable
      - DB_PASSWORD=freshforyou
      - TRIGGER_INTERVAL=50ms

  jaeger:
    image: jaegertracing/all-in-one:1.42
    ports:
      - "16686:16686"  # Web UI
      - "4317:4317"    # OTLP gRPC (for OTEL_EXPORTER_OTLP_ENDPOINT)
    environment:
      COLLECTOR_OTLP_ENABLED: "true"
      LOG_LEVEL: debug
    restart: always

  kafka:
    image: confluentinc/cp-kafka:7.3.1
    ports:
      - "9093:9093"
    environment:
      - KAFKA_NODE_ID=1
      - KAFKA_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      - KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9093
      - KAFKA_LISTENERS=PLAINTEXT://kafka:29092,CONTROLLER://kafka:29093,PLAINTEXT_HOST://0.0.0.0:9093
      - KAFKA_CONTROLLER_QUORUM_VOTERS=1@kafka:29093
      - KAFKA_PROCESS_ROLES=broker,controller
      - KAFKA_INTER_BROKER_LISTENER_NAME=PLAINTEXT
      - KAFKA_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - KAFKA_SOCKET_REQUEST_MAX_BYTES=**********
      - KAFKA_HEAP_OPTS=-Xmx8g -Xms8g -XX:MetaspaceSize=96m -XX:+UseG1GC -XX:MaxGCPauseMillis=20 -XX:InitiatingHeapOccupancyPercent=35 -XX:G1HeapRegionSize=16M -XX:MinMetaspaceFreeRatio=50 -XX:MaxMetaspaceFreeRatio=80
    volumes:
      - kafka-data:/var/lib/kafka/data
    healthcheck:
      test: kafka-topics --list --bootstrap-server kafka:9092
      interval: 5s
      timeout: 10s
      retries: 5
    restart: always
    profiles: ["benthos"]
    command: >
      bash -c "sed -i '/KAFKA_ZOOKEEPER_CONNECT/d' /etc/confluent/docker/configure && 
              sed -i 's/cub zk-ready/echo ignore zk-ready/' /etc/confluent/docker/ensure && 
              echo \"kafka-storage format --ignore-formatted -t $(kafka-storage random-uuid) -c /etc/kafka/kafka.properties\" >> /etc/confluent/docker/ensure && 
              /etc/confluent/docker/run"

volumes:
  postgres-data:
  rabbit-data:
  kafka-data:
