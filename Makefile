NAME=credit-autocancel-service
REPO=github.com/hellofresh/credit-autocancel-service
BUILD_DIR ?= dist
TEST_DIR = testing
BINARY_API=$(NAME)
BINARY_JOB=$(NAME)-job
BINARY_BENTHOS=$(NAME)-benthos
BINARY_SRC=$(REPO)
MAIN_DIR_API=./cmd/credit-autocancel-service-api/main.go
MAIN_DIR_JOB=./cmd/credit-autocancel-service-job/*.go
MAIN_DIR_BENTHOS=./cmd/credit-autocancel-service-benthos

BINARY_MIGRATE_TOOL=/bin/migrate
BINARY_MIGRATE_TOOL_VERSION=v4.18.3
BINARY_MIGRATE_TOOL_SHA256SUM=0dc97ab52d600b10cf7c16e2f427a35eb273b7c56421a00ec8801adc2631ffa1
BINARY_MIGRATE_TOOL_URL=https://github.com/golang-migrate/migrate/releases/download/${BINARY_MIGRATE_TOOL_VERSION}/migrate.linux-amd64.tar.gz

PLATFORM := $(shell uname | tr A-Z a-z)

NO_COLOR=\033[0m
OK_COLOR=\033[32;01m
ERROR_COLOR=\033[31;01m
WARN_COLOR=\033[33;01m

BUILD_FLAGS=-ldflags="-s -w -X main.Version=$(VERSION) -X main.Buildstamp=$(BUILDSTAMP)"

.PHONY: all clean test build format vet lint dev-up build-assets build-migration-tool
all: clean test build

build: build-assets
	@echo "$(OK_COLOR)==> Building... $(NO_COLOR)"
	@if [ ! -d ${BUILD_DIR} ] ; then mkdir -p ${BUILD_DIR} ; fi
	@CGO_ENABLED=0 go build -mod=vendor -v $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_API) $(MAIN_DIR_API)
	@CGO_ENABLED=0 go build -mod=vendor -v $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_JOB) $(MAIN_DIR_JOB)
	@CGO_ENABLED=0 go build -mod=vendor -v $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_BENTHOS) $(MAIN_DIR_BENTHOS)

build-assets: github-token
	@echo "$(OK_COLOR)==> Building assets... $(NO_COLOR)"

build-migration-tool:
	${call print, "Downloading migration tool binary"}
	curl -L ${BINARY_MIGRATE_TOOL_URL} | tar -xvz
	echo "${BINARY_MIGRATE_TOOL_SHA256SUM} migrate" | sha256sum --check
	mv migrate ${BINARY_MIGRATE_TOOL}
	chmod +x ${BINARY_MIGRATE_TOOL}

clean:
	@rm -rf ./dist

deps: build-assets
	@go mod vendor

deps-build: build-assets
	@go mod vendor
	@echo "$(OK_COLOR)==> Building... $(NO_COLOR)"
	@if [ ! -d ${BUILD_DIR} ] ; then mkdir -p ${BUILD_DIR} ; fi
	@GOFLAGS=-mod=vendor
	@CGO_ENABLED=0 go build -mod=vendor -v $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_API) $(MAIN_DIR_API)
	@CGO_ENABLED=0 go build -mod=vendor -v $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_JOB) $(MAIN_DIR_JOB)
	@CGO_ENABLED=0 go build -mod=vendor -v $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_BENTHOS) $(MAIN_DIR_BENTHOS)

test: test-unit test-benthos-integration

test-unit:
	@echo "$(OK_COLOR)==> Running tests$(NO_COLOR)"
	@go test -race -cover -coverprofile=coverage.txt -covermode=atomic -short ./...

# run only benthos integration tests. Set SKIP_BUILD=true if binary has already been built.
test-benthos-integration:
	go test ./integration_tests -v -count=1

format:
	@gofmt -l -s cmd pkg | grep ".*\.go"; if [ "$$?" = "0" ]; then exit 1; fi

vet:
	@echo "$(OK_COLOR)==> Checking code correctness with 'go vet' tool$(NO_COLOR)"
	@go vet ./...

dev-up:
	@echo "$(OK_COLOR)==> Starting dev environment$(NO_COLOR)"
	@test -f .env || cp .env.example .env
	@docker-compose up -d

githooks:
	@git config hooks.gitleaks true
	@git config core.hooksPath .githooks

generate:
	@./scripts/generate.sh

lint:
	golangci-lint run --new-from-rev=origin/master ./...

github-token:
	@printf "$(OK_COLOR)==> Setting Github Token$(NO_COLOR)\n"
	git config --global http.https://gopkg.in.followRedirects true
# Setting up GitHub token to download internal dependencies
ifdef GITHUB_TOKEN
	@git config --global --replace-all url."https://${GITHUB_TOKEN}@github.com/hellofresh/".insteadOf "https://github.com/hellofresh/"
endif

go-generate:
	@if ! which mockery > /dev/null ; then \
			echo ">> [$@]: mockery not found: installing"; \
			go install github.com/vektra/mockery/v2; \
	fi
	go generate ./...

decode-events: check-SQL_DSN
	SQL_DSN=$(SQL_DSN) go run cmd/credit-autocancel-service-event-decoder/main.go

check-%:
	@: $(if $(value $*),,$(error $* is undefined))
