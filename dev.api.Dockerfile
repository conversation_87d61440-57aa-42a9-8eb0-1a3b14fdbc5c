FROM golang:1.24.1

ENV APP_DIR /credit-autocancel-service

COPY . ${APP_DIR}
WORKDIR ${APP_DIR}

ARG GITHUB_TOKEN
ENV GOPRIVATE=github.com/hellofresh

RUN git config --global url."https://${GITHUB_TOKEN}:<EMAIL>/hellofresh".insteadOf "https://github.com/hellofresh"

RUN go install -mod=mod github.com/githubnemo/CompileDaemon

CMD $GOPATH/bin/CompileDaemon -build="make build" -command="dist/credit-autocancel-service" -exclude-dir="internal/migrations" -verbose
