# Contributing

## Prerequisites

Before setting up the service locally, ensure you have the following tools
installed:

- **<PERSON><PERSON> and <PERSON>er Compose**: For running containerized services
- **Go 1.23+**: For building and running the service directly
- **Development Tools**:
  - `golangci-lint`: For Go code linting

## API Documentation

The service API is documented using OpenAPI 3.1.0 specification located at `./docs/openapi.json`.

**Important**: When making any changes to the API endpoints, request/response payloads, or adding new endpoints in the Go code, make sure to update the OpenAPI specification accordingly. This ensures that:

- API documentation remains accurate and up-to-date
- Client code generation tools produce correct code
- API testing and validation tools work correctly

The OpenAPI specification is particularly important for documenting:
- Available endpoints and operations
- Request parameters and body schemas
- Response schemas and status codes
- Data types and validation rules

## Service's local architecture

`cacs` consists of three main components that work together to manage the
autocancel workflow:

- **API**: Provides endpoints for creating and managing autocancel campaigns (port 9090)
- **Job**: Scans the database for orders that need to be canceled (port 9091)
- **Benthos**: Processes Kafka events and handles event-driven cancellation logic (port 9092)

```mermaid
graph LR
   API[Credit Autocancel Service API<br>port: 9090]
   JOB[Credit Autocancel Service Job<br>port: 9091]
   PG[PostgreSQL<br>port: 5432]
   RMQ[RabbitMQ<br>ports: 5672/15672]
   OW[Outbox Worker]
   MIG[Database Migrations]
   JGR[Jaeger<br>port: 16686]

   BEN[Credit Autocancel Service Benthos<br>port: 9092]
   KFK[Kafka<br>port: 9093]

   API --> JGR
   API --> PG

   JOB --> PG
   JOB --> JGR

   BEN --> JGR
   BEN --> PG
   BEN --> KFK

   MIG --> PG
   PG --> OW
   OW --> RMQ
```

relying on a few infrastructure components:

### Data Stores
- **PostgreSQL** (port 5432): Primary database used for storing campaign data and events
  - Database: `creditautocanceldb`
  - Username: `credit-autocancel-service`
  - Password: `freshforyou`
  - Data persisted in named volume: `postgres-data`

See [database diagram](./docs/database-schema-diagram.png).

### Message Brokers
- **RabbitMQ** (ports 5672, 15672):
  - Management UI: [http://localhost:15672](http://localhost:15672)
  - Username: `credit-autocancel-service`
  - Password: `credit-autocancel-service`
  - Data persisted in named volume: `rabbit-data`

- **Kafka** (ports 9093, 29092) (benthos profile):
  - Bootstrap server: `localhost:9093` (from host)
  - Bootstrap server: `kafka:29092` (from containers)
  - Data persisted in named volume: `kafka-data`

### Observability
- **Jaeger** (port 16686):
  - UI: [http://localhost:16686](http://localhost:16686)
  - Collector endpoints:
    - OTLP: port 4317

### Support Services

- **Outbox Worker**: Ensures reliable message delivery from database to message brokers
- **Database Migrations**: Automatically applies schema changes on startup

## Running the Service

You can run the service components via Docker Compose using profiles or by building
and running the binaries directly.

1. **Start the default services**:

   ```bash
   docker compose up -d
   ```

   This will start:
   - PostgreSQL database (port 5432)
   - RabbitMQ (ports 5672, 15672)
   - Credit Autocancel Service API (port 9090)
   - Credit Autocancel Service Job (port 9091)
   - Database migrations
   - Outbox Worker
   - Jaeger (for tracing, port 16686)

2. **Start with Benthos services**:
   ```bash
   docker compose --profile benthos up -d
   ```

   This adds:
   - Kafka broker (port 9093)
   - Credit Autocancel Service Benthos (port 9092)

### Building and Running Directly

1. **Build all service components**:
   ```bash
   make build
   ```

2. **Run the API service**:
   ```bash
   export $(grep -v '^#' .env | xargs)
   go run cmd/credit-autocancel-service-api/main.go
   ```

3. **Run the Job service**:
   ```bash
   export $(grep -v '^#' .env | xargs)
   go run cmd/credit-autocancel-service-job/main.go
   ```

4. **Run the Benthos service**:
   ```bash
   export $(grep -v '^#' .env | xargs)
   go run cmd/credit-autocancel-service-benthos/main.go
   ```

To monitor service operations, you can view the logs for each component:

- **API Logs**: `docker compose logs -f credit-autocancel-service-api`
- **Job Logs**: `docker compose logs -f credit-autocancel-service-job`
- **Benthos Logs**: `docker compose logs -f credit-autocancel-service-benthos`

## Database Management

### Decoding Events

For debugging purposes, you can decode events for specific customer plans:

1. Create a CSV file with customer plan IDs and place it in the `cmd/credit-autocancel-service-event-decoder` directory
2. Run the decoder:
   ```bash
   make decode-events SQL_DSN="postgres://credit-autocancel-service:freshforyou@localhost:5432/creditautocanceldb?sslmode=disable"
   ```

## Testing

The service includes unit tests and integration tests to ensure functionality.

### Automated tests

1. **Unit Tests**:
   ```bash
   make test-unit
   ```

2. **Benthos Integration Tests**:
   ```bash
   make test-benthos-integration
   ```

   This will automatically spin up required Docker containers for testing.

### End-to-end

For more details on how to test in `live` or `staging`, check out [Credit AutoCancel Service API Documentation on Confluence](https://hellofresh.atlassian.net/wiki/spaces/TTH/pages/5148770403/Credit+AutoCancel+Service+API#Testing).
